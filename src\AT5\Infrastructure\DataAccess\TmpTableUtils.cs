﻿namespace AT.Infrastructure.DataAccess;

using System.Data;
using System.Linq.Expressions;
using AT.PrimitivesAT5.Ids.Interfaces;
using LinqKit;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

public static class TmpTableUtils
{
    private const int defaultJoinThreshold = 10_000;

    private static ulong s_tmpTableCounter = 0;

    public static IQueryable<TEntity> SetFast<TEntity, TId>(
        this DbContext dbContext,
        Expression<Func<TEntity, TId>> keySelector,
        IReadOnlyCollection<TId> ids,
        int joinThreshold = defaultJoinThreshold
    )
        where TEntity : class
        where TId : IId
    {
        if (ids.Count <= joinThreshold)
        {
            return dbContext.Set<TEntity>().AsExpandableEFCore().Where(x => ids.Contains(keySelector.Compile()(x)));
        }

        return SetByJoin(dbContext, keySelector, ids);
    }

    public static Task<IQueryable<TEntity>> SetFastAsync<TEntity, TId>(
        this DbContext dbContext,
        Expression<Func<TEntity, TId>> keySelector,
        IReadOnlyCollection<TId> ids,
        int joinThreshold = defaultJoinThreshold
    )
        where TEntity : class
        where TId : IId
    {
        if (ids.Count <= joinThreshold)
        {
            return Task.FromResult(
                dbContext.Set<TEntity>().AsExpandableEFCore().Where(x => ids.Contains(keySelector.Compile()(x)))
            );
        }

        return SetByJoinAsync(dbContext, keySelector, ids);
    }

    public static IQueryable<TEntity> SetByJoin<TEntity, TId>(
        this DbContext dbContext,
        Expression<Func<TEntity, TId>> keySelector,
        IReadOnlyCollection<TId> ids
    )
        where TEntity : class
        where TId : IId
    {
        var connection = (SqlConnection)dbContext.Database.GetDbConnection();
        EnsureSqlConnectionOpen(connection);

        var (fullTableName, columnName) = GetJoinColumnInfo(dbContext, keySelector);

        var tmpTableName = CreateAndFillTmpTable(connection, ids.Select(x => x.Id).Distinct());

        var sql = $"SELECT e.* FROM {fullTableName} e JOIN {tmpTableName} t ON e.[{columnName}] = t.[Id]";

        var result = dbContext.Set<TEntity>().FromSqlRaw(sql);
        return result;
    }

    public static async Task<IQueryable<TEntity>> SetByJoinAsync<TEntity, TId>(
        this DbContext dbContext,
        Expression<Func<TEntity, TId>> keySelector,
        IReadOnlyCollection<TId> ids
    )
        where TEntity : class
        where TId : IId
    {
        var connection = (SqlConnection)dbContext.Database.GetDbConnection();
        await EnsureSqlConnectionOpenAsync(connection);

        var (fullTableName, columnName) = GetJoinColumnInfo(dbContext, keySelector);

        var tmpTableName = await CreateAndFillTmpTableAsync(connection, ids.Select(x => x.Id).Distinct());

        var sql = $"SELECT e.* FROM {fullTableName} e JOIN {tmpTableName} t ON e.[{columnName}] = t.[Id]";

        var result = dbContext.Set<TEntity>().FromSqlRaw(sql);
        return result;
    }

    private static (string fullTableName, string columnName) GetJoinColumnInfo<TEntity, TId>(
        DbContext dbContext,
        Expression<Func<TEntity, TId>> keySelector
    )
        where TEntity : class
        where TId : IId
    {
        var entityType = typeof(TEntity);
        var modelEntityType =
            dbContext.Model.FindEntityType(entityType)
            ?? throw new InvalidOperationException($"Entity '{entityType.Name}' not found in the model.");

        // Consider caching table names.
        var tableName =
            modelEntityType.GetTableName()
            ?? throw new InvalidOperationException($"Entity '{entityType.Name}' doesn't have a table name.");
        var schema = modelEntityType.GetSchema();
        var fullTableName = string.IsNullOrEmpty(schema) ? tableName : $"{schema}.{tableName}";

        if (keySelector.Body is not MemberExpression memberExpression)
        {
            // Consider adding support for complex types. As of now I don't think there is a use case for that.
            throw new ArgumentException($"Only simple property expressions are supported.", nameof(keySelector));
        }

        var propertyName = memberExpression.Member.Name;

        var property =
            modelEntityType.FindProperty(propertyName)
            ?? throw new InvalidOperationException($"Property '{propertyName}' not found in the entity type.");

        // Consider caching column names.
        var columnName =
            property.GetColumnName(StoreObjectIdentifier.Table(tableName, schema))
            ?? throw new InvalidOperationException($"Property '{propertyName}' doesn't have a column name.");

        return (fullTableName, columnName);
    }

    private static void EnsureSqlConnectionOpen(SqlConnection sqlConnection)
    {
        var isOpen = sqlConnection.State == ConnectionState.Open;
        if (!isOpen)
        {
            sqlConnection.Open();
        }
    }

    private static async Task EnsureSqlConnectionOpenAsync(SqlConnection sqlConnection)
    {
        var isOpen = sqlConnection.State == ConnectionState.Open;
        if (!isOpen)
        {
            await sqlConnection.OpenAsync();
        }
    }

    private static string CreateAndFillTmpTable(SqlConnection sqlConnection, IEnumerable<int> ids)
    {
        var tmpTableName = CreateTmpTable(sqlConnection);
        FillTmpTable(sqlConnection, tmpTableName, ids);
        return tmpTableName;
    }

    private static async Task<string> CreateAndFillTmpTableAsync(SqlConnection sqlConnection, IEnumerable<int> ids)
    {
        var tmpTableName = await CreateTmpTableAsync(sqlConnection);
        await FillTmpTableAsync(sqlConnection, tmpTableName, ids);
        return tmpTableName;
    }

    private static string CreateTmpTable(SqlConnection sqlConnection)
    {
        var tmpTableName = GenerateTmpTableName();
        using var tmpTableCreateCommand = CreateCreateTableCommand(sqlConnection, tmpTableName);
        tmpTableCreateCommand.ExecuteNonQuery();
        return tmpTableName;
    }

    private static async Task<string> CreateTmpTableAsync(SqlConnection sqlConnection)
    {
        var tmpTableName = GenerateTmpTableName();
        using var tmpTableCreateCommand = CreateCreateTableCommand(sqlConnection, tmpTableName);
        await tmpTableCreateCommand.ExecuteNonQueryAsync();
        return tmpTableName;
    }

    private static SqlCommand CreateCreateTableCommand(SqlConnection sqlConnection, string tableName)
    {
        var tableCreateCommand = sqlConnection.CreateCommand();
        tableCreateCommand.CommandText = $"CREATE TABLE {tableName} (Id INT NOT NULL);";
        return tableCreateCommand;
    }

    private static string GenerateTmpTableName()
    {
        var current = Interlocked.Increment(ref s_tmpTableCounter);
        return $"#TmpIds_{current}";
    }

    private static void FillTmpTable(SqlConnection sqlConnection, string tmpTableName, IEnumerable<int> ids)
    {
        using var bulkCopy = PrepareBulkCopy(sqlConnection, tmpTableName);
        var table = PrepareDataTable(ids);
        bulkCopy.WriteToServer(table);
    }

    private static async Task FillTmpTableAsync(SqlConnection sqlConnection, string tmpTableName, IEnumerable<int> ids)
    {
        using var bulkCopy = PrepareBulkCopy(sqlConnection, tmpTableName);
        var table = PrepareDataTable(ids);
        await bulkCopy.WriteToServerAsync(table);
    }

    private static SqlBulkCopy PrepareBulkCopy(SqlConnection sqlConnection, string tmpTableName)
    {
        var bulkCopy = new SqlBulkCopy(sqlConnection) { DestinationTableName = tmpTableName, };

        return bulkCopy;
    }

    private static DataTable PrepareDataTable(IEnumerable<int> ids)
    {
        var table = new DataTable();
        table.Columns.Add("Id", typeof(int));
        foreach (var id in ids)
        {
            table.Rows.Add(id);
        }

        return table;
    }
}
