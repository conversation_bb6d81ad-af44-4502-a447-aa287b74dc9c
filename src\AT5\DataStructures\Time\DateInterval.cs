﻿namespace AT.DataStructures.Time;

using System.Text.Json.Serialization;

public readonly partial record struct DateInterval
{
    /// <summary>
    /// Gets the duration of the interval.
    /// </summary>
    public TimeSpan Duration => IsEmpty ? TimeSpan.Zero : TimeSpan.FromDays(End.DayNumber - Start.DayNumber + 1);

    /// <summary>
    /// Implicitly converts a <see cref="DateInterval"/> to an <see cref="OpenDateInterval"/> using <see cref="ToOpenDateInterval"/>.
    /// </summary>
    /// <param name="interval">The <see cref="DateInterval"/> to convert.</param>
    public static implicit operator OpenDateInterval(DateInterval interval)
    {
        return interval.ToOpenDateInterval();
    }

    /// <summary>
    /// Converts a <see cref="DateInterval"/> to an <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <returns>An <see cref="OpenDateInterval"/> representing the same interval.</returns>
    public OpenDateInterval ToOpenDateInterval()
    {
        return new OpenDateInterval(Start, End);
    }

    /// <summary>
    /// Implicitly converts a <see cref="DateInterval"/> to an <see cref="DateTimeInterval"/> using <see cref="ToDateTimeInterval"/>.
    /// </summary>
    /// <param name="interval">The <see cref="DateInterval"/> to convert.</param>
    public static implicit operator DateTimeInterval(DateInterval interval)
    {
        return interval.ToDateTimeInterval();
    }

    /// <summary>
    /// Converts a <see cref="DateInterval"/> to an <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <returns>An <see cref="DateTimeInterval"/> representing the same interval.</returns>
    public DateTimeInterval ToDateTimeInterval()
    {
        return new DateTimeInterval(Start.ToDateTime(TimeOnly.MinValue), End.ToDateTime(TimeOnly.MaxValue).AddTicks(1));
    }

    /// <summary>
    /// Implicitly converts a <see cref="DateInterval"/> to an <see cref="OpenDateTimeInterval"/> using <see cref="ToOpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="interval">The <see cref="DateInterval"/> to convert.</param>
    public static implicit operator OpenDateTimeInterval(DateInterval interval)
    {
        return interval.ToOpenDateTimeInterval();
    }

    /// <summary>
    /// Converts a <see cref="DateInterval"/> to an <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <returns>An <see cref="OpenDateTimeInterval"/> representing the same interval.</returns>
    public OpenDateTimeInterval ToOpenDateTimeInterval()
    {
        return new OpenDateTimeInterval(
            Start.ToDateTime(TimeOnly.MinValue),
            End.ToDateTime(TimeOnly.MaxValue).AddTicks(1)
        );
    }
}
