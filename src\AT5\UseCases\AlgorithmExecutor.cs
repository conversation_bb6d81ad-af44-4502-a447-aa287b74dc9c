namespace AT.UseCases;

using AT.Utilities.Logging;
using Core.Domain.ExampleEntities;
using Core.Repositories.Example;
using Core.Services;
using Core.Services.Algorithm;
using EndTime = Core.Domain.ExampleEntities.EndTime;
using StartTime = Core.Domain.ExampleEntities.StartTime;
using UserName = Core.Domain.ExampleEntities.UserName;

public class AlgorithmExecutor(
    IAlgorithm _algorithm,
    IAlgorithmLogRepository _repository,
    INotifier _notifier,
    TimeProvider _timeProvider,
    ILogger<AlgorithmExecutor> _logger
) : IAlgorithmExecutor
{
    public async Task ExecuteAlgorithmAsync(UserName user, AlgorithmInput input, CancellationToken token = default)
    {
        using var _ = _logger.BeginScopeWithProperties(("CorrelationId", Guid.NewGuid()));

        _logger.Info("Executing algorithm for user {User} on {Input}", user, input);

        var start = StartTime.From(_timeProvider.GetLocalNow());

        var output = await _algorithm.PerformAsync(input);

        var end = EndTime.From(_timeProvider.GetLocalNow());

        var log = new AlgorithmLog(user, start, end, input, output);

        await _repository.AddLogAsync(log, token);

        var message = NotificationMessage.From($"Algorithm took {(end.Value - start.Value).TotalSeconds:F1} seconds");

        await _notifier.SendNotificationAsync(user, message);
    }
}
