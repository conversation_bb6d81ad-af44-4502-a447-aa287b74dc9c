﻿namespace AT.Primitives.Enums;

public enum RequestStatus
{
    None = 0,
    Pending = 1,
    Approved = 2,
    Rejected = 3,
    Auto = 4,
    Cancelled = 5,
    PreApproved = 6,
    PreRejected = 7,
    PreAuto = 8,
}

public static class RequestStatusExtensions
{
    public static bool IsProcessed(this RequestStatus requestStatus)
    {
        return requestStatus
            is RequestStatus.Approved
                or RequestStatus.Rejected
                or RequestStatus.Auto
                or RequestStatus.Cancelled;
    }

    public static bool IsPreProcessedOrPending(this RequestStatus requestStatus)
    {
        return requestStatus
            is RequestStatus.Pending
                or RequestStatus.PreApproved
                or RequestStatus.PreAuto
                or RequestStatus.PreRejected;
    }
}
