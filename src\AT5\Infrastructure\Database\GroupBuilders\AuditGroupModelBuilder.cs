﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="AuditEntity"/> in the database.
/// </summary>
internal static class AuditGroupModelBuilder
{
    public static void BuildAuditGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<AuditDelete>(entity =>
        {
            entity.HasKey(e => e.Id);

            entity.ToTable("AuditEntities_AuditDelete");

            entity
                .HasOne(d => d.AuditEntity)
                .WithOne()
                .HasForeignKey<AuditDelete>(d => d.Id)
                .HasConstraintName("FK_AuditDelete_inherits_AuditEntity");
        });

        modelBuilder.Entity<AuditInsert>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("AuditEntities_AuditInsert");

            entity
                .HasOne(d => d.AuditEntity)
                .WithOne()
                .HasForeignKey<AuditInsert>(d => d.Id)
                .HasConstraintName("FK_AuditInsert_inherits_AuditEntity");
        });

        modelBuilder.Entity<AuditUpdate>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.ToTable("AuditEntities_AuditUpdate");

            entity
                .HasOne(d => d.AuditEntity)
                .WithOne()
                .HasForeignKey<AuditUpdate>(d => d.Id)
                .HasConstraintName("FK_AuditUpdate_inherits_AuditEntity");
        });

        modelBuilder.Entity<AuditEntity>(entity =>
        {
            entity.ToTable("AuditEntities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.TimeStamp).IsStoredAsDateTime();
        });

        modelBuilder.Entity<AuditInsertProperty>(entity =>
        {
            entity.ToTable("AuditInsertProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.AuditInsertId, "IX_FK_AuditInsertAuditInsertProperty");

            entity
                .HasOne(d => d.AuditInsert)
                .WithMany(p => p.AuditInsertProperties)
                .HasForeignKey(d => d.AuditInsertId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AuditInsertAuditInsertProperty");
        });

        modelBuilder.Entity<AuditRelationship>(entity =>
        {
            entity.ToTable("AuditRelationships");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.TimeStamp).IsStoredAsDateTime();
        });

        modelBuilder.Entity<AuditUpdateProperty>(entity =>
        {
            entity.ToTable("AuditUpdateProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.AuditUpdateId, "IX_FK_AuditUpdateAuditUpdateProperty");

            entity
                .HasOne(d => d.AuditUpdate)
                .WithMany(p => p.AuditUpdateProperties)
                .HasForeignKey(d => d.AuditUpdateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AuditUpdateAuditUpdateProperty");
        });
    }
}
