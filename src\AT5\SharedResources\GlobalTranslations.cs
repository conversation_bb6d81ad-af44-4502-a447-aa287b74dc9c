﻿namespace AT.SharedResources;

using AT.DataStructures.Time;
using AT.Translations;
using AT.Translations.Formatting.Numbers;
using AT.Translations.Formatting.Time;

[ResxSource(outputFileName: "Global")]
public static class GlobalTranslations
{
    /// <summary>
    /// Yes
    /// </summary>
    public record Yes : ITranslationNoParameters;

    /// <summary>
    /// No
    /// </summary>
    public record No : ITranslationNoParameters;

    /// <summary>
    /// Operation {OperationName} succeeded.
    /// </summary>
    // FUTURE: Remove this. This serves only as an example.
    public record OperationSuccessful(string OperationName) : ITranslationWithParameters;

    /// <summary>
    /// Value {Value} is too {Value:cond:<=3?low|high}.
    /// </summary>
    // FUTURE: Remove this. This serves only as an example.
    public record ValueIsTooExtreme([property: DoubleFormatting(maxDecimalPlaces: MaxDecimalPlaces.Three)] double Value)
        : ITranslationWithParameters;

    /// <summary>
    /// Interval {Interval} is invalid.
    /// </summary>
    // FUTURE: Remove this. This serves only as an example.
    public record IntervalIsInvalid(
        [property: DateIntervalFormatting(DateIntervalFormattingType.Short)] DateInterval Interval
    ) : ITranslationWithParameters;
}
