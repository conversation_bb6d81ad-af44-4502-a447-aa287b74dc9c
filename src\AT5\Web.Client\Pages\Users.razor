﻿@page "/users"

@using AT.ApiClient
@using AT.Shared.Models.Demo

@inject ApiClient ApiClient

<PageTitle>Users</PageTitle>

<h1>Users</h1>

@if (name == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <p>From server: @name and @userId</p>
}

@code {
    private string? name;
    private UserId? userId;

    protected override async Task OnInitializedAsync()
    {
        var response = await ApiClient.ATApiServiceEndpointsUsersGetUserEndpointAsync("4")!;
        name = response.Name;
        userId = response.UserId;
    }
}
