﻿namespace AT.Infrastructure.Events;

using AT.Core.MasterDomain;
using AT.Infrastructure.Events.Events;
using AT.Infrastructure.Events.Subscribers;
using AT.Primitives.Constants;
using AT.Primitives.Identifiers;

public class DataChangeEventsService : IDataChangeEventsPublisher, IDataChangeEventsSubscriber
{
    private readonly AppInstanceId _appId;
    private readonly IEventsPublisher _eventsPublisher;
    private readonly IEventsSubscriber _eventsSubscriber;
    private readonly IDataChangeSubscribersService _subscribersService;
    private readonly string _channel;

    public DataChangeEventsService(
        AppInstanceId appId,
        IEventsPublisher eventsPublisher,
        IEventsSubscriber eventsSubscriber,
        IDataChangeSubscribersService subscribersService,
        FullOrgId fullOrganizationId
    )
    {
        _appId = appId;
        _eventsPublisher = eventsPublisher;
        _eventsSubscriber = eventsSubscriber;
        _subscribersService = subscribersService;
        _channel = RedisChannels.GetDataChangedChannelId(fullOrganizationId.Id.Value);

        SubscribeToAsyncMessageService();
    }

    private void SubscribeToAsyncMessageService()
    {
        _eventsSubscriber.Subscribe<DataChangeWrapperMessage>(_channel, HandleAsyncMessage);
    }

    private void HandleAsyncMessage(EventContext<DataChangeWrapperMessage> context)
    {
        var payload = context.Value;
        if (payload.SourceAppId == _appId.Id)
        {
            // We don't handle messages from the current application. We expect them to be delivered internally.
            return;
        }

        var message = payload.Message;
        if (message is null)
        {
            return;
        }

        InvokeSubscribers(message);
    }

    private void InvokeSubscribers(DataChangeEvent message)
    {
        foreach (var subscriber in _subscribersService.GetSubscribers().Where(s => s.CanHandle(message)))
        {
            subscriber.Handle(message);
        }
    }

    public void Publish(DataChangeEvent rosterDataChangeMessage)
    {
        var wrapperMessage = new DataChangeWrapperMessage()
        {
            SourceAppId = _appId.Id,
            Message = rosterDataChangeMessage,
        };

        _eventsPublisher.Publish(_channel, wrapperMessage);
    }

    public Guid Subscribe<TMessage>(Action<TMessage> handler)
        where TMessage : DataChangeEvent
    {
        return _subscribersService.AddSubscriber(handler);
    }

    private sealed class DataChangeWrapperMessage
    {
        public Guid SourceAppId { get; init; }

        public DataChangeEvent? Message { get; init; }
    }
}
