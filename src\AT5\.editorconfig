# You can learn more about editorconfig here: https://docs.microsoft.com/en-us/visualstudio/ide/editorconfig-code-style-settings-reference
[*.cs]

csharp_using_directive_placement= inside_namespace:error
csharp_style_namespace_declarations = file_scoped:error

dotnet_sort_system_directives_first = true:error
dotnet_style_qualification_for_field = false:error
dotnet_style_qualification_for_property = false:error
dotnet_style_qualification_for_method = false:error
dotnet_style_qualification_for_event = false:error




dotnet_naming_rule.private_const_should_be_camel.severity = warning
dotnet_naming_rule.private_const_should_be_camel.symbols = private_const
dotnet_naming_rule.private_const_should_be_camel.style = camel_case

dotnet_naming_symbols.private_const.applicable_kinds = field
dotnet_naming_symbols.private_const.applicable_accessibilities = private
dotnet_naming_symbols.private_const.required_modifiers = const

dotnet_naming_style.camel_case.capitalization = camel_case




dotnet_naming_rule.private_static_should_be_s_camel.severity = warning
dotnet_naming_rule.private_static_should_be_s_camel.symbols = private_static
dotnet_naming_rule.private_static_should_be_s_camel.style = s_camel_case

dotnet_naming_symbols.private_static.applicable_kinds = field
dotnet_naming_symbols.private_static.applicable_accessibilities = private
dotnet_naming_symbols.private_static.required_modifiers = static

dotnet_naming_style.s_camel_case.capitalization = camel_case
dotnet_naming_style.s_camel_case.required_prefix = s_




dotnet_naming_rule.private_fields_should_be__camel.severity = warning
dotnet_naming_rule.private_fields_should_be__camel.symbols = private_fields
dotnet_naming_rule.private_fields_should_be__camel.style = _camel_case

dotnet_naming_symbols.private_fields.applicable_kinds = field
dotnet_naming_symbols.private_fields.applicable_accessibilities = private
dotnet_naming_symbols.private_fields.required_modifiers = 

dotnet_naming_style._camel_case.capitalization = camel_case
dotnet_naming_style._camel_case.required_prefix = _