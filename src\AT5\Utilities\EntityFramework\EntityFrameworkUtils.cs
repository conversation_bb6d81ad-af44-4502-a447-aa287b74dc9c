﻿namespace AT.Utilities.EntityFramework;

public static class EntityFrameworkUtils
{
    public const string DefaultServerConnectionString =
        "Data Source=.\\;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;";

    /// <param name="serverConnectionString">A connection string to the server (that is without any 'Initial Catalog=XY' part).</param>
    /// <param name="database">The name of the database.</param>
    public static string CreateConnectionStringForDatabase(string serverConnectionString, string database)
    {
        return string.Format("Initial Catalog={0};", database) + serverConnectionString;
    }
}
