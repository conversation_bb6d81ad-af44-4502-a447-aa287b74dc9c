﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class Job
{
    public JobId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string Parameters { get; set; } = null!;

    public JobType Type { get; set; }

    public bool Disabled { get; set; }

    public string? SuccessEmails { get; set; }

    public string? ErrorEmails { get; set; }

    public virtual ICollection<JobTrigger> JobTriggers { get; set; } = new List<JobTrigger>();
}
