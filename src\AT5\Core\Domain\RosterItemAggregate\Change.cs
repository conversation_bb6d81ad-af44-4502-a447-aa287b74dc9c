﻿namespace AT.Core.Domain.RosterItemAggregate;

public class Change
{
    public ChangeId Id { get; set; }

    public string Type { get; set; } = null!;

    public string TypeData { get; set; } = null!;

    public ChangeLogId? ChangeLogId { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public ChangeRequestId? ChangeRequestId { get; set; }

    public CalculationId? CalculationId { get; set; }

    public DateTimeInterval AffectedInterval { get; set; }

    public UserId NewEmployeeId { get; set; }

    public UserId OldEmployeeId { get; set; }

    public virtual Calculation? Calculation { get; set; }

    public virtual ChangeLog? ChangeLog { get; set; }

    public virtual ChangeRequest? ChangeRequest { get; set; }

    public virtual Employee NewEmployee { get; set; } = null!;

    public virtual Employee OldEmployee { get; set; } = null!;

    public virtual RosterItem RosterItem { get; set; } = null!;
}
