﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Core.Domain.UserAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Site"/> in the database.
/// </summary>
internal static class SiteGroupModelBuilder
{
    public static void BuildSiteGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Location>(entity =>
        {
            entity.ToTable("Locations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Abbreviation).HasMaxLength(10);
            entity.Property(e => e.Name).HasMaxLength(255);

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.Locations)
                .UsingEntity<Dictionary<string, object>>(
                    "LocationSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_LocationSite_Site"),
                    l =>
                        l.HasOne<Location>()
                            .WithMany()
                            .HasForeignKey("LocationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_LocationSite_Location"),
                    j =>
                    {
                        j.HasKey("LocationsId", "SitesId");
                        j.ToTable("LocationSite");
                        j.HasIndex(["SitesId"], "IX_FK_LocationSite_Site");
                        j.IndexerProperty<LocationId>("LocationsId").HasColumnName("Locations_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<Requirement>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Requirements");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.RosterItemPartTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RosterItemPartTypeId, "IX_FK_RosterItemPartTypeRequirement");

            entity.Property(e => e.SkillId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SkillId, "IX_FK_SkillRequirement");

            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasTimeInterval(e => e.TimeInterval, iota);

            entity.Property(e => e.Minimum).HasColumnOrder(iota);
            entity.Property(e => e.Maximum).HasColumnOrder(iota);
            entity.Property(e => e.PrioritySteps).HasColumnOrder(iota);

            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.ReservationsAllowed).HasColumnOrder(iota);
            entity.Property(e => e.AllowedShiftExtension).HasColumnOrder(iota);
            entity.Property(e => e.AllowedShiftShortening).HasColumnOrder(iota);
            entity.Property(e => e.FillPriority).HasColumnOrder(iota);
            entity.Property(e => e.Type).HasColumnOrder(iota);
            entity.Property(e => e.Parameters).HasMaxLength(4000).HasColumnOrder(iota);

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_RequirementOwnerSite"); // This index will be dropped and replaced by an additional index.

            entity.HasOpenDateInterval(e => e.TotalInterval, iota);

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.Requirements)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemPartTypeRequirement");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.Requirements)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequirementOwnerSite");

            entity
                .HasOne(d => d.Skill)
                .WithMany(p => p.Requirements)
                .HasForeignKey(d => d.SkillId)
                .HasConstraintName("FK_SkillRequirement");

            entity
                .HasMany(d => d.AllowedEmployees)
                .WithMany(p => p.Requirements)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementEmployee",
                    r =>
                        r.HasOne<Employee>()
                            .WithMany()
                            .HasForeignKey("AllowedEmployeesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementEmployee_Employee"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementEmployeeEmployeeId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementEmployee_Requirement"),
                    j =>
                    {
                        j.HasKey("RequirementEmployeeEmployeeId", "AllowedEmployeesId");
                        j.ToTable("RequirementEmployee");
                        j.HasIndex(["AllowedEmployeesId"], "IX_FK_RequirementEmployee_Employee");
                        j.IndexerProperty<RequirementId>("RequirementEmployeeEmployeeId")
                            .HasColumnName("RequirementEmployee_Employee_Id");
                        j.IndexerProperty<UserId>("AllowedEmployeesId").HasColumnName("AllowedEmployees_Id");
                    }
                );

            entity
                .HasMany(d => d.AllowedTeams)
                .WithMany(p => p.RequirementTeamTeams)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementTeam",
                    r =>
                        r.HasOne<Team>()
                            .WithMany()
                            .HasForeignKey("AllowedTeamsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementTeam_Team"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementTeamTeamId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementTeam_Requirement"),
                    j =>
                    {
                        j.HasKey("RequirementTeamTeamId", "AllowedTeamsId");
                        j.ToTable("RequirementTeam");
                        j.HasIndex(["AllowedTeamsId"], "IX_FK_RequirementTeam_Team");
                        j.IndexerProperty<RequirementId>("RequirementTeamTeamId")
                            .HasColumnName("RequirementTeam_Team_Id");
                        j.IndexerProperty<TeamId>("AllowedTeamsId").HasColumnName("AllowedTeams_Id");
                    }
                );

            entity
                .HasMany(d => d.Locations)
                .WithMany(p => p.Requirements)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementLocation",
                    r =>
                        r.HasOne<Location>()
                            .WithMany()
                            .HasForeignKey("LocationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementLocation_Location"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementLocation_Requirement"),
                    j =>
                    {
                        j.HasKey("RequirementsId", "LocationsId");
                        j.ToTable("RequirementLocation");
                        j.HasIndex(["LocationsId"], "IX_FK_RequirementLocation_Location");
                        j.IndexerProperty<RequirementId>("RequirementsId").HasColumnName("Requirements_Id");
                        j.IndexerProperty<LocationId>("LocationsId").HasColumnName("Locations_Id");
                    }
                );

            entity
                .HasMany(d => d.SatisfactoryRosterItemPartTypes)
                .WithMany(p => p.RequirementSatisfactoryRosterItemPartTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementSatisfactoryRosterItemPartType",
                    r =>
                        r.HasOne<RosterItemPartType>()
                            .WithMany()
                            .HasForeignKey("SatisfactoryRosterItemPartTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementSatisfactoryRosterItemPartType_RosterItemPartType"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementSatisfactoryRosterItemPartTypeRosterItemPartTypeId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementSatisfactoryRosterItemPartType_Requirement"),
                    j =>
                    {
                        j.HasKey(
                            "RequirementSatisfactoryRosterItemPartTypeRosterItemPartTypeId",
                            "SatisfactoryRosterItemPartTypesId"
                        );
                        j.ToTable("RequirementSatisfactoryRosterItemPartType");
                        j.HasIndex(
                            ["SatisfactoryRosterItemPartTypesId"],
                            "IX_FK_RequirementSatisfactoryRosterItemPartType_RosterItemPartType"
                        );
                        j.IndexerProperty<RequirementId>(
                                "RequirementSatisfactoryRosterItemPartTypeRosterItemPartTypeId"
                            )
                            .HasColumnName("RequirementSatisfactoryRosterItemPartType_RosterItemPartType_Id");
                        j.IndexerProperty<RosterItemPartTypeId>("SatisfactoryRosterItemPartTypesId")
                            .HasColumnName("SatisfactoryRosterItemPartTypes_Id");
                    }
                );

            entity
                .HasMany(d => d.SatisfactoryShiftTemplates)
                .WithMany(p => p.RequirementsToSatisfies)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementSatisfactoryShiftTemplate",
                    r =>
                        r.HasOne<ShiftTemplate>()
                            .WithMany()
                            .HasForeignKey("SatisfactoryShiftTemplatesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementSatisfactoryShiftTemplate_ShiftTemplate"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementsToSatisfyId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementSatisfactoryShiftTemplate_Requirement"),
                    j =>
                    {
                        j.HasKey("RequirementsToSatisfyId", "SatisfactoryShiftTemplatesId");
                        j.ToTable("RequirementSatisfactoryShiftTemplate");
                        j.HasIndex(
                            ["SatisfactoryShiftTemplatesId"],
                            "IX_FK_RequirementSatisfactoryShiftTemplate_ShiftTemplate"
                        );
                        j.IndexerProperty<RequirementId>("RequirementsToSatisfyId")
                            .HasColumnName("RequirementsToSatisfy_Id");
                        j.IndexerProperty<ShiftTemplateId>("SatisfactoryShiftTemplatesId")
                            .HasColumnName("SatisfactoryShiftTemplates_Id");
                    }
                );

            entity
                .HasMany(d => d.ShiftTemplates)
                .WithMany(p => p.Requirements)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementShiftTemplate",
                    r =>
                        r.HasOne<ShiftTemplate>()
                            .WithMany()
                            .HasForeignKey("ShiftTemplatesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementShiftTemplate_ShiftTemplate"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementShiftTemplate_Requirement"),
                    j =>
                    {
                        j.HasKey("RequirementsId", "ShiftTemplatesId");
                        j.ToTable("RequirementShiftTemplate");
                        j.HasIndex(["ShiftTemplatesId"], "IX_FK_RequirementShiftTemplate_ShiftTemplate");
                        j.IndexerProperty<RequirementId>("RequirementsId").HasColumnName("Requirements_Id");
                        j.IndexerProperty<ShiftTemplateId>("ShiftTemplatesId").HasColumnName("ShiftTemplates_Id");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.RequirementSites)
                .UsingEntity<Dictionary<string, object>>(
                    "RequirementSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementSite_Site"),
                    l =>
                        l.HasOne<Requirement>()
                            .WithMany()
                            .HasForeignKey("RequirementSiteSiteId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequirementSite_Requirement"),
                    j =>
                    {
                        j.HasKey("RequirementSiteSiteId", "SitesId");
                        j.ToTable("RequirementSite");
                        j.HasIndex(["SitesId"], "IX_FK_RequirementSite_Site");
                        j.IndexerProperty<RequirementId>("RequirementSiteSiteId")
                            .HasColumnName("RequirementSite_Site_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.SiteId,
                        e.TotalInterval.Start,
                        e.TotalInterval.End
                    },
                    "IX_FK_RequirementOwnerSite"
                )
                .WithSettings(
                    new
                    {
                        PAD_INDEX = "OFF",
                        STATISTICS_NORECOMPUTE = "OFF",
                        SORT_IN_TEMPDB = "OFF",
                        DROP_EXISTING = "ON",
                        ONLINE = "OFF",
                        ALLOW_ROW_LOCKS = "ON",
                        ALLOW_PAGE_LOCKS = "ON",
                        FILLFACTOR = 80,
                        OPTIMIZE_FOR_SEQUENTIAL_KEY = "OFF"
                    }
                );

            entity.HasIndex(
                context.AdditionalIndexCollection,
                e => new
                {
                    e.SiteId,
                    e.TotalInterval.Start,
                    e.TotalInterval.End
                },
                "IX_NC_Requirements_ForRG"
            );
        });

        modelBuilder.Entity<Site>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Sites");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Location).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Code).HasMaxLength(20).HasColumnOrder(iota);
            entity.Property(e => e.AllowEmployees).HasColumnOrder(iota);
            entity.Property(e => e.AllowContracts).HasColumnOrder(iota);
            entity.Property(e => e.AllowPlanningPeriods).HasColumnOrder(iota);
            entity.Property(e => e.AllowPlanning).HasColumnOrder(iota);
            entity.Property(e => e.AllowAsLocation).HasColumnOrder(iota);

            entity.Property(e => e.TypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.TypeId, "IX_FK_SiteTypeSite");

            entity.Property(e => e.CostCenter).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.ExternalId).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.AllowNeeds).HasColumnOrder(iota);

            entity.Property(e => e.SubtypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SubtypeId, "IX_FK_SiteSiteSubtype");

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.AutoGenerateShifts).HasColumnOrder(iota);

            entity.Property(e => e.BalanceConfigurationId).HasColumnOrder(iota);
            entity.HasIndex(e => e.BalanceConfigurationId, "IX_FK_BalanceConfigurationSite");

            entity
                .HasOne(d => d.BalanceConfiguration)
                .WithMany(p => p.Sites)
                .HasForeignKey(d => d.BalanceConfigurationId)
                .HasConstraintName("FK_BalanceConfigurationSite");

            entity
                .HasOne(d => d.Subtype)
                .WithMany(p => p.Sites)
                .HasForeignKey(d => d.SubtypeId)
                .HasConstraintName("FK_SiteSiteSubtype");

            entity
                .HasOne(d => d.Type)
                .WithMany(p => p.Sites)
                .HasForeignKey(d => d.TypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteTypeSite");

            entity
                .HasMany(d => d.AdditionalPermissionsSources)
                .WithMany(p => p.PermissionsDestinations)
                .UsingEntity<Dictionary<string, object>>(
                    "PermissionsTransfer",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("AdditionalPermissionsSourcesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PermissionsTransfer_Site1"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("PermissionsDestinationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PermissionsTransfer_Site"),
                    j =>
                    {
                        j.HasKey("PermissionsDestinationsId", "AdditionalPermissionsSourcesId");
                        j.ToTable("PermissionsTransfer");
                        j.HasIndex(["AdditionalPermissionsSourcesId"], "IX_FK_PermissionsTransfer_Site1");
                        j.IndexerProperty<SiteId>("PermissionsDestinationsId")
                            .HasColumnName("PermissionsDestinations_Id");
                        j.IndexerProperty<SiteId>("AdditionalPermissionsSourcesId")
                            .HasColumnName("AdditionalPermissionsSources_Id");
                    }
                );

            entity
                .HasMany(d => d.Agencies)
                .WithMany(p => p.Sites)
                .UsingEntity<Dictionary<string, object>>(
                    "SitesAgency",
                    r =>
                        r.HasOne<Agency>()
                            .WithMany()
                            .HasForeignKey("AgenciesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SitesAgencies_Agency"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SitesAgencies_Site"),
                    j =>
                    {
                        j.HasKey("SitesId", "AgenciesId");
                        j.ToTable("SitesAgencies");
                        j.HasIndex(["AgenciesId"], "IX_FK_SitesAgencies_Agency");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                        j.IndexerProperty<AgencyId>("AgenciesId").HasColumnName("Agencies_Id");
                    }
                );

            entity
                .HasMany(d => d.Contracts)
                .WithMany(p => p.Sites)
                .UsingEntity<Dictionary<string, object>>(
                    "SiteContract",
                    r =>
                        r.HasOne<Contract>()
                            .WithMany()
                            .HasForeignKey("ContractsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteContract_Contract"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteContract_Site"),
                    j =>
                    {
                        j.HasKey("SitesId", "ContractsId");
                        j.ToTable("SiteContract");
                        j.HasIndex(["ContractsId"], "IX_FK_SiteContract_Contract");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                        j.IndexerProperty<ContractId>("ContractsId").HasColumnName("Contracts_Id");
                    }
                );

            entity
                .HasMany(d => d.PermissionsDestinations)
                .WithMany(p => p.AdditionalPermissionsSources)
                .UsingEntity<Dictionary<string, object>>(
                    "PermissionsTransfer",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("PermissionsDestinationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PermissionsTransfer_Site"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("AdditionalPermissionsSourcesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PermissionsTransfer_Site1"),
                    j =>
                    {
                        j.HasKey("PermissionsDestinationsId", "AdditionalPermissionsSourcesId");
                        j.ToTable("PermissionsTransfer");
                        j.HasIndex(["AdditionalPermissionsSourcesId"], "IX_FK_PermissionsTransfer_Site1");
                        j.IndexerProperty<SiteId>("PermissionsDestinationsId")
                            .HasColumnName("PermissionsDestinations_Id");
                        j.IndexerProperty<SiteId>("AdditionalPermissionsSourcesId")
                            .HasColumnName("AdditionalPermissionsSources_Id");
                    }
                );

            entity
                .HasMany(d => d.Positions)
                .WithMany(p => p.Sites)
                .UsingEntity<Dictionary<string, object>>(
                    "SitePosition",
                    r =>
                        r.HasOne<Position>()
                            .WithMany()
                            .HasForeignKey("PositionsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SitePosition_Position"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SitePosition_Site"),
                    j =>
                    {
                        j.HasKey("SitesId", "PositionsId");
                        j.ToTable("SitePosition");
                        j.HasIndex(["PositionsId"], "IX_FK_SitePosition_Position");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                        j.IndexerProperty<PositionId>("PositionsId").HasColumnName("Positions_Id");
                    }
                );

            entity
                .HasMany(d => d.RosterItemPartTypes)
                .WithMany(p => p.Sites)
                .UsingEntity<Dictionary<string, object>>(
                    "SiteRosterItemPartType",
                    r =>
                        r.HasOne<RosterItemPartType>()
                            .WithMany()
                            .HasForeignKey("RosterItemPartTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteRosterItemPartType_RosterItemPartType"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteRosterItemPartType_Site"),
                    j =>
                    {
                        j.HasKey("SitesId", "RosterItemPartTypesId");
                        j.ToTable("SiteRosterItemPartType");
                        j.HasIndex(["RosterItemPartTypesId"], "IX_FK_SiteRosterItemPartType_RosterItemPartType");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                        j.IndexerProperty<RosterItemPartTypeId>("RosterItemPartTypesId")
                            .HasColumnName("RosterItemPartTypes_Id");
                    }
                );

            entity
                .HasMany(d => d.ShiftTemplates)
                .WithMany(p => p.Sites)
                .UsingEntity<Dictionary<string, object>>(
                    "SiteShiftTemplate",
                    r =>
                        r.HasOne<ShiftTemplate>()
                            .WithMany()
                            .HasForeignKey("ShiftTemplatesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteShiftTemplate_ShiftTemplate"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteShiftTemplate_Site"),
                    j =>
                    {
                        j.HasKey("SitesId", "ShiftTemplatesId");
                        j.ToTable("SiteShiftTemplate");
                        j.HasIndex(["ShiftTemplatesId"], "IX_FK_SiteShiftTemplate_ShiftTemplate");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                        j.IndexerProperty<ShiftTemplateId>("ShiftTemplatesId").HasColumnName("ShiftTemplates_Id");
                    }
                );

            entity
                .HasMany(d => d.SiteReportColumnSites)
                .WithMany(p => p.Sites)
                .UsingEntity<Dictionary<string, object>>(
                    "SiteReportColumn",
                    r =>
                        r.HasOne<ReportColumn>()
                            .WithMany()
                            .HasForeignKey("SiteReportColumnSiteId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteReportColumn_ReportColumn"),
                    l =>
                        l.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteReportColumn_Site"),
                    j =>
                    {
                        j.HasKey("SitesId", "SiteReportColumnSiteId");
                        j.ToTable("SiteReportColumn");
                        j.HasIndex(["SiteReportColumnSiteId"], "IX_FK_SiteReportColumn_ReportColumn");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                        j.IndexerProperty<ReportColumnId>("SiteReportColumnSiteId")
                            .HasColumnName("SiteReportColumn_Site_Id");
                    }
                );
        });

        modelBuilder.Entity<SiteProperty>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("SiteProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Value).HasMaxLength(4000).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.PropertyId).HasColumnOrder(iota);
            entity.HasIndex(e => e.PropertyId, "IX_FK_PropertySiteProperty");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_SiteSiteProperty");

            entity.Property(e => e.Source).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Property)
                .WithMany(p => p.SiteProperties)
                .HasForeignKey(d => d.PropertyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PropertySiteProperty");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.SiteProperties)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteSiteProperty");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.SiteId,
                        e.Validity.Interval.Start,
                        e.Validity.Interval.End,
                        e.Validity.IsInvalid
                    },
                    "NC_SiteProperties_SiteId_Validity_Start_Validity_Invalid"
                )
                .IncludeProperties(e => new { e.Value })
                .WithSettings(new { FILLFACTOR = 90 });
        });

        modelBuilder.Entity<SiteRelation>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("SiteRelations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.ParentSiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ParentSiteId, "IX_FK_SiteChildSite");

            entity.Property(e => e.ChildSiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ChildSiteId, "IX_FK_SiteParentSite");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.ChildSite)
                .WithMany(p => p.SiteRelationChildSites)
                .HasForeignKey(d => d.ChildSiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteParentSite");

            entity
                .HasOne(d => d.ParentSite)
                .WithMany(p => p.SiteRelationParentSites)
                .HasForeignKey(d => d.ParentSiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteChildSite");
        });

        modelBuilder.Entity<SiteSubtype>(entity =>
        {
            entity.ToTable("SiteSubtypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.SiteTypeId, "IX_FK_SiteSubtypeParentSiteType");
            entity.HasIndex(e => e.PrimaryActivityId, "IX_FK_SiteSubtypePrimaryActivity");

            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parameters).HasMaxLength(512);

            entity
                .HasOne(d => d.SiteType)
                .WithMany(p => p.SiteSubtypes)
                .HasForeignKey(d => d.SiteTypeId)
                .HasConstraintName("FK_SiteSubtypeParentSiteType");

            entity
                .HasOne(st => st.PrimaryActivity)
                .WithMany()
                .HasForeignKey(st => st.PrimaryActivityId)
                .HasConstraintName("FK_SiteSubtypePrimaryActivity");

            entity
                .HasMany(d => d.OtherActivities)
                .WithMany()
                .UsingEntity<Dictionary<string, object>>(
                    "SiteSubtypeOtherActivity",
                    r =>
                        r.HasOne<ActivityType>()
                            .WithMany()
                            .HasForeignKey("OtherActivities_Id")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteSubtypeOtherActivities_ActivityType"),
                    l =>
                        l.HasOne<SiteSubtype>()
                            .WithMany()
                            .HasForeignKey("SiteSubtypes_Id")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SiteSubtypeOtherActivities_SiteSubtype"),
                    j =>
                    {
                        j.HasKey("SiteSubtypes_Id", "OtherActivities_Id");
                        j.ToTable("SiteSubtypeOtherActivities");
                        j.HasIndex(["OtherActivities_Id"], "IX_FK_SiteSubtypeOtherActivities_ActivityType");
                        j.IndexerProperty<SiteSubtypeId>("SiteSubtypes_Id").HasColumnName("SiteSubtypes_Id");
                        j.IndexerProperty<RosterItemPartTypeId>("OtherActivities_Id")
                            .HasColumnName("OtherActivities_Id");
                    }
                );
        });

        modelBuilder.Entity<SiteType>(entity =>
        {
            entity.ToTable("SiteTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Code).HasMaxLength(50);
        });
    }
}
