﻿namespace AT.Primitives.Enums;

public enum ContractType
{
    Fixed = 0,
    Floating = 1,

    [Obsolete("This contract type is no longer used and can be removed after migrating all tenants to V4.")]
    FloatingWithFixedMax = 2,

    [Obsolete("This contract type is no longer used and can be removed after migrating all tenants to V4.")]
    FloatingMinMaxFraction = 3,

    [Obsolete("This contract type is no longer used and can be removed after migrating all tenants to V4.")]
    Continuous = 4
}
