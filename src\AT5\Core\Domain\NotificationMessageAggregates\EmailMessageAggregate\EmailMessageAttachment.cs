﻿namespace AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;

public class EmailMessageAttachment : IEntity
{
    public long Id { get; set; }

    public string FileName { get; set; } = null!;

    public byte[] Content { get; set; } = null!;

    public string? ContentType { get; set; }

    public long EmailMessageId { get; set; }

    public virtual EmailMessage EmailMessage { get; set; } = null!;

    public EmailMessageAttachment Clone()
    {
        return new EmailMessageAttachment()
        {
            Id = Id,
            Content = Content,
            ContentType = ContentType,
            EmailMessage = EmailMessage,
            EmailMessageId = EmailMessageId,
            FileName = FileName,
        };
    }
}
