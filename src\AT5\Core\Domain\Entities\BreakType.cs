﻿namespace AT.Core.Domain.Entities;

using Base;

public class BreakType
{
    public RosterItemPartTypeId Id { get; set; }

    public bool Productive { get; set; }

    public virtual ICollection<BreakTemplate> BreakTemplates { get; set; } = new List<BreakTemplate>();

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;

    public virtual ICollection<RequestTypeBreak> BreakRequestTypes { get; set; } = new List<RequestTypeBreak>();
}
