﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using Base;

public class Location
{
    public LocationId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Abbreviation { get; set; } = null!;

    public int? Limit { get; set; }

    public bool Disabled { get; set; }

    public bool IsGlobal { get; set; }

    public bool ImplicitAssignment { get; set; }

    public virtual ICollection<EmployeeLocation> EmployeeLocations { get; set; } = new List<EmployeeLocation>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<Requirement> Requirements { get; set; } = new List<Requirement>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
