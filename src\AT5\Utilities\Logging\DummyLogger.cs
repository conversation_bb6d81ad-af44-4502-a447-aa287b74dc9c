﻿namespace AT.Utilities.Logging;

using System;
using System.Runtime.CompilerServices;

public class DummyLogger<TCategoryName>() : ILogger<TCategoryName>
{
    public IDisposable? BeginScopeWithProperties(params (string Key, object Value)[] properties)
    {
        return null;
    }

    public void Critical(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Critical(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Critical(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Critical(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Debug(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Debug(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Error(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Error(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Error(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Error(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Info(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Info(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Trace(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Trace(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Warn(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Warn(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Warn(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }

    public void Warn(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    ) { }
}
