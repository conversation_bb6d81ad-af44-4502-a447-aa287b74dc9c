﻿namespace AT.Infrastructure.Queries;

using System.Collections.Generic;
using System.Threading.Tasks;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate.Queries;
using AT.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;

public sealed class PushNotificationTokenForUsersQuery(IDataSource<PushNotificationToken> _pnTokenDataSource)
    : IPushNotificationTokenForUsersQuery
{
    public Task<List<UserPnToken>> GetTokensPerUserId(
        IEnumerable<UserId> userIds,
        CancellationToken cancellationToken = default(CancellationToken)
    )
    {
        // FUTURE: There used to be FilterWithTempsIdsCacheable; Is this fast enough now?
        return _pnTokenDataSource
            .Data.Where(token => userIds.Contains(token.UserId))
            .Select(token => new UserPnToken(token.UserId, token.Token))
            .ToListAsync(cancellationToken);
    }
}
