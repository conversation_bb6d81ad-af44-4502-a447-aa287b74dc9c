﻿namespace AT.Infrastructure.Jobs.SimpleJobs;

using AT.Core.MasterDomain;
using AT.Core.SimpleJobs;
using AT.Utilities.Logging;
using AT.Utilities.Reflection;
using Quartz;

[DisallowConcurrentExecution]
public class NonconcurrentScopedJobWrapper<TJobImpl> : IJob
    where TJobImpl : class, ISimpleJob
{
    public async Task Execute(IJobExecutionContext context)
    {
        // NOTE: Can this be all moved to constructor?
        var dataMap = context.MergedJobDataMap;
        var jobContext = (ScopedJobWrapperContext)dataMap[nameof(ScopedJobWrapperContext)];

        var resolver = jobContext.OrgServiceProvider;
        var logger = resolver.Resolve<ILogger<NonconcurrentScopedJobWrapper<TJobImpl>>>();

        try
        {
            var jobImplName = typeof(TJobImpl).GetShortName();
            var fullOrgId = resolver.Resolve<FullOrgId>();

            using var _ = logger.BeginScopeWithProperties(
                ("Organization", fullOrgId.DatabaseName),
                ("JobName", jobImplName)
            );

            using var scopedResolver = resolver.BeginScope();
            var jobImpl = scopedResolver.Resolve<TJobImpl>();

            await jobImpl.Execute();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed with an exception");
        }
    }
}
