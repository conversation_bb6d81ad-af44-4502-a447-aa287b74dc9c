using System;
using System.IO;
using AT.Infrastructure.Utilities.DependencyInjection;
using AT.Infrastructure.Utilities.EntityFramework;
using AT.JobService.DependencyInjection;
using CliFx;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;

EntityFrameworkProfiler.InitializeIfDebugging();

Directory.SetCurrentDirectory(AppDomain.CurrentDomain.BaseDirectory);

var configuration = new ConfigurationBuilder()
    .AddEnvironmentVariables()
    .AddCommandLine(args)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
    .Build();

if (Environment.UserInteractive && args.Length > 0)
{
    await new CliApplicationBuilder()
        .AddCommandsFromThisAssembly()
        .UseTypeActivator(commandTypes =>
            JobServiceDependencyInjection.CreateSingleOrganizationServiceProviderForCliFx(commandTypes, configuration)
        )
        .Build()
        .RunAsync();

    return;
}

var hostBuilder = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration(builder =>
    {
        builder.Sources.Clear();
        builder.AddConfiguration(configuration);
    });

hostBuilder.UseServiceProviderFactory(
    new CustomMultitenantAutofacServiceProviderFactory(
        JobServiceDependencyInjection.CreateMultitenantContainer,
        containerBuilder => JobServiceDependencyInjection.Populate(containerBuilder, configuration)
    )
);

var host = hostBuilder.UseWindowsService().Build();

await host.RunAsync();
