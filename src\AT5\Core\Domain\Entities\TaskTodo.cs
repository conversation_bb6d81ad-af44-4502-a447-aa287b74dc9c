﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Primitives.Enums;

// ISSUE: Is this a good name for this entity? The name is probably TaskTodo because it was conflicting with System.Task.
public class TaskTodo
{
    public TaskTodoId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public RequestStatus Status { get; set; }

    public DateOnly? DueDate { get; set; }

    public DateTime Changed { get; set; }

    public DateTime Created { get; set; }

    public UserId CreatedById { get; set; }

    public UserId ChangedById { get; set; }

    public TaskEntityType Type { get; set; }

    public string? Parameters { get; set; }

    public virtual User ChangedBy { get; set; } = null!;

    public virtual User CreatedBy { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
}
