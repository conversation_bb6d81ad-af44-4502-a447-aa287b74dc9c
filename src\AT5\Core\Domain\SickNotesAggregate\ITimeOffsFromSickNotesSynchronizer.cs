﻿namespace AT.Core.Domain.SickNotesAggregate;

using AT.Core.Domain.RosterItemAggregate;
using AT.Utilities.General;

// TODO: Move type declarations into separate files.

public interface ITimeOffsFromSickNotesSynchronizer
{
    /// <summary>
    /// Creates/updates period time off <see cref="RosterItem"/>s in the roster <paramref name="rosterId"/>
    /// from the <paramref name="sickNoteSyncInfos"/>.
    /// </summary>
    Task<RosterItemsFromSickNotesSyncResult> Synchronize(
        RosterId rosterId,
        IReadOnlyCollection<SickNoteSyncInfo> sickNoteSyncInfos,
        CancellationToken cancellationToken = default
    );
}

public sealed record RosterItemsFromSickNotesSyncResult
{
    /// <summary>
    /// The synchronization created these period time off roster items.
    /// </summary>
    public List<RosterItem> CreatedRosterItems { get; set; } = [];

    /// <summary>
    /// The synchronization did not create period time off roster items for these <see cref="SickNote"/>s.
    /// </summary>
    public Dictionary<SickNoteId, PeriodTimeOffCreationSkipReason> CreationSkipReasonPerSickNoteId { get; set; } = [];

    /// <summary>
    /// The synchronization made these changes to existing <see cref="RosterItem"/>s.
    /// </summary>
    public Dictionary<RosterItemId, RosterItemChanges> ChangesPerRosterItemId { get; set; } = [];

    /// <summary>
    /// The synchronization skipped these changes to existing <see cref="RosterItem"/>s.
    /// </summary>
    public Dictionary<RosterItemId, SkippedRosterItemChanges> SkippedChangesPerRosterItemId { get; set; } = [];

    public sealed record RosterItemChanges
    {
        public EntityChange<DateTime>? IntervalStartChange { get; set; }

        public EntityChange<DateTime>? IntervalEndChange { get; set; }

        public EntityChange<RosterItemPartTypeId>? TimeOffTypeChange { get; set; }
    }

    public sealed record SkippedRosterItemChanges
    {
        public EntityChangeSkip<DateTime>? SkippedIntervalStartChange { get; set; }

        public EntityChangeSkip<DateTime>? SkippedIntervalEndChange { get; set; }

        public EntityChangeSkip<RosterItemPartTypeId>? SkippedTimeOffTypeChange { get; set; }
    }
}

public record SickNoteSyncInfo
{
    public required UserId EmployeeId { get; init; }

    public required DateOnly From { get; init; }

    public required DateOnly? To { get; init; }

    /// <summary>
    /// Create/update <see cref="RosterItem"/> with a single <see cref="RosterItemPart"/> that will
    /// have a <see cref="RosterItemPartType"/> with this ID.
    /// </summary>
    public required RosterItemPartTypeId TimeOffTypeId { get; init; }

    public required SickNoteId SickNoteId { get; init; }

    /// <summary>
    /// If set to <see langword="true"/>, then this data row corresponds to a newly created <see cref="SickNote"/>,
    /// and as such, we can be sure there is no <see cref="RosterItem"/> created for it.
    /// </summary>
    public required bool IsNewSickNote { get; set; }

    public required SickNoteSynchronizationInfo SynchronizationInfo { get; init; }

    public OpenDateInterval OpenDateInterval => new(From, To);
}

public readonly record struct EntityChangeSkip<T>(
    EntityChange<T> SkippedChange,
    PeriodTimeOffFieldUpdateSkipReason SkipReason
);

public readonly record struct SickNoteSynchronizationInfo(
    bool SynchronizeStart,
    bool SynchronizeEnd,
    bool SynchronizeType
);

/// <summary>
/// The set of possible reasons why the period time off's <see cref="RosterItem"/>'s field was not updated.
/// </summary>
public enum PeriodTimeOffFieldUpdateSkipReason
{
    /// <summary>
    /// The field was not updated because the new value is the same as the old one.
    /// </summary>
    ValueIsUnchanged,

    /// <summary>
    /// The property update was skipped due to instruction specified by <see cref="SickNoteSynchronizationInfo"/>.
    /// </summary>
    SynchronizationSkipRequested,

    /// <summary>
    /// The update would make an unallowed change to the period time off's <see cref="RosterItem"/>
    /// due to <see cref="RosterItem"/>'s <see cref="Employee"/> having <see cref="EmployeeRosterAggregate.EmployeeRosterFinish.State"/>
    /// set to <see cref="AttendanceFinishState.Finished"/>.
    /// </summary>
    TimeOffIsInFinishedRoster,
}

/// <summary>
/// The set of possible reasons why the period time off's <see cref="RosterItem"/>'s was not created from a <see cref="SickNote"/>.
/// </summary>
public enum PeriodTimeOffCreationSkipReason
{
    SickNoteIntervalIsInFinishedRoster,
}
