﻿namespace AT.Infrastructure.MasterDbQueries;

using System.Threading;
using System.Threading.Tasks;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

internal sealed class OrganizationInfoQuery(IDbContextFactory<MasterDbContext> _masterDbContextFactory)
    : IOrganizationInfoQuery
{
    public async Task<OrganizationInfo?> TryGetOrganizationInfoAsync(
        OrganizationId id,
        CancellationToken cancellationToken = default
    )
    {
        using var masterDbContext = _masterDbContextFactory.CreateDbContext();
        return await masterDbContext
            .Organizations.AsQueryable()
            .AsNoTracking()
            .Where(x => x.Id == id)
            .Select(x => new OrganizationInfo(x.Id, x.Name, x.UrlId, x.DatabaseName, x.HeaderTitle, x.HeaderColor))
            .FirstOrDefaultAsync(cancellationToken);
    }

    public OrganizationInfo? TryGetOrganizationInfo(OrganizationId id)
    {
        using var masterDbContext = _masterDbContextFactory.CreateDbContext();
        return masterDbContext
            .Organizations.AsQueryable()
            .AsNoTracking()
            .Where(x => x.Id == id)
            .Select(x => new OrganizationInfo(x.Id, x.Name, x.UrlId, x.DatabaseName, x.HeaderTitle, x.HeaderColor))
            .FirstOrDefault();
    }
}
