﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Report"/> in the database.
/// </summary>
internal static class ReportGroupModelBuilder
{
    public static void BuildReportGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Report>(entity =>
        {
            entity.ToTable("Reports");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.ReportCategoryId, "IX_FK_ReportCategoryReport");

            entity.HasIndex(e => e.SqlReportDefinitionId, "IX_FK_ReportSqlReportDefinition");

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(100);

            entity
                .HasOne(d => d.ReportCategory)
                .WithMany(p => p.Reports)
                .HasForeignKey(d => d.ReportCategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportCategoryReport");

            entity
                .HasOne(d => d.SqlReportDefinition)
                .WithMany(p => p.Reports)
                .HasForeignKey(d => d.SqlReportDefinitionId)
                .HasConstraintName("FK_ReportSqlReportDefinition");

            entity
                .HasMany(d => d.Roles)
                .WithMany(p => p.Reports)
                .UsingEntity<Dictionary<string, object>>(
                    "ReportRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("RolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ReportRoles_Role"),
                    l =>
                        l.HasOne<Report>()
                            .WithMany()
                            .HasForeignKey("ReportsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ReportRoles_Report"),
                    j =>
                    {
                        j.HasKey("ReportsId", "RolesId");
                        j.ToTable("ReportRoles");
                        j.HasIndex(["RolesId"], "IX_FK_ReportRoles_Role");
                        j.IndexerProperty<ReportId>("ReportsId").HasColumnName("Reports_Id");
                        j.IndexerProperty<RoleId>("RolesId").HasColumnName("Roles_Id");
                    }
                );
        });

        modelBuilder.Entity<ReportCategory>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ReportCategories");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.Rank).HasColumnOrder(iota);
        });

        modelBuilder.Entity<ReportColumn>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ReportColumns");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Rank).HasColumnOrder(iota);
            entity.Property(e => e.Name).HasColumnOrder(iota);

            entity.Property(e => e.ReportId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ReportId, "IX_FK_ReportReportColumn");

            entity.Property(e => e.SiteDepId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteDepId, "IX_FK_ReportColumnSite");

            entity.Property(e => e.AgregateFunction).HasColumnOrder(iota);
            entity.Property(e => e.Global).HasColumnOrder(iota);
            entity.Property(e => e.Expression).HasColumnOrder(iota);
            entity.Property(e => e.TotalAggregateFuncion).HasColumnOrder(iota);
            entity.Property(e => e.Parameters).HasMaxLength(4000).HasColumnOrder(iota);
            entity.Property(e => e.Hidden).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.ReportColumns)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportReportColumn");

            entity
                .HasOne(d => d.SiteDep)
                .WithMany(p => p.ReportColumns)
                .HasForeignKey(d => d.SiteDepId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportColumnSite");
        });

        modelBuilder.Entity<ReportColumnField>(entity =>
        {
            entity.ToTable("ReportColumnFields");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.FilterId, "IX_FK_ReportColumnFieldFilter");

            entity.HasIndex(e => e.ReportColumnId, "IX_FK_ReportColumnReportColumnField");

            entity.Property(e => e.Method).HasMaxLength(100);
            entity.Property(e => e.Name).HasMaxLength(50);

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.ReportColumnFields)
                .HasForeignKey(d => d.FilterId)
                .HasConstraintName("FK_ReportColumnFieldFilter");

            entity
                .HasOne(d => d.ReportColumn)
                .WithMany(p => p.ReportColumnFields)
                .HasForeignKey(d => d.ReportColumnId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportColumnReportColumnField");
        });

        modelBuilder.Entity<ReportDimension>(entity =>
        {
            entity.ToTable("ReportDimensions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.ReportId, "IX_FK_ReportReportDimension");

            entity.Property(e => e.Method).HasMaxLength(100);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parametes).HasMaxLength(4000);

            entity
                .HasOne(d => d.Report)
                .WithMany(p => p.ReportDimensions)
                .HasForeignKey(d => d.ReportId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReportReportDimension");

            entity
                .HasMany(d => d.Subtotals)
                .WithMany(p => p.Dimensions)
                .UsingEntity<Dictionary<string, object>>(
                    "ReportDimensionReportSubtotal",
                    r =>
                        r.HasOne<ReportSubtotal>()
                            .WithMany()
                            .HasForeignKey("SubtotalsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ReportDimensionReportSubtotal_ReportSubtotal"),
                    l =>
                        l.HasOne<ReportDimension>()
                            .WithMany()
                            .HasForeignKey("DimensionsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ReportDimensionReportSubtotal_ReportDimension"),
                    j =>
                    {
                        j.HasKey("DimensionsId", "SubtotalsId");
                        j.ToTable("ReportDimensionReportSubtotal");
                        j.HasIndex(["SubtotalsId"], "IX_FK_ReportDimensionReportSubtotal_ReportSubtotal");
                        j.IndexerProperty<ReportDimensionId>("DimensionsId").HasColumnName("Dimensions_Id");
                        j.IndexerProperty<ReportSubtotalId>("SubtotalsId").HasColumnName("Subtotals_Id");
                    }
                );
        });

        modelBuilder.Entity<ReportSubtotal>(entity =>
        {
            entity.ToTable("ReportSubtotals");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<SqlReportDefinition>(entity =>
        {
            entity.ToTable("SqlReportDefinitions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50);
        });
    }
}
