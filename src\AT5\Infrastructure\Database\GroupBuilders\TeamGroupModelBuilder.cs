﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Team"/> in the database.
/// </summary>
internal static class TeamGroupModelBuilder
{
    public static void BuildTeamGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Team>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Teams");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_SiteTeam");

            entity.Property(e => e.CcId).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Name).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.VisibleInRoster).HasColumnOrder(iota);
            entity.Property(e => e.VisibelForPlanning).HasColumnOrder(iota);
            entity.Property(e => e.CheckedForPlanning).HasColumnOrder(iota);
            entity.Property(e => e.Rank).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.GroupInRoster).HasColumnOrder(iota);
            entity.Property(e => e.HideInReport).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.Teams)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteTeam");
        });
    }
}
