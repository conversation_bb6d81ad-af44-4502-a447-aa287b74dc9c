namespace AT.NotificationsService.Commands;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Primitives.Enums;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

/// <example>
/// BareSendPushNotificationToToken --totoken dasa5ds4asd5a4f55eg454f5we --title Test1 --body Test2 --sendtomultiple false
/// </example>
[Command("BareSendPushNotificationToToken", Description = "Send a push notification via Firebase to token")]
public class BareSendPushNotificationToTokenCommand(IPushNotificationSender _pushNotificationSender) : ICommand
{
    [CommandOption(nameof(ToToken), Description = "User's Firebase token")]
    public required string ToToken { get; init; }

    [CommandOption(nameof(Title), Description = "Title of the push notification.")]
    public required string Title { get; init; }

    [CommandOption(nameof(Body), Description = "Text of the push notification.")]
    public required string Body { get; init; }

    [CommandOption(
        nameof(SendToMultiple),
        Description = "Whether to use SendToMultiple or SendToSingle API method. Default is true -> SendToMultiple."
    )]
    public bool SendToMultiple { get; init; } = true;

    public async ValueTask ExecuteAsync(IConsole console)
    {
        PushNotification notification =
            new()
            {
                Title = Title,
                Body = Body,
                Type = PushNotificationType.BreakEnd,
                Generated = DateTime.Now,
            };

        bool success;
        if (SendToMultiple)
        {
            var result = await _pushNotificationSender.SendPushNotificationToMultipleAsync([ToToken], notification);
            success = result.Success;
        }
        else
        {
            var result = await _pushNotificationSender.SendPushNotificationToSingleAsync(ToToken, notification);
            success = result.Success;
        }

        var successInfoMessage = success ? "succeeded" : "failed";
        await console.Output.WriteLineAsync($"Sending notification {successInfoMessage}.");
    }
}
