﻿namespace AT.Translations;

using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using AT.Translations.Formatting;
using SmartFormat;

public sealed partial class Translator : ITranslator
{
    /// <summary>
    /// We need to inject <see cref="ITranslationParametersFormatter"/> as Lazy because it injects dependencies that
    /// inject Translator which causes circular dependency, preventing DI container from instantiating these classes.
    /// </summary>
    private readonly Lazy<ITranslationParametersFormatter> _parameterFormatterLazy;
    private readonly Dictionary<ResxLocation, LocalizationProvider> _localizationProviders;

    public Translator(
        Lazy<ITranslationParametersFormatter> parameterFormatterLazy,
        IEnumerable<ResourceManagerWrapper> resourceManagerWrappers
    )
    {
        _parameterFormatterLazy = parameterFormatterLazy;
        _localizationProviders = resourceManagerWrappers
            .Distinct()
            .ToDictionary(
                x => x.ResxLocation,
                x => new LocalizationProvider(x.ResourceManager)
                {
                    FallbackCulture = CultureInfo.GetCultureInfo(Language.Invariant.LanguageLocaleName.Value),
                }
            );
    }

    public TranslatedText Translate<T>(Language? language = null)
        where T : ITranslationNoParameters
    {
        var translationType = typeof(T);
        string localizedTemplate = GetLocalizedTemplate(translationType, language);

        return TranslatedText.From(localizedTemplate);
    }

    public TranslatedText Translate(ITranslationWithParameters translation, Language? language = null)
    {
        var translationType = translation.GetType();
        string localizedTemplate = GetLocalizedTemplate(translationType, language);

        // If the message template contains string such as "{MyValue:", replace it with "{MyValueRaw:" so that
        // _parameterFormatter includes the original (non-formatted) value in the list of SmartFormat's parameters.
        // It's needed because typically what follows after the ":" character needs to work with the property's
        // original data type, not the formatted string.
        // Example:
        //   localizedTemplate: "Value {Value} is too {Value:cond:<=3?low|high}."
        // The first "{Value" is replaced with a formatted string such as "1,222.156", but the second "{Value:"
        // has to be replaced with the number 1222.156. Therefore, we change the second occurrence to "{ValueRaw".
        localizedTemplate = IncludeRawValueRegex().Replace(localizedTemplate, "{$1Raw:");

        var parameters = _parameterFormatterLazy.Value.Format(localizedTemplate, translation, language);

        return TranslatedText.From(Smart.Format(localizedTemplate, parameters));
    }

    private static ResxLocation GetResxLocation(Type translationsContainerType)
    {
        string assemblyName = translationsContainerType.Assembly.GetName().Name!;
        string resxRelativeLocation = translationsContainerType
            .GetCustomAttribute<ResxSourceAttribute>()!
            .OutputFileRelativeNamespace;

        return new ResxLocation(assemblyName, resxRelativeLocation);
    }

    private string GetLocalizedTemplate(Type translationType, Language? language = null)
    {
        string translationKey = translationType.Name;
        var resxLocation = GetResxLocation(translationType.DeclaringType!);
        var localizationProvider = _localizationProviders[resxLocation];

        return language is not null
            ? localizationProvider.GetString(translationKey, language.LanguageLocaleName.Value)!
            : localizationProvider.GetString(translationKey)!;
    }

    /// <summary>
    /// Compile-time generated regex to match strings such as "{MyValue:", "{Interval:", ...
    /// </summary>
    [GeneratedRegex(@"\{([A-Za-z0-9]+):")]
    private static partial Regex IncludeRawValueRegex();
}
