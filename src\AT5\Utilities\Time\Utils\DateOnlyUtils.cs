﻿namespace AT.Utilities.Time.Utils;

using System;

/// <summary>
/// Provides utility methods for performing common operations on <see cref="DateOnly"/> and nullable <see cref="DateOnly"/> values.
/// </summary>
public static class DateOnlyUtils
{
    /// <summary>
    /// Returns the later of two <see cref="DateOnly"/> values.
    /// </summary>
    /// <param name="first">The first <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second <see cref="DateOnly"/> value.</param>
    /// <returns>The later of the two <see cref="DateOnly"/> values.</returns>
    public static DateOnly Max(DateOnly first, DateOnly second)
    {
        return first >= second ? first : second;
    }

    /// <summary>
    /// Returns the later of two nullable <see cref="DateOnly"/> values, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateOnly? Max(DateOnly? first, DateOnly? second)
    {
        if (first.HasValue && second.HasValue)
        {
            return Max(first.Value, second.Value);
        }

        return first ?? second;
    }

    /// <summary>
    /// Returns the later of two <see cref="DateOnly"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateOnly Max(DateOnly? first, DateOnly second)
    {
        if (!first.HasValue)
        {
            return second;
        }

        return Max(first.Value, second);
    }

    /// <summary>
    /// Returns the later of two <see cref="DateOnly"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The nullable <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateOnly Max(DateOnly first, DateOnly? second)
    {
        return Max(second, first);
    }

    /// <summary>
    /// Returns the earlier of two <see cref="DateOnly"/> values.
    /// </summary>
    /// <param name="first">The first <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two <see cref="DateOnly"/> values.</returns>
    public static DateOnly Min(DateOnly first, DateOnly second)
    {
        return first <= second ? first : second;
    }

    /// <summary>
    /// Returns the earlier of two nullable <see cref="DateOnly"/> values, treating <c>null</c> as the largest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the largest value.</returns>
    public static DateOnly? Min(DateOnly? first, DateOnly? second)
    {
        if (first.HasValue && second.HasValue)
        {
            return Min(first.Value, second.Value);
        }

        return first ?? second;
    }

    /// <summary>
    /// Returns the earlier of two <see cref="DateOnly"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateOnly Min(DateOnly? first, DateOnly second)
    {
        if (!first.HasValue)
        {
            return second;
        }

        return Min(first.Value, second);
    }

    /// <summary>
    /// Returns the earlier of two <see cref="DateOnly"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The nullable <see cref="DateOnly"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateOnly"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateOnly Min(DateOnly first, DateOnly? second)
    {
        return Min(second, first);
    }

    /// <summary>
    /// Converts a nullable <see cref="DateOnly"/> and a <see cref="TimeOnly"/> to a nullable <see cref="DateTime"/>.
    /// </summary>
    /// <param name="dateOnly">The nullable <see cref="DateOnly"/> value to convert.</param>
    /// <param name="time">The <see cref="TimeOnly"/> value representing the time component to combine with the date.</param>
    /// <returns>
    /// A nullable <see cref="DateTime"/> representing the combination of the date and time components,
    /// or <c>null</c> if the input <paramref name="dateOnly"/> is <c>null</c>.
    /// </returns>
    public static DateTime? ToDateTime(this DateOnly? dateOnly, TimeOnly time)
    {
        return dateOnly?.ToDateTime(time);
    }
}
