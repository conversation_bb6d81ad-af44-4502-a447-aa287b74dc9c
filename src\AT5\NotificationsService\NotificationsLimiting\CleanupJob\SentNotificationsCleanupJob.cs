﻿namespace AT.NotificationsService.NotificationsLimiting.CleanupJob;

using AT.Core.SimpleJobs;
using AT.NotificationsService.NotificationsLimiting.Interfaces;
using Microsoft.Extensions.Options;

public class SentNotificationsCleanupJob(
    ILogger<SentNotificationsCleanupJob> _logger,
    ISentNotificationService _sentNotificationService,
    IOptions<RateLimitingConfig> _config
) : ISimpleJob
{
    public async Task Execute()
    {
        _logger.Info(nameof(SentNotificationsCleanupJob) + " Starting...");

        // FUTURE: Do not use DateTime.Today/Now.
        var lastRelevantDay = DateTime.Today.AddDays(-_config.Value.CleanupJobMaxDaysBack);

        await _sentNotificationService.DeleteAsync(untilExclusive: lastRelevantDay);

        _logger.Info(nameof(SentNotificationsCleanupJob) + " Successfully finished. Terminating...");
    }
}
