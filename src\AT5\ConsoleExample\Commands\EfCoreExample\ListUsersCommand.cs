namespace AT.ConsoleExample.Commands.EfCoreExample;

using AT.PrimitivesAT5.Ids;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Repositories;

[Command(
    "ef list",
    Description = "Lists users with given ids how many notes they have. No ids means listing all users"
)]
public class ListUsersCommand(IUserWithNotesReader _userWithNotesReader) : ICommand
{
    [CommandParameter(0, Description = "Ids of users to list", IsRequired = false)]
    public IReadOnlyList<UserId> UserIds { get; init; } = [];

    public async ValueTask ExecuteAsync(IConsole console)
    {
        var users = await _userWithNotesReader.GetUserNoteDataAsync(UserIds);

        foreach (var user in users)
        {
            await console.Output.WriteLineAsync($"[{user.Id}] {user.User} has {user.NoteCount} notes.");
        }
    }
}
