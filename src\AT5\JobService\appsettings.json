{
  "GeneralConfig": {
    "DbName": "at_",
    "IsSingleOrg": true
  },
  "ConnectionStrings": {
    "LegacyMasterDb": "metadata=res://*/MasterModel.csdl|res://*/MasterModel.ssdl|res://*/MasterModel.msl;provider=System.Data.SqlClient;provider connection string=\"; Data Source=.\\;Initial Catalog=AristoTelos;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false\";",
    "MasterDb": "Data Source=.\\;Initial Catalog=AristoTelos;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;",
    "OrgDbTemplate": "Data Source=.\\;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;"
  },
  // Some settings that are not explicitly stated in here are added to the serilog in SerilogLoggerCreator.cs.
  "Serilog": {
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/NotificationsService_.log",
          "fileSizeLimitBytes": 31457280
        }
      }
    ]
  }
}