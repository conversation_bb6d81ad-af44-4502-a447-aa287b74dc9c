﻿namespace AT.ApiService.Endpoints.CurrentUser;

using AT.Core.Auth;
using AT.Core.Domain.UserAggregate.Queries;
using AT.Shared.Models.CurrentUser;
using FastEndpoints;

public class GetBasicInfoEndpoint(IUserAuthenticator _userAuthenticator, IBasicUserInfoQuery _basicUserInfoQuery)
    : EndpointWithoutRequest<CurrentUserBasicInfoResponse>
{
    public override void Configure()
    {
        Get("/currentuser/basicinfo");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var userId = _userAuthenticator.GetCurrentUserIdOrThrow();
        var userInfo = await _basicUserInfoQuery.GetUserInfoAsync(userId, ct);

        var result = new CurrentUserBasicInfoResponse(
            Username: userInfo.Username,
            FirstName: userInfo.FirstName,
            LastName: userInfo.LastName
        );

        await SendAsync(result, cancellation: ct);
    }
}
