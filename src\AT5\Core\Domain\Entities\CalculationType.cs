﻿namespace AT.Core.Domain.Entities;

using Base;

public class CalculationType
{
    public CalculationTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string InputFields { get; set; } = null!;

    public string Parameters { get; set; } = null!;

    public bool IsGlobal { get; set; }

    public virtual ICollection<CalculationTypePhase> CalculationTypePhases { get; set; } =
        new List<CalculationTypePhase>();

    public virtual ICollection<Calculation> Calculations { get; set; } = new List<Calculation>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
