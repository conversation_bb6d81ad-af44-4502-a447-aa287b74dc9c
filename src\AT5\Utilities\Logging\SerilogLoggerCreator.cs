﻿namespace AT.Utilities.Logging;

using AT.Utilities.Collections;
using Microsoft.Extensions.Configuration;
using Serilog;

public static class SerilogLoggerCreator
{
    public static IReadOnlyDictionary<string, string?> DefaultMinimumLevelSettings { get; } =
        new Dictionary<string, string?>()
        {
            { "Serilog:MinimumLevel:Default", "Verbose" },
            { "Serilog:MinimumLevel:Override:Microsoft", "Warning" },
            { "Serilog:MinimumLevel:Override:System", "Warning" },
        };

    public static IReadOnlyDictionary<string, string?> DefaultConsoleSinkSettings { get; } =
        new Dictionary<string, string?>()
        {
            { "Args:formatter:type", "Serilog.Templates.ExpressionTemplate, Serilog.Expressions" },
            {
                "Args:formatter:template",
                "[{@t:HH:mm:ss} {@l:u3}] {#if SourceContext is not null}{Concat('[', Substring(SourceContext, LastIndexOf(SourceContext, '.') + 1), ']', ' ')}{#end}{@m}\n{@x}"
            },
        };

    public static IReadOnlyDictionary<string, string?> DefaultFileSinkSettings { get; } =
        new Dictionary<string, string?>()
        {
            { "Args:formatter:type", "Serilog.Templates.ExpressionTemplate, Serilog.Expressions" },
            {
                "Args:formatter:template",
                "[{@t:HH:mm:ss} {@l:u3}] {#if SourceContext is not null}{Concat('[', Substring(SourceContext, LastIndexOf(SourceContext, '.') + 1), ']', ' ')}{#end}{@m}{#if rest(true) <> {}} <{#each k, v in rest(true)}{k} = {v}{#delimit} | {#end}>{#end}\n{@x}"
            },
            { "Args:rollingInterval", "Day" }, // Create a new log file for every day.
            { "Args:rollOnFileSizeLimit", "true" }, // When file limit size is exceeded, create a new log file.
            { "Args:fileSizeLimitBytes", "10485760" }, // 10 MB
            { "Args:retainedFileCountLimit", "30" }, // Log files retention period in number of days.
        };

    public static IReadOnlyDictionary<string, string?> DefaultEnrichers { get; } =
        new Dictionary<string, string?>()
        {
            { "Serilog:Enrich:0", "FromLogContext" }, // Includes key-value pairs from the BeginScope called on the ILogger.
            { "Serilog:Enrich:1", "WithThreadId" },
            { "Serilog:Enrich:2", "WithMachineName" },
        };

    /// <summary>
    /// Based on "Serilog" section in the <paramref name="configuration"/> and the default Serilog settings, create a Serilog logger.
    /// </summary>
    public static ILogger Create(IConfiguration configuration)
    {
        var writeToSections = configuration.GetSection("Serilog:WriteTo").GetChildren().ToList();

        // Determine which sinks should be put into the "Using" section. The "Using" section helps Serilog find needed assemblies.
        var requiredSinksNames = writeToSections
            .Select(x => x.GetValue<string>("Name"))
            .Where(name => !string.IsNullOrEmpty(name))
            .Distinct()
            .ToList();

        var defaultSettings = new Dictionary<string, string?>();

        // Ensure "Using" contains the necessary sinks.
        for (int i = 0; i < requiredSinksNames.Count; i++)
        {
            string? sink = requiredSinksNames[i];
            string fullSinkName = sink switch
            {
                "Console" => "Serilog.Sinks.Console",
                "File" => "Serilog.Sinks.File",

                _ => throw new InvalidOperationException($"Unknown sink name for the input sink {sink}")
            };

            defaultSettings[$"Serilog:Using:{i}"] = fullSinkName;
        }

        // Add default settings to WriteTo sections.
        for (int i = 0; i < writeToSections.Count; i++)
        {
            var section = writeToSections[i];
            var sinkName = section.GetValue<string>("Name");

            if (sinkName == "Console")
            {
                DefaultConsoleSinkSettings.ForEach(x =>
                    defaultSettings.TryAdd($"Serilog:WriteTo:{i}:{x.Key}", x.Value)
                );
            }
            else if (sinkName == "File")
            {
                DefaultFileSinkSettings.ForEach(x => defaultSettings.TryAdd($"Serilog:WriteTo:{i}:{x.Key}", x.Value));
            }
        }

        // Ensure MinimumLevel settings exist.
        DefaultMinimumLevelSettings.ForEach(x => defaultSettings.TryAdd(x.Key, x.Value));

        // Add Enricher defaults only if they are not explicitly specified.
        if (!configuration.GetSection("Serilog:Enrich").Exists())
        {
            DefaultEnrichers.ForEach(x => defaultSettings.Add(x.Key, x.Value));
        }

        var mergedConfiguration = new ConfigurationBuilder()
            .AddInMemoryCollection(defaultSettings)
            .AddConfiguration(configuration)
            .Build(); // Adding the original configuration after adding the defaultSettings overwrites the defaults.

        return new LoggerConfiguration().ReadFrom.Configuration(mergedConfiguration).CreateLogger();
    }
}
