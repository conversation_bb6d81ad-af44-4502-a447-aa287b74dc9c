﻿namespace AT.Core.Domain.RosterItemAggregate;

using System.Collections.Generic;
using System.Diagnostics;

public sealed class PeriodTimeOffWriteService(IRosterItemRepository _rosterItemRepository) : IPeriodTimeOffWriteService
{
    public Task<List<RosterItem>> CreatePeriodTimeOffsAsync(
        IEnumerable<PeriodTimeOffInfo> periodTimeOffInfos,
        CancellationToken cancellationToken = default
    )
    {
        throw new NotImplementedException();
        //_rosterItemRepository.AddForInsert();
    }

    public Task UpdatePeriodTimeOffsAsync(
        IEnumerable<RosterItem> rosterItem,
        CancellationToken cancellationToken = default
    )
    {
        Debug.Assert(rosterItem.All(ri => ri.RosterItemParts.Count == 1));

        throw new NotImplementedException();
        //_rosterItemRepository.AddForUpdate();
    }
}
