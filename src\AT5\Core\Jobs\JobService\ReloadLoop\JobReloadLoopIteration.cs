﻿namespace AT.Core.Jobs.JobService.ReloadLoop;

using System.Threading;
using System.Threading.Tasks;
using AT.Core.Domain.JobsAggregate.Queries;
using AT.Core.Loops;
using AT.Utilities.Logging;

public class JobReloadLoopIteration(ILogger<JobReloadLoopIteration> _logger, IJobQuery _jobQuery)
    : ILoopIteration<JobReloadLoopParameters>
{
    public async Task Execute(JobReloadLoopParameters parameters, CancellationToken ct = default)
    {
        _logger.Info($"Executing {nameof(JobReloadLoopIteration)}.");

        _logger.Info($"Loading jobs and triggers.");
        var jobSeries = await _jobQuery.GetAllJobSeries(ct);
        _logger.Info(
            "Found {JobsCount} jobs and {TriggersCount} triggers.",
            jobSeries.Count,
            jobSeries.SelectMany(x => x.Triggers).Count()
        );

        _logger.Info("Updating job service.");
        await parameters.JobService.UpdateJobs(jobSeries, ct);

        _logger.Info($"Finished {nameof(JobReloadLoopIteration)}.");
    }
}
