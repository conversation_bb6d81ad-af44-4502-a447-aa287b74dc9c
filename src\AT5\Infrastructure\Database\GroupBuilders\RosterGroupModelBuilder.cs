﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Roster"/> in the database.
/// </summary>
internal static class RosterGroupModelBuilder
{
    public static void BuildRosterGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<PlanningPeriod>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("PlanningPeriods");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_SitePlanningPeriod");

            entity.HasDateInterval(e => e.Interval, iota);
            entity.HasDateInterval(e => e.RequestInterval, iota);

            entity.Property(e => e.Created).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.CreatedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.CreatedById, "IX_FK_PlanningPeriodUser");

            entity.Property(e => e.HideInRoster).HasColumnOrder(iota);

            entity.Property(e => e.InputRosterId).HasColumnName("InputRoster_Id").HasColumnOrder(iota);
            entity.HasIndex(e => e.InputRosterId, "IX_FK_InputRosterPlanningPeriod");

            entity.Property(e => e.PrepublicRosterId).HasColumnName("PrepublicRoster_Id").HasColumnOrder(iota);
            entity.HasIndex(e => e.PrepublicRosterId, "IX_FK_PrepublicRosterPlanningPeriod");

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.PlanningPeriods)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PlanningPeriodUser");

            entity
                .HasOne(d => d.InputRoster)
                .WithMany(p => p.PlanningPeriodInputRosters)
                .HasForeignKey(d => d.InputRosterId)
                .HasConstraintName("FK_InputRosterPlanningPeriod");

            entity
                .HasOne(d => d.PrepublicRoster)
                .WithMany(p => p.PlanningPeriodPrepublicRosters)
                .HasForeignKey(d => d.PrepublicRosterId)
                .HasConstraintName("FK_PrepublicRosterPlanningPeriod");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.PlanningPeriods)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SitePlanningPeriod");
        });

        modelBuilder.Entity<Roster>(entity =>
        {
            entity.ToTable("Rosters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.PlanningPeriodId, "IX_FK_PlanningPeriodRoster");

            entity.HasIndex(e => e.CreatedById, "IX_FK_RosterCreated");

            entity.HasIndex(e => e.LockedById, "IX_FK_RosterLocked");

            entity
                .HasIndex(context.AdditionalIndexCollection, e => new { e.SiteId, e.Deleted }, "IX_FK_SiteRosters")
                .WithSettings(new { FILLFACTOR = 80 });

            entity.HasIndex(e => new { e.SiteRosterType, e.SiteId }, "IX_NC_SpecialRoster_Fetch");

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.CreatedById).HasColumnName("CreatedBy_Id");
            entity.Property(e => e.LockTime).IsStoredAsDateTime();
            entity.Property(e => e.LockedById).HasColumnName("LockedBy_Id");

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.RosterCreatedBies)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterCreated");

            entity
                .HasOne(d => d.LockedBy)
                .WithMany(p => p.RosterLockedBies)
                .HasForeignKey(d => d.LockedById)
                .HasConstraintName("FK_RosterLocked");

            entity
                .HasOne(d => d.PlanningPeriod)
                .WithMany(p => p.Rosters)
                .HasForeignKey(d => d.PlanningPeriodId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_PlanningPeriodRoster");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.Rosters)
                .HasForeignKey(d => d.SiteId)
                .HasConstraintName("FK_SiteRosters");
        });
    }
}
