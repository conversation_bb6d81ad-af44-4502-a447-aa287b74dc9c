﻿namespace AT.NotificationsService.PushNotifications.Firebase;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Utilities.Logging;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Microsoft.Extensions.Options;

public class FirebasePushNotificationSender : IPushNotificationSender
{
    private readonly ILogger<FirebasePushNotificationSender> _logger;

    public FirebaseMessaging Messaging { get; private set; }

    // NOTE: Not using IOptionsMonitor because FirebaseApp.Create can be called just once. Calling it again with a different
    // firebase config throws an expection. There is a Delete method on FirebaseApp which could potentially be used (and then Create
    // should work just fine) but it could cause some problems, so for now, using just IOptions.
    public FirebasePushNotificationSender(
        ILogger<FirebasePushNotificationSender> logger,
        IOptions<PushNotificationConfig> pushNotificationConfig
    )
    {
        _logger = logger;

        if (FirebaseApp.DefaultInstance == null)
        {
            var firebaseConfiguration = pushNotificationConfig.Value.FirebaseConfiguration;

            var app = FirebaseApp.Create(
                new AppOptions() { Credential = GoogleCredential.FromJson(firebaseConfiguration.GoogleServices) }
            );

            Messaging = FirebaseMessaging.GetMessaging(app);
        }
        else
        {
            Messaging = FirebaseMessaging.DefaultInstance;
        }
    }

    public async Task<PushNotificationSendToMultipleResult> SendPushNotificationToMultipleAsync(
        IReadOnlyList<string> tokens,
        PushNotification pushNotification,
        bool dryRun = false,
        CancellationToken cancellationToken = default
    )
    {
        // Firebase allows maximum of 500 tokens in a single message.
        var tokensBatches = tokens.SplitToListsOfUniqueElements(500);
        int successCount = 0;
        int failureCount = 0;
        List<Exception> exceptions = [];

        foreach (var tokensBatch in tokensBatches)
        {
            try
            {
                var message = new MulticastMessage()
                {
                    Tokens = tokensBatch,
                    Notification = ToFirebaseNotification(pushNotification),
                    Android = new AndroidConfig { Notification = new AndroidNotification { Sound = "default" } }
                };

                var batchResult = await Messaging.SendEachForMulticastAsync(message, dryRun, cancellationToken);

                successCount += batchResult.SuccessCount;
                failureCount += batchResult.FailureCount;
                exceptions.AddRange(batchResult.Responses.Select(r => r.Exception).Where(e => e is not null));
            }
            catch (Exception ex)
            {
                if (ex is FirebaseMessagingException firebaseMessagingException)
                {
                    _logger.Error(
                        ex,
                        "SendEachForMulticastAsync failed with an exception with {ErrorCode} and {MessagingErrorCode} for the batch of {Tokens} tokens.",
                        firebaseMessagingException.ErrorCode,
                        firebaseMessagingException.MessagingErrorCode,
                        tokensBatch.Count
                    );
                }
                else
                {
                    _logger.Error(
                        ex,
                        "SendEachForMulticastAsync failed with an exception for the batch of {Tokens} tokens.",
                        tokensBatch.Count
                    );
                }

                failureCount += tokensBatch.Count;
                exceptions.Add(ex);
            }
        }

        return new PushNotificationSendToMultipleResult(
            SuccessCount: successCount,
            FailureCount: failureCount,
            Exceptions: exceptions
        );
    }

    public async Task<PushNotificationSendToSingleResult> SendPushNotificationToSingleAsync(
        string token,
        PushNotification pushNotification,
        bool dryRun = false,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var message = new Message { Token = token, Notification = ToFirebaseNotification(pushNotification), };

            await Messaging.SendAsync(message, dryRun, cancellationToken);

            return new PushNotificationSendToSingleResult(Success: true);
        }
        catch (FirebaseMessagingException ex)
        {
            // If token is invalid.
            if (ex.MessagingErrorCode is MessagingErrorCode.InvalidArgument or MessagingErrorCode.Unregistered)
            {
                return new PushNotificationSendToSingleResult(Success: false, TokenIsInvalid: true);
            }

            _logger.Error(
                ex,
                "SendAsync failed with an exception with {ErrorCode} and {MessagingErrorCode} for the token {Token}.",
                ex.ErrorCode,
                ex.MessagingErrorCode,
                token
            );

            return new PushNotificationSendToSingleResult(Success: false, Exception: ex);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "SendAsync failed with an exception for the token {Token}", token);
            return new PushNotificationSendToSingleResult(Success: false, Exception: ex);
        }
    }

    private static Notification ToFirebaseNotification(PushNotification pushNotification)
    {
        return new()
        {
            Title = pushNotification.Title,
            Body = pushNotification.Body,
            ImageUrl = pushNotification.ImageUrl,
        };
    }
}
