﻿namespace AT.Translations.Generator;

using System.IO;
using System.Linq;
using System.Reflection;
using System.Resources;
using System.Resources.NetStandard;

public sealed class ResxTranslationsGenerator : ITranslationsGenerator
{
    public Task GenerateAsync(Type projectAssemblyType)
    {
        // FUTURE: Use Roslyn generators capable of working with the target project's csproj.
        // We would avoid: writing these relative paths, coupling this project with the target projects,
        // and get some extra features.
        var assembly = projectAssemblyType.Assembly;
        string projectName = assembly.GetName().Name!;
        string solutionLocation = Path.GetFullPath(@"..\..\..\..\..\..", assembly.Location);
        string projectLocation = Path.Combine(solutionLocation, projectName);
        string resourcesLocation = Path.Combine(projectLocation, "Resources");
        Directory.CreateDirectory(resourcesLocation);

        GenerateResxFiles(assembly, resourcesLocation);

        GenerateResourceManagersFile(projectAssemblyType, resourcesLocation);

        return Task.CompletedTask;
    }

    private static void GenerateResxFiles(Assembly assembly, string resourcesLocation)
    {
        var resxSourceClasses = assembly.GetTypes().Where(t => t.GetCustomAttribute<ResxSourceAttribute>() != null);
        foreach (var resxSourceClass in resxSourceClasses)
        {
            var resxSourceAttribute = resxSourceClass.GetCustomAttribute<ResxSourceAttribute>()!;
            string resxFileName = resxSourceAttribute.OutputFileName;

            string resxDirectory = Path.Combine(resourcesLocation, resxFileName);
            Directory.CreateDirectory(resxDirectory);

            EnsureEmptyLanguageResxFilesAreCreated(resxDirectory, resxFileName);

            CreateOrUpdateInvariantResxFile(resxSourceClass, resxDirectory, resxFileName);
        }
    }

    // FUTURE: There is definitely a prettier way of writing this generator.
    private static void GenerateResourceManagersFile(Type projectAssemblyType, string resourcesLocation)
    {
        var assembly = projectAssemblyType.Assembly;
        string projectName = assembly.GetName().Name!;
        string fileNamespace = $"{projectAssemblyType.Namespace}.Resources";

        var resxFileRelativePaths = assembly
            .GetTypes()
            .Where(t => t.GetCustomAttribute<ResxSourceAttribute>() != null)
            .Select(t => t.GetCustomAttribute<ResxSourceAttribute>()!)
            .Select(a => a.OutputFileRelativeNamespace);

        var resourceManagerWrappers = resxFileRelativePaths.Select(resxRelativeLocation =>
        {
            string resourceManagerResxLocation = $@"""{fileNamespace}.{resxRelativeLocation}""";
            string resourceManagerAssembly = $@"typeof({projectName}Assembly).Assembly";
            string resourceManager =
                $@"new {nameof(ResourceManager)}({resourceManagerResxLocation}, {resourceManagerAssembly})";

            string assemblyName = projectAssemblyType.Assembly.GetName().Name!;
            string resxLocationAssemblyName = $@"{nameof(ResxLocation.AssemblyName)}: ""{assemblyName}""";
            string resxLocationRelativeLocation =
                $@"{nameof(ResxLocation.ResxRelativeNamespace)}: ""{resxRelativeLocation}""";
            string resxLocation =
                $@"new {nameof(ResxLocation)}({resxLocationAssemblyName}, {resxLocationRelativeLocation})";

            return $@"new {nameof(ResourceManagerWrapper)}({resourceManager}, {resxLocation})";
        });

        string content =
            @$"namespace {fileNamespace};

using System.CodeDom.Compiler;
using System.Resources;
using AT.Translations;

[GeneratedCode(""AT.Translations.Generator.{nameof(ResxTranslationsGenerator)}.cs"", ""1.0.0.0"")]
public static class {projectName}ResourceManagers
{{
    public static {nameof(ResourceManagerWrapper)}[] ResourceManagers = new {nameof(ResourceManagerWrapper)}[]
    {{
        {string.Join($",\r\n        ", resourceManagerWrappers)}
    }};
}}";

        string fileName = $"{projectName}ResourceManagers.generated.cs";
        string filePath = Path.Combine(resourcesLocation, fileName);

        File.WriteAllText(filePath, content);
    }

    private static Dictionary<TranslationId, ResXDataNode> ReadResxFile(string resxFilePath)
    {
        Dictionary<TranslationId, ResXDataNode> result = [];

        if (!File.Exists(resxFilePath))
        {
            return result;
        }

        using var reader = new ResXResourceReader(resxFilePath);

        // The objects returned by the enumerator below are gonna be ResXDataNode instead of string values of the keys.
        // We need this so that we can work with the resx comments.
        reader.UseResXDataNodes = true;

        var enumerator = reader.GetEnumerator();

        while (enumerator.MoveNext())
        {
            var translationId = TranslationId.From(enumerator.Key.ToString()!);
            var dataNode = (ResXDataNode)enumerator.Value!;

            result.Add(translationId, dataNode);
        }

        return result;
    }

    private static void EnsureEmptyLanguageResxFilesAreCreated(string resxDirectory, string resxFileName)
    {
        foreach (var language in Language.List.Where(l => l != Language.Invariant))
        {
            string languageLocaleName = language.LanguageLocaleName.Value;
            string languageOutputFileName = $"{resxFileName}.{languageLocaleName}.resx";
            string languageResxFilePath = Path.Combine(resxDirectory, languageOutputFileName);

            if (!File.Exists(languageResxFilePath))
            {
                using var _ = new ResXResourceWriter(languageResxFilePath);
            }
        }
    }

    private static void CreateOrUpdateInvariantResxFile(Type resxSourceClass, string resxDirectory, string resxFileName)
    {
        string invariantOutputFileName = $"{resxFileName}.resx";
        string invariantResxFilePath = Path.Combine(resxDirectory, invariantOutputFileName);
        using var writer = new ResXResourceWriter(invariantResxFilePath);

        var existingResxNodesByKey = ReadResxFile(invariantResxFilePath);

        var translations = resxSourceClass
            .GetNestedTypes()
            .Where(t => t.IsClass && typeof(ITranslation).IsAssignableFrom(t))
            .ToHashSet();

        foreach (var translation in translations)
        {
            var translationId = TranslationId.From(translation.Name);

            if (!existingResxNodesByKey.TryGetValue(translationId, out var translationNode))
            {
                translationNode = new(name: translationId.Value, value: string.Empty);
            }

            translationNode.Comment = string.Join(" ", translation.GetProperties().Select(p => $"{{{p.Name}}}"));

            writer.AddResource(translationNode);
        }
    }
}
