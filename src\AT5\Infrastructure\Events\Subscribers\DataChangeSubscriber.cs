﻿namespace AT.Infrastructure.Events.Subscribers;

using System;
using AT.Infrastructure.Events.Events;

public class DataChangeSubscriber<TMessage>(Guid _id, Action<TMessage> _messageHandler) : IDataChangeSubscriber
    where TMessage : DataChangeEvent
{
    public Guid Id => _id;

    public bool CanHandle(DataChangeEvent message)
    {
        return message is TMessage;
    }

    public void Handle(DataChangeEvent message)
    {
        _messageHandler((TMessage)message);
    }
}
