﻿namespace AT.Infrastructure.DataAccess;

using System.Threading;
using Ardalis.Specification;
using Ardalis.Specification.EntityFrameworkCore;
using AT.Core.DataAccess;
using Microsoft.EntityFrameworkCore;

public sealed class EfGeneralQuery<TEntity>(
    IDataSource<TEntity> _dataSource,
    ISpecificationEvaluator _specificationEvaluator
) : IGeneralQuery<TEntity>
    where TEntity : class
{
    public EfGeneralQuery(IDataSource<TEntity> dataSource)
        : this(dataSource, SpecificationEvaluator.Default) { }

    public Task<List<TResult>> ListAsync<TResult>(
        ISpecification<TEntity, TResult> specification,
        CancellationToken cancellationToken = default(CancellationToken)
    )
    {
        return _specificationEvaluator.GetQuery(_dataSource.Data, specification).ToListAsync(cancellationToken);
    }

    public Task<TResult?> FirstOrDefaultAsync<TResult>(
        ISpecification<TEntity, TResult> specification,
        CancellationToken cancellationToken = default(CancellationToken)
    )
    {
        return _specificationEvaluator.GetQuery(_dataSource.Data, specification).FirstOrDefaultAsync(cancellationToken);
    }
}
