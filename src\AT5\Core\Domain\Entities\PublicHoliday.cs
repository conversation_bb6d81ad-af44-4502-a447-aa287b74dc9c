﻿namespace AT.Core.Domain.Entities;

public class PublicHoliday
{
    public PublicHolidayId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public DateOnly Date { get; set; }

    public bool IsGlobal { get; set; }

    public DateTimeInterval PaymentInterval { get; set; }

    public bool Working { get; set; }

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
