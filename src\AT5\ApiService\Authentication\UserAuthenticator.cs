﻿namespace AT.ApiService.Authentication;

using System.Globalization;
using AT.Core.Auth;

public class UserAuthenticator(IHttpContextAccessor _httpContextAccessor) : IUserAuthenticator
{
    public UserId? TryGetCurrentUserId()
    {
        var userIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c =>
            c.Type == AristoTelosClaimTypes.UserId
        );
        if (userIdClaim is null)
        {
            return null;
        }

        if (UserId.TryParse(userIdClaim.Value, CultureInfo.InvariantCulture, out var userIdValue))
        {
            return userIdValue;
        }

        return null;
    }
}
