﻿namespace AT.Infrastructure.Jobs.DurableJobs;

using System.Threading.Tasks;
using AT.Core.Jobs.JobService.EmailsService;
using AT.Core.Jobs.JobService.JobWrapper;
using AT.Core.MasterDomain;
using AT.Infrastructure.Jobs.JobService;
using AT.Primitives.Enums;
using AT.Utilities.Logging;
using AT.Utilities.Reflection;
using Quartz;

[DisallowConcurrentExecution]
public class DurableJobSeriesWrapper : IJob
{
    public async Task Execute(IJobExecutionContext context)
    {
        // NOTE: Can this be all moved to constructor?
        var dataMap = context.MergedJobDataMap;
        var jobContext = (DurableJobSeriesWrapperContext)dataMap[nameof(DurableJobSeriesWrapperContext)];

        var resolver = jobContext.OrgServiceProvider;
        var logger = resolver.Resolve<ILogger<DurableJobSeriesWrapper>>();

        var jobSeriesDefinition = jobContext.JobSeriesDefinition;

        try
        {
            foreach (var jobDefinition in jobSeriesDefinition.JobsDefinitions)
            {
                var jobImplementation = jobDefinition.JobImplementation;
                var jobImplName = jobImplementation.GetShortName();
                var fullOrgId = resolver.Resolve<FullOrgId>();

                using var _ = logger.BeginScopeWithProperties(
                    ("Organization", fullOrgId.DatabaseName),
                    ("JobName", jobImplName)
                );

                using var scopedResolver = resolver.BeginScope();

                var jobEmailsServiceFactory = scopedResolver.Resolve<IJobEmailsServiceFactory>();
                var jobEmailsService = jobEmailsServiceFactory.Create(
                    jobDefinition.JobName,
                    jobDefinition.SuccessEmails,
                    jobDefinition.ErrorEmails
                );

                var jobImpl = (IATJobUnsafe)scopedResolver.Resolve(jobImplementation);

                var atJobContext = new ATJobContext(
                    jobDefinition.JobId,
                    jobDefinition.JobName,
                    context.JobDetail.Key.ToString(),
                    context.Trigger.Key.ToString(),
                    jobDefinition.SuccessEmails,
                    jobDefinition.ErrorEmails,
                    jobDefinition.AristoTelosWebUrl,
                    FireTimeParameters.FromQuartzContext(context)
                );

                try
                {
                    var result = await jobImpl.Execute(atJobContext, jobDefinition.JobParameters);

                    switch (result.Type)
                    {
                        case JobResultType.Success:
                        case JobResultType.SuccessWithWarning:
                        case JobResultType.PartialSuccess:
                            await jobEmailsService.SendSuccessEmails();
                            break;
                        case JobResultType.Failure:
                            await jobEmailsService.SendErrorEmails();
                            break;
                        default:
                            break;
                    }
                }
                catch (Exception e)
                {
                    await jobEmailsService.SendErrorEmails(e);
                    throw;
                }
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Failed with an exception");
        }
    }
}
