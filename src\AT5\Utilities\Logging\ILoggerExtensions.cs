﻿namespace AT.Utilities.Logging;

using System.Runtime.CompilerServices;

/// <summary>
/// Provides extension methods for <see cref="ILogger{TCategoryName}"/> to simplify structured logging
/// with automatic inclusion of caller information.
/// </summary>
[System.Diagnostics.CodeAnalysis.SuppressMessage(
    "Design",
    "S107:Methods should not have too many parameters.",
    Justification = "Extra parameters are needed for structured logging and compiler caller info."
)]
public static class ILoggerExtensions
{
    /// <summary>
    /// Logs that contain the most detailed messages. These messages may contain sensitive application data.
    /// These messages are disabled by default and should never be enabled in a production environment.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Trace("Processing request from {Address}", address);</example>
    public static void Trace<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Trace(messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that contain the most detailed messages. These messages may contain sensitive application data.
    /// These messages are disabled by default and should never be enabled in a production environment.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Trace("Processing request from {Address} at {Time}", address, time);</example>
    public static void Trace<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Trace(messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that contain the most detailed messages. These messages may contain sensitive application data.
    /// These messages are disabled by default and should never be enabled in a production environment.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Trace("Processing request from {Address} at {Time} with ID {RequestId}", address, time, requestId);</example>
    public static void Trace<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Trace(messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that are used for interactive investigation during development. These logs should primarily contain
    /// information useful for debugging and have no long-term value.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Debug("Connected to database {DatabaseName}", dbName);</example>
    public static void Debug<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Debug(messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that are used for interactive investigation during development. These logs should primarily contain
    /// information useful for debugging and have no long-term value.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Debug("Connected to {DatabaseName} in {ElapsedMs} ms", dbName, elapsedMs);</example>
    public static void Debug<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Debug(messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that are used for interactive investigation during development. These logs should primarily contain
    /// information useful for debugging and have no long-term value.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Debug("Connected to {DatabaseName} in {ElapsedMs} ms with user {User}", dbName, elapsedMs, user);</example>
    public static void Debug<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Debug(messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that track the general flow of the application. These logs should have long-term value.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Info("User {UserId} logged in", userId);</example>
    public static void Info<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Info(messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that track the general flow of the application. These logs should have long-term value.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Info("User {UserId} logged in from {IpAddress}", userId, ipAddress);</example>
    public static void Info<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Info(messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that track the general flow of the application. These logs should have long-term value.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Info("User {UserId} logged in from {IpAddress} using {Device}", userId, ipAddress, device);</example>
    public static void Info<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Info(messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Warn("Unsupported input format: {Format}", format);</example>
    public static void Warn<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Warn(messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Warn("Unsupported format {Format} for file {FileName}", format, fileName);</example>
    public static void Warn<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Warn(messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Warn("Unsupported format {Format} for file {FileName} uploaded by {User}", format, fileName, user);</example>
    public static void Warn<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Warn(messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Warn(exception, "Unsupported format {Format} for file {FileName} uploaded by {User}", format, fileName, user);</example>
    public static void Warn<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Warn(exception, messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Warn(exception, "Unsupported format {Format} for file {FileName} uploaded by {User}", format, fileName, user);</example>
    public static void Warn<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Warn(exception, messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Warn(exception, "Unsupported format {Format} for file {FileName} uploaded by {User}", format, fileName, user);</example>
    public static void Warn<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Warn(exception, messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Error("Failed to load file {FileName}", fileName);</example>
    public static void Error<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Error(messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Error("Failed to load file {FileName} at {Time}", fileName, time);</example>
    public static void Error<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Error(messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Error("Failed to load file {FileName} at {Time} uploaded by {User}", fileName, time, user);</example>
    public static void Error<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Error(messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Error(exception, "Failed to load file {FileName} at {Time} uploaded by {User}", fileName, time, user);</example>
    public static void Error<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Error(exception, messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Error(exception, "Failed to load file {FileName} at {Time} uploaded by {User}", fileName, time, user);</example>
    public static void Error<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Error(exception, messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Error(exception, "Failed to load file {FileName} at {Time} uploaded by {User}", fileName, time, user);</example>
    public static void Error<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Error(exception, messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Critical("System failure in module {Module}", module);</example>
    public static void Critical<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Critical(messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Critical("Failure in {Module} at {Time}", module, time);</example>
    public static void Critical<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Critical(messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Critical("Failure in {Module} at {Time} triggered by {User}", module, time, user);</example>
    public static void Critical<TCategoryName>(
        this ILogger<TCategoryName> log,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Critical(messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the critical failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Critical(exception, "Failure in {Module} at {Time} triggered by {User}", module, time, user);</example>
    public static void Critical<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Critical(exception, messageFmt, [args1], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the critical failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Critical(exception, "Failure in {Module} at {Time} triggered by {User}", module, time, user);</example>
    public static void Critical<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        object? args2,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Critical(exception, messageFmt, [args1, args2], stop, memberName, sourceFilePath, sourceLineNumber);
    }

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
    /// <param name="log">The logger instance.</param>
    /// <param name="exception">The exception to log, providing details about the critical failure.</param>
    /// <param name="messageFmt">The message template to log.</param>
    /// <param name="args1">An argument to format into the message template.</param>
    /// <param name="args2">The second argument to format into the message template.</param>
    /// <param name="args3">The third argument to format into the message template.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>log.Critical(exception, "Failure in {Module} at {Time} triggered by {User}", module, time, user);</example>
    public static void Critical<TCategoryName>(
        this ILogger<TCategoryName> log,
        Exception? exception,
        string messageFmt,
        object? args1,
        object? args2,
        object? args3,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        log.Critical(exception, messageFmt, [args1, args2, args3], stop, memberName, sourceFilePath, sourceLineNumber);
    }
}
