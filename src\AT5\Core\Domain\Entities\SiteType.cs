﻿namespace AT.Core.Domain.Entities;

using Base;

public class SiteType
{
    public SiteTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public int Level { get; set; }

    public string? Code { get; set; }

    public virtual ICollection<SiteSubtype> SiteSubtypes { get; set; } = new List<SiteSubtype>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
