﻿namespace AT.Infrastructure.Database;

using AT.Core.MasterDomain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

public class MasterDbContext(DbContextOptions<MasterDbContext> _options) : DbContext(_options)
{
    public DbSet<AppConfigurationTable> AppConfigurationTables { get; set; } = null!;

    public DbSet<Organization> Organizations { get; set; } = null!;

    public DbSet<Service> Services { get; set; } = null!;

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        base.ConfigureConventions(configurationBuilder);

        // This will add Vogen convertors, this is a generated extension method
        configurationBuilder.RegisterAllInVogenEfCoreConverters();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AppConfigurationTable>(entity =>
        {
            entity.HasKey(e => e.Name);

            entity.ToTable("AppConfigurationTable");

            entity.Property(e => e.Name).HasMaxLength(32);
            entity.Property(e => e.Value).HasMaxLength(64);
        });

        modelBuilder.Entity<Organization>(entity =>
        {
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.DatabaseName).HasMaxLength(32);
            entity.Property(e => e.HeaderColor).HasMaxLength(20);
            entity.Property(e => e.HeaderTitle).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(32);
            entity.Property(e => e.UrlId).HasMaxLength(32);
        });

        modelBuilder.Entity<Service>(entity =>
        {
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.OrganizationId, "IX_FK_OrganizationService");

            entity.Property(e => e.Token).HasMaxLength(50);

            entity
                .HasOne(d => d.Organization)
                .WithMany(p => p.Services)
                .HasForeignKey(d => d.OrganizationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrganizationService");
        });
    }
}
