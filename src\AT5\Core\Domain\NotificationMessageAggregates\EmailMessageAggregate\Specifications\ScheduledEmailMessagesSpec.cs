﻿namespace AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;

public class ScheduledEmailMessagesSpec : Specification<EmailMessage>
{
    public ScheduledEmailMessagesSpec(DateTime untilExclusive, int? batchSize = null)
    {
        Query
            .Where(x => x.SendTime < untilExclusive)
            .Include(pn => pn.EmailMessageRecipients)
            .Include(pn => pn.EmailMessageAttachments);

        if (batchSize > 0)
        {
            Query.Take(batchSize.Value);
        }
    }
}
