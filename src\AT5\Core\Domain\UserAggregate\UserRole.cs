﻿namespace AT.Core.Domain.UserAggregate;

using Primitives.Enums;

public class UserRole
{
    public UserRoleId Id { get; set; }

    public RoleId RoleId { get; set; }

    public UserId UserId { get; set; }

    public RoleAssignmentType AssignmentType { get; set; }

    public TimeValidity Validity { get; set; }

    public SiteId? SiteId { get; set; }

    public TeamId? TeamId { get; set; }

    public UserId? SubordinateUserId { get; set; }

    public bool Synchronized { get; set; }

    public virtual Role Role { get; set; } = null!;

    public virtual Site? Site { get; set; }

    public virtual User? SubordinateUser { get; set; }

    public virtual Team? Team { get; set; }

    public virtual User User { get; set; } = null!;
}
