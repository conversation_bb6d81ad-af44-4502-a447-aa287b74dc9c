﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;

public class AuditRelationship
{
    public AuditRelationshipId Id { get; set; }

    public string FirstEntityName { get; set; } = null!;

    public int FirstEntityKey { get; set; }

    public string SecondEntityName { get; set; } = null!;

    public int SecondEntityKey { get; set; }

    public bool Added { get; set; }

    public DateTime TimeStamp { get; set; }

    public UserId UserId { get; set; }
}
