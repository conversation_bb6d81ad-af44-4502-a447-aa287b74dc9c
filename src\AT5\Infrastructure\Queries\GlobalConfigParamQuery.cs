﻿namespace AT.Infrastructure.Queries;

using System.Threading;
using System.Threading.Tasks;
using AT.Core.Domain.ConfigurationParameterAggregate;
using AT.Core.Domain.ConfigurationParameterAggregate.Queries;
using AT.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;

public sealed class GlobalConfigParamQuery(IDataSource<DefaultConfigParameter> _defaultConfigParamDataSource)
    : IGlobalConfigParamQuery
{
    public Task<string?> TryGetValue(string name, CancellationToken cancellationToken = default)
    {
        return _defaultConfigParamDataSource
            .Data.Where(x => x.Name == name)
            .Select(x => x.DefaultValue)
            .FirstOrDefaultAsync(cancellationToken);
    }
}
