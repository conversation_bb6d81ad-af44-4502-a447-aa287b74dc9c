﻿namespace AT.Primitives.Enums;

/// <summary>
/// Defines how time off interacts with shifts.
/// </summary>
public enum TimeOffShiftInteraction
{
    /// <summary>
    /// Time off is a part of the shift.
    /// </summary>
    InShift = 0,

    /// <summary>
    /// Time off overlaps shift or its part but cannot exist without a shift.
    /// </summary>
    OnShift = 1,

    /// <summary>
    /// Time off is defined for a date time period regardless of shifts.
    /// </summary>
    Period = 2,

    /// <summary>
    /// Time off is a part of the shift or overlaps shift
    /// </summary>
    InShiftOrOnShift = 3
}

/// <summary>
/// Extension methods for TimeOffShiftInteraction enum
/// </summary>
public static class TimeOffShiftInteractionExtensions
{
    /// <summary>
    /// Checks if interaction is allowed on shift
    /// </summary>
    /// <param name="shiftInteraction"></param>
    /// <returns></returns>
    public static bool AllowsOnShift(this TimeOffShiftInteraction shiftInteraction)
    {
        return shiftInteraction is (TimeOffShiftInteraction.OnShift or TimeOffShiftInteraction.InShiftOrOnShift);
    }
}
