﻿namespace AT.UseCases.Jobs;

using System.Threading.Tasks;
using AT.Core.Jobs.JobService.JobWrapper;
using AT.Primitives.Enums;
using AT.Utilities.Logging;

public class ModernNullJob(ILogger<ModernNullJob> _logger) : ATJobBase<ModernNullJobParams>
{
    public override Task<IATJobResult> Execute(IATJobContext context, ModernNullJobParams parameters)
    {
        _logger.Info("Executing TestJob.");

        _logger.Info("Message: {Message}.", parameters.Message);

        if (parameters.Throw)
        {
            throw new InvalidOperationException("Test exception triggered by job parameters. Throw == true.");
        }

        return Task.FromResult(MakeResult(JobResultType.Success));
    }
}
