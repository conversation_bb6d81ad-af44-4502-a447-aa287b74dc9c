﻿namespace AT.ApiService.DependencyInjection;

using System.Globalization;
using AT.ApiService.Authentication;
using Autofac.Multitenant;
using Microsoft.AspNetCore.Http;

/// <summary>
/// BEWARE: This solution is probably not robust enough, and needs to be changed.
/// </summary>
internal class UserClaimsTenantIdentificationStrategy(IHttpContextAccessor _httpContextAccessor)
    : ITenantIdentificationStrategy
{
    public bool TryIdentifyTenant(out object? tenantId)
    {
        var tenantClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c =>
            c.Type == AristoTelosClaimTypes.TenantId
        );

        tenantId = null;
        if (
            tenantClaim is not null
            && OrganizationId.TryParse(tenantClaim.Value, CultureInfo.InvariantCulture, out var organizationId)
        )
        {
            tenantId = organizationId;
            return true;
        }

        return tenantId is not null;
    }
}
