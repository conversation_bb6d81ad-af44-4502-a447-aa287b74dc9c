﻿namespace AT.Infrastructure.DependencyInjection.Extensions;

using AT.Infrastructure.Utilities.Parsing;
using AT.Utilities.Parsing;
using AT.Utilities.Time;
using Autofac;
using Autofac.Multitenant;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Quartz;

// FUTURE: This class will grow. For now, there are only few extension methods but at some point, we should move certain methods
// into separate classes, maybe like DataAccessDependencyInjection that would contain all data access related services.
public static class DependencyInjectionExtensions
{
    public static ContainerBuilder AddCurrentTime(this ContainerBuilder containerBuilder)
    {
        containerBuilder.RegisterInstance(TimeProvider.System).As<TimeProvider>().ExternallyOwned();
        containerBuilder.RegisterType<SystemTimeProvider>().As<ITimeProvider>().SingleInstance();

        return containerBuilder;
    }

    public static ContainerBuilder AddCustomMultitenantServiceProviders(this ContainerBuilder containerBuilder)
    {
        containerBuilder.RegisterType<AppServiceProvider>().As<IAppServiceProvider>().AsSelf().SingleInstance();
        containerBuilder.RegisterType<OrgServiceProvider>().As<IOrgServiceProvider>().InstancePerTenant();
        containerBuilder
            .RegisterType<OrgServiceProviderScope>()
            .As<IOrgServiceProviderScope>()
            .InstancePerLifetimeScope();

        return containerBuilder;
    }

    // FUTURE: Use configuration for parser-specific settings?
    public static IServiceCollection AddParsers(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        return serviceCollection.AddSingleton<IJsonParser, SystemTextJsonParser>();
    }
}
