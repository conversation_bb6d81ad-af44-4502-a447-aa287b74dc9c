﻿namespace AT.Translations.Formatting.Time;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class TimeSpanFormattingAttribute(TimeSpanFormattingType type) : Attribute
{
    public TimeSpanFormattingType Type { get; init; } = type;
}

// FUTURE: This shouldn't be in the Translations project.
/// <summary>
/// Locale-specific type of <see cref="TimeSpan"/> formatting.
/// </summary>
public enum TimeSpanFormattingType
{
    /// <summary>
    /// Uses the standard (ToString("c")) formatting. However, if the seconds and milliseconds are zero,
    /// then they are not shown in the result at all.
    /// </summary>
    Standard,

    TotalHoursAndMinutes,
}
