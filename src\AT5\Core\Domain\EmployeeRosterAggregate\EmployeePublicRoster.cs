﻿namespace AT.Core.Domain.EmployeeRosterAggregate;

using AT.Core.Domain.UserAggregate;

public class EmployeePublicRoster
{
    public EmployeePublicRosterId Id { get; set; }

    public SiteId SiteId { get; set; }

    public UserId EmployeeId { get; set; }

    public DateInterval Interval { get; set; }

    public UserId AuthorId { get; set; }

    public DateTime Timestamp { get; set; }

    public RosterPublicityType Type { get; set; }

    public bool EmptyPublish { get; set; }

    public virtual User Author { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
