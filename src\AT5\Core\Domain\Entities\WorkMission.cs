﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;

public class WorkMission
{
    public WorkMissionId Id { get; set; }

    public DateTime MissionStart { get; set; }

    public DateTime? MissionEnd { get; set; }

    public double Volume { get; set; }

    public long? SystemId { get; set; }

    public string? SystemCode { get; set; }

    public WorkMissionTypeId TypeId { get; set; }

    public UserId EmployeeId { get; set; }

    public QueueId QueueId { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Queue Queue { get; set; } = null!;

    public virtual WorkMissionType Type { get; set; } = null!;
}
