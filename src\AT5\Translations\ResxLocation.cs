﻿namespace AT.Translations;

/// <param name="AssemblyName">The name of the assembly that contains the resx files.</param>
/// <param name="ResxRelativeNamespace">Relative namespace of the resx files relative to the Resources folder.<br/>
/// Example: 'Global.Global' for the Global.resx in AT5/SharedResources/Resources/Global/Global.resx.</param>
public record struct ResxLocation(string AssemblyName, string ResxRelativeNamespace);
