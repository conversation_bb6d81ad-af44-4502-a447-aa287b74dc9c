﻿namespace AT.NotificationsService.PushNotifications;

using System.Diagnostics.CodeAnalysis;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.MasterDomain;
using AT.NotificationsService;
using AT.NotificationsService.General;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Utilities.Logging;
using Microsoft.Extensions.Options;

public sealed class PushNotificationLogger : IPushNotificationLogger
{
    private readonly OrganizationInfo _organizationInfo;
    private readonly ILogger<DailyFileLogger> _loggerForDailyFileLogger;
    private DailyFileLogger _dailyFileLogger;

    /// <summary>
    /// When CsvLogsDirectory changes, we want to re-create dailyFileLogger with the new path. However, dailyFilterLogger
    /// might be used at that moment, so we cannot dispose it. Therefore, we use locking.
    /// </summary>
    private readonly object _dailyFileLoggerLock = new();

    public PushNotificationLogger(
        IOptionsMonitor<PushNotificationConfig> pushNotificationConfigMonitor,
        OrganizationInfo organizationInfo,
        ILogger<DailyFileLogger> loggerForDailyFileLogger
    )
    {
        _organizationInfo = organizationInfo;
        _loggerForDailyFileLogger = loggerForDailyFileLogger;

        UseCsvLogsDirectory(pushNotificationConfigMonitor.CurrentValue.CsvLogsDirectory);

        pushNotificationConfigMonitor.OnChange(x => UseCsvLogsDirectory(x.CsvLogsDirectory));
    }

    [MemberNotNull(nameof(_dailyFileLogger))]
    public void UseCsvLogsDirectory(string csvLogsDirectory)
    {
        lock (_dailyFileLoggerLock)
        {
            _dailyFileLogger?.Dispose();

            var orgCsvLogsDirectory = Path.GetFullPath(
                Path.Combine(csvLogsDirectory, _organizationInfo.Name),
                AppContext.BaseDirectory
            );

            _dailyFileLogger = new DailyFileLogger(
                _loggerForDailyFileLogger,
                orgCsvLogsDirectory,
                "push_notifications_%DATE%.csv",
                _onFileOpen: LogHeader
            );
        }
    }

    private static void LogHeader(StreamWriter writer)
    {
        if (writer.BaseStream.Position == 0)
        {
            writer.WriteLine(
                "Time;Event;EventReason;Id;Type;EntityId;EventParameters;Title;Body;ImageUrl;Generated;SendTime;UserIds;Tokens"
            );
        }
    }

    public void LogPushNotificationTokensLoad(PushNotification message, IEnumerable<string> tokens)
    {
        LogPushNotificationEvent(
            message,
            "TokensLoaded",
            recipients: message.PushNotificationRecipients,
            tokens: tokens
        );
    }

    public void LogPushNotificationSendPartiallySuccessful(PushNotification message)
    {
        LogPushNotificationEvent(message, "PushNotificationSendPartiallySuccessful");
    }

    public void LogMessageLoad(PushNotification message)
    {
        LogPushNotificationEvent(message, "Loaded", recipients: message.PushNotificationRecipients);
    }

    public void LogMessageProcessed(PushNotification message)
    {
        LogPushNotificationEvent(message, "Processed");
    }

    public void LogMessageSendSuccessful(PushNotification message)
    {
        LogPushNotificationEvent(message, "SendSuccessful");
    }

    public void LogMessageSendSuccessfulForRecipients(
        PushNotification message,
        IEnumerable<PushNotificationRecipient> recipients
    )
    {
        LogPushNotificationEvent(message, "SendSuccessfulForRecipients", recipients: recipients);
    }

    public void LogMessageSendFailed(PushNotification message, string? eventReason = null)
    {
        LogPushNotificationEvent(
            message,
            "SendFailed",
            recipients: message.PushNotificationRecipients,
            eventReason: eventReason
        );
    }

    public void LogMessageSendFailedForRecipients(
        PushNotification message,
        IEnumerable<PushNotificationRecipient> recipients,
        string? eventReason = null
    )
    {
        LogPushNotificationEvent(message, "SendFailedForRecipients", recipients: recipients, eventReason: eventReason);
    }

    public void LogMessageExpired(PushNotification message)
    {
        LogPushNotificationEvent(message, "Expired", recipients: message.PushNotificationRecipients);
    }

    public void LogMessageFilteredByRateLimiting(PushNotification message)
    {
        LogPushNotificationEvent(message, "FilteredByRateLimiting", recipients: message.PushNotificationRecipients);
    }

    public void LogMessageRecipientsFilteredByRateLimiting(
        PushNotification message,
        IEnumerable<PushNotificationRecipient> recipients
    )
    {
        LogPushNotificationEvent(message, "RecipientsFilteredByRateLimiting", recipients: recipients);
    }

    public void LogPushNotificationEvent(
        PushNotification pushNotification,
        string logEvent,
        IEnumerable<PushNotificationRecipient>? recipients = null,
        IEnumerable<string>? tokens = null,
        string? eventReason = null
    )
    {
        var now = DateTime.Now.ToString("o");
        var logEventStr = logEvent.ToString();
        var eventReasonStr = eventReason ?? string.Empty;
        var id = pushNotification.Id;
        var typeStr = pushNotification.Type.ToString();
        var entityId = pushNotification.EntityId;
        var eventParams = pushNotification.EventParameters;
        var title = pushNotification.Title;
        var body = pushNotification.Body;
        var imageUrl = pushNotification.ImageUrl;
        var generated = pushNotification.Generated.ToString("o");
        var sendTime = pushNotification.SendTime?.ToString("o") ?? string.Empty;
        var userIds = string.Join(", ", (recipients ?? []).Select(r => r.UserId));
        var tokensStr = string.Join(", ", tokens ?? []);

        var record =
            $"{now};{logEventStr};{eventReasonStr};{id};{typeStr};{entityId};{eventParams};{title};{body};{imageUrl};{generated};{sendTime};{userIds};{tokensStr}"
                .Replace("\r", "\\r")
                .Replace("\n", "\\n"); // Each record in CSV must always be a single line.

        lock (_dailyFileLoggerLock)
        {
            _dailyFileLogger.WriteLine(record);
        }
    }

    public void Dispose()
    {
        _dailyFileLogger.Dispose();
    }
}
