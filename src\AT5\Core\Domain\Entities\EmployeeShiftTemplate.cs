﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeeShiftTemplate
{
    public EmployeeShiftTemplateId Id { get; set; }

    public int? Min { get; set; }

    public int? Max { get; set; }

    public double? Priority { get; set; }

    public ShiftTemplateId ShiftTemplateId { get; set; }

    public UserId EmployeeId { get; set; }

    public SiteId SiteId { get; set; }

    public Validity Validity { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual ShiftTemplate ShiftTemplate { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
