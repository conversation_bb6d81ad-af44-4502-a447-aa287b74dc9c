﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;

public class WorkOrderProductivity
{
    public WorkOrderProductivityId Id { get; set; }

    public SiteId SiteId { get; set; }

    public DateTimeInterval Interval { get; set; }

    public DoubleValueChange ProductivityChange { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<Queue> Queues { get; set; } = new List<Queue>();
}
