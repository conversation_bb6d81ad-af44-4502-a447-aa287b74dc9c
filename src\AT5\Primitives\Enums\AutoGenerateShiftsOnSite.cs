﻿namespace AT.Primitives.Enums;

public enum AutoGenerateShiftsOnSite
{
    /// <summary>
    /// Not specified, inherit autogenerate option from parent site.
    /// </summary>
    None = 0,

    /// <summary>
    /// Yes for employees directly in the site. This setting will not affect subsites.
    /// Subsites inherit settings from the parent of this site's parent.
    /// </summary>
    Yes = 1,

    /// <summary>
    /// No for employees directly in the site. This setting will not affect subsites.
    /// Subsites inherit settings from the parent of this site's parent.
    /// </summary>
    No = 2,

    /// <summary>
    /// Yes for employees directly in this site and subsites.
    /// Setting in subsites can be overridden, closest setting in ancestor takes precedence.
    /// </summary>
    YesIncludingSubsites = 3,

    /// <summary>
    /// No for employees directly in this site and subsites.
    /// Setting in subsites can be overridden, closest setting in ancestor takes precedence.
    /// </summary>
    NoIncludingSubsites = 4,
}

public static class AutoGenerateShiftsOnSiteExtensions
{
    public static AutoGenerateShifts ToAutoGenerateShifts(this AutoGenerateShiftsOnSite autoGenerateShiftsOnSite)
    {
        switch (autoGenerateShiftsOnSite)
        {
            case AutoGenerateShiftsOnSite.None:
                return AutoGenerateShifts.None;
            case AutoGenerateShiftsOnSite.Yes:
                return AutoGenerateShifts.Yes;
            case AutoGenerateShiftsOnSite.No:
                return AutoGenerateShifts.No;
            case AutoGenerateShiftsOnSite.YesIncludingSubsites:
                return AutoGenerateShifts.Yes;
            case AutoGenerateShiftsOnSite.NoIncludingSubsites:
                return AutoGenerateShifts.No;
        }

        return AutoGenerateShifts.None;
    }

    public static bool IsInherited(this AutoGenerateShiftsOnSite autoGenerateShiftsOnSite)
    {
        return autoGenerateShiftsOnSite == AutoGenerateShiftsOnSite.YesIncludingSubsites
            || autoGenerateShiftsOnSite == AutoGenerateShiftsOnSite.NoIncludingSubsites;
    }
}
