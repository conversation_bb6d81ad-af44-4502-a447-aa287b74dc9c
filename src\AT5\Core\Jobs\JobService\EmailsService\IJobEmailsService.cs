﻿namespace AT.Core.Jobs.JobService.EmailsService;

using System;
using System.Collections.Generic;

public interface IJobEmailsService
{
    Task SendSuccessEmails(
        string? message = null,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    );

    Task SendSuccessEmails(
        string subject,
        string body,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    );

    Task SendErrorEmails(
        string? message = null,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    );

    Task SendErrorEmails(
        string subject,
        string body,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    );

    Task SendErrorEmails(Exception e);
}
