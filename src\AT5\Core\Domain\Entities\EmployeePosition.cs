﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeePosition
{
    public EmployeePositionId Id { get; set; }

    public UserId EmployeeId { get; set; }

    public Validity Validity { get; set; }

    public PositionId PositionId { get; set; }

    public SiteId SiteId { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Position Position { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
