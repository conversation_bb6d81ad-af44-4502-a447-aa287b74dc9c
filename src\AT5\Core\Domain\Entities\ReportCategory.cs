﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;

public class ReportCategory
{
    public ReportCategoryId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public int Rank { get; set; }

    public virtual ICollection<Report> Reports { get; set; } = new List<Report>();
}
