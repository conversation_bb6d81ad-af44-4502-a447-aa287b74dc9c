﻿namespace AT.Translations.Formatting.Time;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class TimeFormattingAttribute(TimeFormattingType type) : Attribute
{
    public TimeFormattingType Type { get; init; } = type;
}

// FUTURE: This shouldn't be in the Translations project.
/// <summary>
/// Locale-specific type of <see cref="DateOnly"/> formatting.
/// </summary>
public enum TimeFormattingType
{
    /// <summary>
    /// E.g. "14:25:07" or "02:25:07 PM".
    /// </summary>
    Standard,

    /// <summary>
    /// E.g. "19:25" or "07:25 PM".
    /// </summary>
    WithoutSeconds,

    /// <summary>
    /// E.g. "7:25" or "7:25 AM".
    /// </summary>
    Short,
}
