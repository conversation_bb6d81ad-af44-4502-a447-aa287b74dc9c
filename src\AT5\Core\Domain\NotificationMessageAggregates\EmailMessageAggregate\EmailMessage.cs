﻿namespace AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;

using AT.Core.Domain.NotificationMessageAggregates;
using Primitives.Enums;

public class EmailMessage : IAggregateRoot, INotificationMessage
{
    public long Id { get; set; }

    public required Guid TrackingId { get; set; }

    public EmailMessageType EmailType { get; set; }

    public int? EntityId { get; set; }

    public string? EventParameters { get; set; }

    public string Subject { get; set; } = null!;

    public string Body { get; set; } = null!;

    public bool BodyIsHtml { get; set; }

    public required DateTime Generated { get; set; }

    public DateTime? SendTime { get; set; }

    public virtual ICollection<EmailMessageAttachment> EmailMessageAttachments { get; set; } =
        new List<EmailMessageAttachment>();

    public virtual ICollection<EmailMessageRecipient> EmailMessageRecipients { get; set; } =
        new List<EmailMessageRecipient>();

    public IEnumerable<INotificationMessageRecipient> Recipients => EmailMessageRecipients;

    public override string ToString()
    {
        var basic =
            $"Id: {Id}, EmailType: {EmailType}, BodyIsHtml: {BodyIsHtml}, EntityId: {EntityId}, EventParameters: {EventParameters}, Generated: {Generated}, SendTime: {SendTime}, Subject: {Subject}, Body: {Body}";

        var recipients = string.Join(
            ", ",
            EmailMessageRecipients.Select(r =>
                $"{{ Address: {r.Address}, RecipientType: {r.RecipientType}{(r.UserId.HasValue ? $", UserId: {r.UserId}" : string.Empty)} }}"
            )
        );

        var attachments = string.Empty;
        if (EmailMessageAttachments?.Count > 0)
        {
            attachments = string.Join(
                ", ",
                EmailMessageAttachments.Select(a =>
                    $"{{ FileName: {a.FileName}, {(string.IsNullOrEmpty(a.ContentType) ? string.Empty : $", ContentType: {a.ContentType}")} }}"
                )
            );
        }

        return $"{{ {basic}{Environment.NewLine}\tRecipients: [{recipients}]{(string.IsNullOrEmpty(attachments) ? string.Empty : $"{Environment.NewLine}\tAttachments: {attachments}")} }}";
    }

    public EmailMessage Clone(bool withRecipients = true, bool withAttachments = true)
    {
        return new EmailMessage()
        {
            Id = Id,
            Body = Body,
            BodyIsHtml = BodyIsHtml,
            EmailMessageAttachments = withAttachments ? EmailMessageAttachments.Select(a => a.Clone()).ToList() : [],
            EmailMessageRecipients = withRecipients ? EmailMessageRecipients.Select(r => r.Clone()).ToList() : [],
            EmailType = EmailType,
            EntityId = EntityId,
            EventParameters = EventParameters,
            Generated = Generated,
            SendTime = SendTime,
            Subject = Subject,
            TrackingId = TrackingId,
        };
    }
}
