﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class Filter2
{
    public Filter2Id Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string Value { get; set; } = null!;

    public bool Template { get; set; }

    public FilterType Type { get; set; }

    public int? SubType { get; set; }

    public virtual ICollection<LicenceRule> LicenceRules { get; set; } = new List<LicenceRule>();

    public virtual ICollection<PropertyFilter> PropertyFilters { get; set; } = new List<PropertyFilter>();

    public virtual ICollection<ReportColumnField> ReportColumnFields { get; set; } = new List<ReportColumnField>();

    public virtual ICollection<RequestTypeFilter> RequestTypeFilters { get; set; } = new List<RequestTypeFilter>();

    public virtual ICollection<RosterItemPartTypeFilter> RosterItemPartTypeFilters { get; set; } =
        new List<RosterItemPartTypeFilter>();

    public virtual ICollection<RosteringRuleFilters2> RosteringRuleFilters2s { get; set; } =
        new List<RosteringRuleFilters2>();

    public virtual ICollection<ShiftTemplateFilter> ShiftTemplateFilters { get; set; } =
        new List<ShiftTemplateFilter>();
}
