﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class AuthenticationToken
{
    public AuthenticationTokenId Id { get; set; }

    public UserId UserId { get; set; }

    public AuthenticationTokenType Type { get; set; }

    public int SubType { get; set; }

    public string TokenData { get; set; } = null!;

    public DateTime? ValidityStart { get; set; }

    public DateTime? ValidityEnd { get; set; }

    public virtual User User { get; set; } = null!;
}
