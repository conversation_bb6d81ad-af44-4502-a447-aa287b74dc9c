﻿namespace AT.Infrastructure.Database.Infrastructure;

using System.Linq.Expressions;
using AT.Core.Domain.ComplexTypes;
using AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal static class EntityTypeBuilderExtensions
{
    private const string dateTimeDataType = "datetime";

    /// <summary>
    /// Configures a property of type <see cref="DateInterval"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="DateInterval"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start date column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end date column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasDateInterval<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, DateInterval>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<DateInterval> interval) =>
            {
                // Configure the Start property with a datetime column type and optional custom column order.
                // The column should always contain a valid date and is exposed as such.
                interval
                    .Property(i => i.Start)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(startColumnName)
                    .HasColumnOrder(iota)
                    .HasConversion(
                        dateOnly => dateOnly.ToDateTime(TimeOnly.MinValue),
                        dateTime => DateOnly.FromDateTime(dateTime)
                    )
                    .IsRequired();

                // Configure the End property with a datetime column type and optional custom column order.
                // The column should always contain a valid date and is exposed as such.
                interval
                    .Property(i => i.End)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(endColumnName)
                    .HasColumnOrder(iota)
                    .HasConversion(
                        dateOnly => dateOnly.ToDateTime(TimeOnly.MinValue),
                        dateTime => DateOnly.FromDateTime(dateTime)
                    )
                    .IsRequired();
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="DateTimeInterval"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="DateTimeInterval"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start datetime column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end datetime column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasDateTimeInterval<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, DateTimeInterval>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<DateTimeInterval> interval) =>
            {
                // Configure the Start property with a datetime column type and optional custom column order.
                interval
                    .Property(i => i.Start)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(startColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the End property with a datetime column type and optional custom column order.
                interval
                    .Property(i => i.End)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(endColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="OpenDateInterval"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="OpenDateInterval"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start date column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end date column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasOpenDateInterval<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, OpenDateInterval>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<OpenDateInterval> interval) =>
            {
                // Configure the Start property with an optional datetime column type and optional custom column order.
                // The column should always contain a valid date and is exposed as such.
                interval
                    .Property(i => i.Start)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(startColumnName)
                    .HasColumnOrder(iota)
                    .HasConversion(
                        dateOnly => dateOnly.HasValue ? dateOnly.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null,
                        dateTime => dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : (DateOnly?)null
                    );

                // Configure the End property with an optional datetime column type and optional custom column order.
                // The column should always contain a valid date and is exposed as such.
                interval
                    .Property(i => i.End)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(endColumnName)
                    .HasColumnOrder(iota)
                    .HasConversion(
                        dateOnly => dateOnly.HasValue ? dateOnly.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null,
                        dateTime => dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : (DateOnly?)null
                    );
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="OpenDateTimeInterval"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="OpenDateTimeInterval"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start datetime column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end datetime column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasOpenDateTimeInterval<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, OpenDateTimeInterval>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<OpenDateTimeInterval> interval) =>
            {
                // Configure the Start property with an optional datetime column type and optional custom column order.
                interval
                    .Property(i => i.Start)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(startColumnName)
                    .HasColumnOrder(iota);

                // Configure the End property with an optional datetime column type and optional custom column order.
                interval
                    .Property(i => i.End)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnName(endColumnName)
                    .HasColumnOrder(iota);
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="Validity"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="Validity"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start date column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end date column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <param name="invalidColumnName">The custom name for the invalid bit column. Defaults to "{GetPropertyName(propertyExpression)}_Invalid".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasValidity<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, Validity>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null,
        string? invalidColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";
        invalidColumnName ??= $"{GetPropertyName(propertyExpression)}_Invalid";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<Validity> validity) =>
            {
                // Configure the Interval complex property.
                validity.ComplexProperty(
                    v => v.Interval,
                    (ComplexPropertyBuilder<OpenDateInterval> interval) =>
                    {
                        // Configure the Start property with an optional datetime column type and optional custom column order.
                        // The column should always contain a valid date and is exposed as such.
                        interval
                            .Property(i => i.Start)
                            .HasColumnType(dateTimeDataType)
                            .HasColumnName(startColumnName)
                            .HasColumnOrder(iota)
                            .HasConversion(
                                dateOnly =>
                                    dateOnly.HasValue ? dateOnly.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null,
                                dateTime => dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : (DateOnly?)null
                            );

                        // Configure the End property with an optional datetime column type and optional custom column order.
                        // The column should always contain a valid date and is exposed as such.
                        interval
                            .Property(i => i.End)
                            .HasColumnType(dateTimeDataType)
                            .HasColumnName(endColumnName)
                            .HasColumnOrder(iota)
                            .HasConversion(
                                dateOnly =>
                                    dateOnly.HasValue ? dateOnly.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null,
                                dateTime => dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : (DateOnly?)null
                            );
                    }
                );

                // Configure the IsInvalid with bit column type and optional custom column order.
                validity
                    .Property(v => v.IsInvalid)
                    .HasColumnType("bit")
                    .HasColumnName(invalidColumnName)
                    .HasColumnOrder(iota);
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="TimeValidity"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="TimeValidity"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start datetime column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end datetime column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <param name="invalidColumnName">The custom name for the invalid bit column. Defaults to "{GetPropertyName(propertyExpression)}_Invalid".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasTimeValidity<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, TimeValidity>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null,
        string? invalidColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";
        invalidColumnName ??= $"{GetPropertyName(propertyExpression)}_Invalid";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<TimeValidity> validity) =>
            {
                // Configure the Interval complex property.
                validity.ComplexProperty(
                    v => v.Interval,
                    (ComplexPropertyBuilder<OpenDateTimeInterval> interval) =>
                    {
                        // Configure the Start property with an optional datetime column type and optional custom column order.
                        interval
                            .Property(i => i.Start)
                            .HasColumnType(dateTimeDataType)
                            .HasColumnName(startColumnName)
                            .HasColumnOrder(iota);

                        // Configure the End property with an optional datetime column type and optional custom column order.
                        interval
                            .Property(i => i.End)
                            .HasColumnType(dateTimeDataType)
                            .HasColumnName(endColumnName)
                            .HasColumnOrder(iota);
                    }
                );

                // Configure the IsInvalid with bit column type and optional custom column order.
                validity
                    .Property(v => v.IsInvalid)
                    .HasColumnType("bit")
                    .HasColumnName(invalidColumnName)
                    .HasColumnOrder(iota);
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="TimeInterval"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="TimeValidity"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start time column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="durationColumnName">The custom name for the duration in seconds int column. Defaults to "{GetPropertyName(propertyExpression)}_DurationInSeconds".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasTimeInterval<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, TimeInterval>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? durationColumnName = null
    )
        where T : class
    {
        startColumnName = $"{GetPropertyName(propertyExpression)}_Start";
        durationColumnName = $"{GetPropertyName(propertyExpression)}_DurationInSeconds";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<TimeInterval> interval) =>
            {
                // Configure the Start property with time column type and optional custom column order.
                interval.Property(i => i.Start).HasColumnName(startColumnName).HasColumnOrder(iota).IsRequired();

                // Configure the Duration property with int column type containing the duration in seconds and optional custom column order.
                interval
                    .Property(i => i.Duration)
                    .HasColumnType("int")
                    .HasColumnName(durationColumnName)
                    .HasColumnOrder(iota)
                    .HasConversion(x => x.TotalSeconds, x => TimeSpan.FromSeconds(x))
                    .IsRequired();
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="Appearance"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="Appearance"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="borderWidthColumnName">The custom name for the border width column. Defaults to "{GetPropertyName(propertyExpression)}_BorderWidth".</param>
    /// <param name="borderTypeColumnName">The custom name for the border type column. Defaults to "{GetPropertyName(propertyExpression)}_BorderType".</param>
    /// <param name="foreColorColumnName">The custom name for the fore color column. Defaults to "{GetPropertyName(propertyExpression)}_ForeColor".</param>
    /// <param name="borderColorColumnName">The custom name for the border color column. Defaults to "{GetPropertyName(propertyExpression)}_BorderColor".</param>
    /// <param name="backColorColumnName">The custom name for the back color column. Defaults to "{GetPropertyName(propertyExpression)}_BackColor".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasAppearance<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, Appearance>> propertyExpression,
        Iota? iota,
        string? borderWidthColumnName = null,
        string? borderTypeColumnName = null,
        string? foreColorColumnName = null,
        string? borderColorColumnName = null,
        string? backColorColumnName = null
    )
        where T : class
    {
        borderWidthColumnName ??= $"{GetPropertyName(propertyExpression)}_BorderWidth";
        borderTypeColumnName ??= $"{GetPropertyName(propertyExpression)}_BorderType";
        foreColorColumnName ??= $"{GetPropertyName(propertyExpression)}_ForeColor";
        borderColorColumnName ??= $"{GetPropertyName(propertyExpression)}_BorderColor";
        backColorColumnName ??= $"{GetPropertyName(propertyExpression)}_BackColor";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<Appearance> appearance) =>
            {
                // Configure the BorderWidth property with an optional custom column order.
                appearance
                    .Property(a => a.BorderWidth)
                    .HasColumnName(borderWidthColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the BorderType property with an optional custom column order.
                appearance
                    .Property(a => a.BorderType)
                    .HasColumnName(borderTypeColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the ForeColor string property with fixed length of 7 an optional custom column order.
                // FUTURE: Possibly add conversion to a Color type here.
                appearance
                    .Property(a => a.ForeColor)
                    .HasColumnName(foreColorColumnName)
                    .HasColumnOrder(iota)
                    .HasMaxLength(7)
                    .IsFixedLength()
                    .IsRequired();

                // Configure the BorderColor string property with fixed length of 7 an optional custom column order.
                // FUTURE: Possibly add conversion to a Color type here.
                appearance
                    .Property(a => a.BorderColor)
                    .HasColumnName(borderColorColumnName)
                    .HasColumnOrder(iota)
                    .HasMaxLength(7)
                    .IsFixedLength()
                    .IsRequired();

                // Configure the BackColor string property with fixed length of 7 an optional custom column order.
                // FUTURE: Possibly add conversion to a Color type here.
                appearance
                    .Property(a => a.BackColor)
                    .HasColumnName(backColorColumnName)
                    .HasColumnOrder(iota)
                    .HasMaxLength(7)
                    .IsFixedLength()
                    .IsRequired();
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="Recurrence"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="Recurrence"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="startColumnName">The custom name for the start column. Defaults to "{GetPropertyName(propertyExpression)}_Start".</param>
    /// <param name="endColumnName">The custom name for the end column. Defaults to "{GetPropertyName(propertyExpression)}_End".</param>
    /// <param name="weekdaysColumnName">The custom name for the weekdays column. Defaults to "{GetPropertyName(propertyExpression)}_Weekdays".</param>
    /// <param name="excludeWorkingHolidaysColumnName">The custom name for the exclude working holidays bit column. Defaults to "{GetPropertyName(propertyExpression)}_ExcludeWorkingHolidays".</param>
    /// <param name="excludeNotWorkingHolidaysColumnName">The custom name for the exclude not working holidays bit column. Defaults to "{GetPropertyName(propertyExpression)}_ExcludeNotWorkingHolidays".</param>
    /// <param name="includeWorkingHolidaysColumnName">The custom name for the include working holidays bit column. Defaults to "{GetPropertyName(propertyExpression)}_IncludeWorkingHolidays".</param>
    /// <param name="includeNotWorkingHolidaysColumnName">The custom name for include not working holidays bit column. Defaults to "{GetPropertyName(propertyExpression)}_IncludeNotWorkingHolidays".</param>
    /// <param name="exceptionsColumnName">The custom name for the exceptions column. Defaults to "{GetPropertyName(propertyExpression)}_Exceptions".</param>
    /// <param name="inclusionsColumnName">The custom name for the inclusions column. Defaults to "{GetPropertyName(propertyExpression)}_Inclusions".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasRecurrence<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, Recurrence>> propertyExpression,
        Iota? iota,
        string? startColumnName = null,
        string? endColumnName = null,
        string? weekdaysColumnName = null,
        string? excludeWorkingHolidaysColumnName = null,
        string? excludeNotWorkingHolidaysColumnName = null,
        string? includeWorkingHolidaysColumnName = null,
        string? includeNotWorkingHolidaysColumnName = null,
        string? exceptionsColumnName = null,
        string? inclusionsColumnName = null
    )
        where T : class
    {
        startColumnName ??= $"{GetPropertyName(propertyExpression)}_Start";
        endColumnName ??= $"{GetPropertyName(propertyExpression)}_End";
        weekdaysColumnName ??= $"{GetPropertyName(propertyExpression)}_Weekdays";
        excludeWorkingHolidaysColumnName ??= $"{GetPropertyName(propertyExpression)}_ExcludeWorkingHolidays";
        excludeNotWorkingHolidaysColumnName ??= $"{GetPropertyName(propertyExpression)}_ExcludeNotWorkingHolidays";
        includeWorkingHolidaysColumnName ??= $"{GetPropertyName(propertyExpression)}_IncludeWorkingHolidays";
        includeNotWorkingHolidaysColumnName ??= $"{GetPropertyName(propertyExpression)}_IncludeNotWorkingHolidays";
        exceptionsColumnName ??= $"{GetPropertyName(propertyExpression)}_Exceptions";
        inclusionsColumnName ??= $"{GetPropertyName(propertyExpression)}_Inclusions";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<Recurrence> appearance) =>
            {
                // Configure the Start property with a datetime data type an optional custom column order.
                appearance
                    .Property(a => a.Start)
                    .HasColumnName(startColumnName)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnOrder(iota);

                // Configure the End property with a datetime data type an optional custom column order.
                appearance
                    .Property(a => a.End)
                    .HasColumnName(endColumnName)
                    .HasColumnType(dateTimeDataType)
                    .HasColumnOrder(iota);

                // Configure the Weekdays property with an optional custom column order.
                appearance
                    .Property(a => a.Weekdays)
                    .HasColumnName(weekdaysColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the ExcludeWorkingHolidays property with an optional custom column order.
                appearance
                    .Property(a => a.ExcludeWorkingHolidays)
                    .HasColumnName(excludeWorkingHolidaysColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the Exceptions property with an optional custom column order.
                appearance
                    .Property(a => a.Exceptions)
                    .HasColumnName(exceptionsColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the ExcludeNotWorkingHolidays property with an optional custom column order.
                appearance
                    .Property(a => a.ExcludeNotWorkingHolidays)
                    .HasColumnName(excludeNotWorkingHolidaysColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the IncludeWorkingHolidays property with an optional custom column order.
                appearance
                    .Property(a => a.IncludeWorkingHolidays)
                    .HasColumnName(includeWorkingHolidaysColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the IncludeNotWorkingHolidays property with an optional custom column order.
                appearance
                    .Property(a => a.IncludeNotWorkingHolidays)
                    .HasColumnName(includeNotWorkingHolidaysColumnName)
                    .HasColumnOrder(iota)
                    .IsRequired();

                // Configure the Inclusions property with a max length of 1000 an optional custom column order.
                appearance
                    .Property(a => a.Inclusions)
                    .HasColumnName(inclusionsColumnName)
                    .HasColumnOrder(iota)
                    .HasMaxLength(2000)
                    .IsRequired();
            }
        );
    }

    /// <summary>
    /// Configures a property of type <see cref="DoubleValueChange"/> for an entity type.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="propertyExpression">An expression pointing to the <see cref="DoubleValueChange"/> property.</param>
    /// <param name="iota">An optional <see cref="Iota"/> instance for determining column order.</param>
    /// <param name="valueColumnName">The custom name for the value column. Defaults to "{GetPropertyName(propertyExpression)}_Value".</param>
    /// <param name="scaleColumnName">The custom name for the scale column. Defaults to "{GetPropertyName(propertyExpression)}_Scale".</param>
    /// <returns>The configured <see cref="EntityTypeBuilder{T}"/> instance.</returns>
    public static EntityTypeBuilder<T> HasDoubleValueChange<T>(
        this EntityTypeBuilder<T> builder,
        Expression<Func<T, DoubleValueChange>> propertyExpression,
        Iota? iota,
        string? valueColumnName = null,
        string? scaleColumnName = null
    )
        where T : class
    {
        valueColumnName ??= $"{GetPropertyName(propertyExpression)}_Value";
        scaleColumnName ??= $"{GetPropertyName(propertyExpression)}_Scale";

        return builder.ComplexProperty(
            propertyExpression,
            (ComplexPropertyBuilder<DoubleValueChange> interval) =>
            {
                // Configure the Value property with optional custom column order.
                interval.Property(i => i.Value).HasColumnName(valueColumnName).HasColumnOrder(iota);

                // Configure the Scale property with optional custom column order.
                interval.Property(i => i.Scale).HasColumnName(scaleColumnName).HasColumnOrder(iota);
            }
        );
    }

    /// <summary>
    /// Configures an additional index created by SQL. Avoid using if possible. Only use for indexes that cannot be written in another way.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="indexCollection">The <see cref="IAdditionalIndexCollection"/> to store the index.</param>
    /// <param name="indexCreationCommand">The SQL command used to create the index.</param>
    public static void HasIndex<T>(
        this EntityTypeBuilder<T> builder,
        IAdditionalIndexCollection indexCollection,
        string indexCreationCommand
    )
        where T : class
    {
        indexCollection.CreateFixedIndexBuilder(indexCreationCommand);
    }

    /// <summary>
    /// Configures an additional index on the specified properties with given name. If index with the same name already exists, it will be returned instead.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="indexCollection">The <see cref="IAdditionalIndexCollection"/> to store the index.</param>
    /// <param name="indexExpression">Expression for column names discovery.</param>
    /// <param name="name">Name of the index.</param>
    /// <returns>Index with the same name, if it doesn't exist, it will be created.</returns>
    public static ITypedAdditionalIndexBuilder<T> HasIndex<T>(
        this EntityTypeBuilder<T> builder,
        IAdditionalIndexCollection indexCollection,
        Expression<Func<T, object?>> indexExpression,
        string name
    )
        where T : class
    {
        return indexCollection.GetOrCreateIndexBuilder(builder, indexExpression, name);
    }

    /// <summary>
    /// Configures an additional index on the specified properties with given name. If index with the same name already exists, it will be returned instead.
    /// </summary>
    /// <typeparam name="T">The entity type being configured.</typeparam>
    /// <param name="builder">The <see cref="EntityTypeBuilder{T}"/> used to configure the entity.</param>
    /// <param name="indexCollection">The <see cref="IAdditionalIndexCollection"/> to store the index.</param>
    /// <param name="propertyNames">Names of the index columns.</param>
    /// <param name="name">Name of the index.</param>
    /// <returns>Index with the same name, if it doesn't exist, it will be created.</returns>
    public static ITypedAdditionalIndexBuilder<T> HasIndex<T>(
        this EntityTypeBuilder<T> builder,
        IAdditionalIndexCollection indexCollection,
        string[] propertyNames,
        string name
    )
        where T : class
    {
        return indexCollection.GetOrCreateIndexBuilder<T>(builder, propertyNames, name);
    }

    /// <summary>
    /// Gets the property name from an expression.
    /// </summary>
    /// <typeparam name="TEntity">The type of the entity.</typeparam>
    /// <typeparam name="TProperty">The type of the property.</typeparam>
    /// <param name="propertyExpression">An expression pointing to the property.</param>
    /// <returns>The name of the property.</returns>
    /// <exception cref="ArgumentException">Thrown if the expression does not point to a property.</exception>
    private static string GetPropertyName<TEntity, TProperty>(Expression<Func<TEntity, TProperty>> propertyExpression)
    {
        if (propertyExpression.Body is MemberExpression memberExpression)
        {
            return memberExpression.Member.Name;
        }

        throw new ArgumentException(
            "The provided expression does not point to a property.",
            nameof(propertyExpression)
        );
    }
}
