﻿namespace AT.Core.Domain.PushNotificationTokenAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.UserAggregate;

public class PushNotificationTokensForUserIds : Specification<PushNotificationToken>
{
    // NOTE: There used to be FilterWithTempsIdsCacheable; Is this fast enough now?
    public PushNotificationTokensForUserIds(IEnumerable<UserId> userIds)
    {
        Query.Where(pnt => userIds.Contains(pnt.UserId));
    }
}
