﻿namespace AT.Core.Auth;

public static class IUserAuthenticatorExtensions
{
    /// <summary>
    /// For usage in context where we are sure we have an authenticated user.
    /// </summary>
    public static UserId GetCurrentUserIdOrThrow(this IUserAuthenticator userAuthenticator)
    {
        var userId = userAuthenticator.TryGetCurrentUserId();
        if (userId is null)
        {
            throw new InvalidOperationException("No user is authenticated.");
        }

        return userId.Value;
    }
}
