﻿namespace AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.ConfigurationParameters;

using System;
using System.Collections.Generic;
using AT.Primitives.Enums;

/// <summary>
/// A customer may want to limit how many messages of a certain type (e.g. emails informing about a calculation end) they wish to receive
/// in a certain time period. E.g., if a calculation ends, they might wanna get only one email per day that would inform the managers about
/// the end of the calculation. If someone re-runs <b>the same</b> calculation (i.e., with the same ID) throughout the same day, then
/// the NotificationsService will not send the email to any receipient who already received the email in that day.
/// </summary>
public class NotificationsRateLimitingConfig
{
    public IReadOnlyCollection<EmailMessageRateLimitingConfig> Emails { get; set; } = [];
}

public class EmailMessageRateLimitingConfig
{
    /// <summary>
    /// Setup limiting for the emails of this type.
    /// </summary>
    public EmailMessageType Type { get; set; }

    public RateLimitingByType IntervalType { get; set; }

    /// <summary>
    /// If <see cref="IntervalType"/> was set to an arbitrary interval, then this field would contain the specific interval. Currently unused.
    /// </summary>
    public TimeSpan Interval { get; set; }
}
