﻿namespace AT.Translations;

/// <summary>
/// Classes with this attribute are sources for generating resx files.
/// </summary>
// FUTURE: Add support for specifying subfolder path where resx files should be placed.
[AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
public sealed class ResxSourceAttribute(string outputFileName) : Attribute
{
    public string OutputFileName { get; } = outputFileName;

    /// <summary>
    /// Resx file namespace relative to Resources folder.
    /// Example: "Global.Global" for the namespace "AT.SharedResources.Resources.Global.Global".
    /// </summary>
    public string OutputFileRelativeNamespace => $"{OutputFileName}.{OutputFileName}";
}
