﻿namespace AT.Core.DataAccess;

/// <summary>
/// Factory for a specific entity repository.
/// </summary>
/// <typeparam name="TRepository">A repository interface.</typeparam>
public interface IRepositoryFactory<out TRepository>
    where TRepository : class, IDisposable
{
    /// <summary>
    /// BEWARE: Dispose of the repository created using this method.
    /// </summary>
    TRepository Create();
}
