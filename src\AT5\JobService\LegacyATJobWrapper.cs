﻿namespace AT.JobService;

using System.Threading.Tasks;
using AristoTelosJobsService.Common;
using AristoTelosJobsService.Model;
using AT.Core.Jobs.JobService.JobWrapper;
using AT.Infrastructure.Database;
using AT.Primitives.Enums;
using AT.UseCases.Jobs;
using Microsoft.Extensions.Configuration;

public class LegacyATJobWrapper<TLegacyJobImplementation, TLegacyJobParams>(
    OrgDbName _orgDbName,
    IConfiguration _configuration
) : ATJobBase<TLegacyJobParams>
    where TLegacyJobImplementation : IAJob<TLegacyJobParams>
{
    public override async Task<IATJobResult> Execute(IATJobContext context, TLegacyJobParams parameters)
    {
        // This is quite hacky, but we need connection strings for interop with old version.
        var masterDbConnectionString = _configuration.GetConnectionString("LegacyMasterDb");
        var orgDbTemplateConnectionString = _configuration.GetConnectionString("OrgDbTemplate");

        var organization = JobEntityServicesBuilderFactory.GetOrganization(
            _orgDbName.DatabaseName,
            masterConnectionString: masterDbConnectionString
        );

        // This job knows that IATJobContext actually contains class
        // that also implements IATLegacyJobContext, so it can perform this unsafe cast.
        // When legacy jobs are removed. This will be removed as well.
        var legacyContext = (IATLegacyJobContext)context;
        using var entityServices = JobEntityServicesBuilderFactory.CreateEntityServices(
            legacyContext.JobName,
            organization,
            connectionStringTemplate: orgDbTemplateConnectionString,
            unparsedSuccessEmails: string.Join(';', context.SuccessEmails),
            unparsedErrorEmails: string.Join(';', context.ErrorEmails),
            webHostTemplate: legacyContext.AristoTelosWebUrl // This is no longer a template, but that shouldn't matter.
        );

        var legacyJob = entityServices.Resolve<TLegacyJobImplementation>();

        var aJobContext = AJobContext.Create(
            legacyContext.JobId.Value,
            legacyContext.JobName,
            legacyContext.JobKey,
            legacyContext.TriggerKey,
            organization,
            new FireTimeParameters(
                legacyContext.FireTimeParameters.FireTimeUtc,
                legacyContext.FireTimeParameters.NextFireTimeUtc
            )
        );

        await legacyJob.Execute(aJobContext, parameters);
        return await Task.FromResult(MakeResult(JobResultType.None));
    }
}
