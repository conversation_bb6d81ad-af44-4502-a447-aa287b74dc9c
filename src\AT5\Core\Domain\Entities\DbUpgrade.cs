﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class DbUpgrade
{
    public DbUpgradeId Id { get; set; }

    public string Name { get; set; } = null!;

    public DbUpgradeType Type { get; set; }

    public int Number { get; set; }

    public DateTime UpdateStart { get; set; }

    public DateTime UpdateEnd { get; set; }

    public DbUpgradeResult Result { get; set; }
}
