﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class RequestProperty
{
    public RequestPropertyId Id { get; set; }

    public string? Value { get; set; }

    public DateTime ValueChanged { get; set; }

    public UserId ValueChangedById { get; set; }

    public PropertyId PropertyId { get; set; }

    public RequestId RequestId { get; set; }

    public PropertyState State { get; set; }

    public DateTime StateChanged { get; set; }

    public UserId StateChangedById { get; set; }

    public string? ProcessNote { get; set; }

    public virtual Property Property { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual Request Request { get; set; } = null!;

    public virtual ICollection<RequestFile> RequestFiles { get; set; } = new List<RequestFile>();

    public virtual User StateChangedBy { get; set; } = null!;

    public virtual User ValueChangedBy { get; set; } = null!;
}
