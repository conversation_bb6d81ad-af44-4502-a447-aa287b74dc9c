﻿namespace AT.Primitives.Enums;

public enum RotationRosteringRuleType
{
    Rotation = 1, // for each occurence of rule, select last shift from occurence and create penale for each earlier shift based on number days between them

    ////RotationFairPolicyByRatio = 2,
    ////RotationFairPolicyBySum = 3,
    ////RotationByFirst = 4, // rotation penale is computed only from shifts on first day of occurence
    // RotationForAll = 5, // for all shifts in occurence, we create instance of standard Rotation with occurence from start to this shift
    // RotationHourly = 6, // distance is counted in hours instead of days
    RotationWeekly = 7, // weekly rotation, create helper variables for each instance of rule with this type and their occurences, and create rotation for these variables
    // RotationHourlyParts = 8, // like RotationHourly, but by RosterItemParts
    //// rotation coupling can by achieved with simple rotation with negative penale
}
