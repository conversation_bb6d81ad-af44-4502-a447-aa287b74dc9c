﻿namespace AT.Infrastructure.Events.Events;

using AT.Primitives.Enums;

public class RosterRangeDataChangeEvent : DataChangeEvent
{
    public int? RosterId { get; set; }

    public DateTimeInterval? DateInterval { get; set; }

    public IReadOnlyCollection<int>? EmployeeIds { get; set; }

    public RosterRangeDataChangeEvent() { }

    public RosterRangeDataChangeEvent(
        int? rosterId,
        DateTimeInterval? dateInterval,
        IReadOnlyCollection<int>? employeeIds
    )
    {
        Type = DataChangeType.RosterRange;
        RosterId = rosterId;
        DateInterval = dateInterval;
        EmployeeIds = employeeIds;
    }
}
