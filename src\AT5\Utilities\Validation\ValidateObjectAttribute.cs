namespace AT.Utilities.Validation;

using System.ComponentModel.DataAnnotations;
using System.Text;
using AT.Utilities.Collections;

/// <summary>
/// A custom validation attribute that recursively validates an object's properties.
/// </summary>
/// <param name="_allowNull">Indicates whether null values are considered valid.</param>
public class ValidateObjectAttribute(bool _allowNull = false) : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is null)
        {
            return _allowNull
                ? ValidationResult.Success
                : validationContext.CreateValidationResult($"{validationContext.DisplayName} cannot be null.");
        }

        var validationResults = new List<ValidationResult>();
        var context = new ValidationContext(value, validationContext, validationContext.Items);

        // Recursively validate all properties of the object
        bool isValid = Validator.TryValidateObject(value, context, validationResults, validateAllProperties: true);

        if (isValid)
        {
            return ValidationResult.Success;
        }

        // Build an error message that aggregates all validation errors
        var message = new StringBuilder(
            $"{Environment.NewLine}The {validationContext.DisplayName} field:{Environment.NewLine}"
        );

        message = validationResults.Aggregate(
            message,
            (current, validationResult) =>
                // Adds indentation for better readability of nested validation errors
                current.Append(
                    $"{validationResult.ErrorMessage?
                    .Split(Environment.NewLine)
                    .Select(line => $"  {line}")
                    .ToJoinedString(Environment.NewLine)}{Environment.NewLine}"
                )
        );

        return validationContext.CreateValidationResult(message.ToString());
    }
}
