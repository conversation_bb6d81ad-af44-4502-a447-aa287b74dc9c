﻿namespace AT.ConsoleExample.Commands.AppsettingsExample;

using System.Threading.Tasks;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Microsoft.Extensions.Options;

[Command("appSettingsExample", Description = "Print appsetings values")]
public class AppSettingsExampleCommand(IOptionsMonitor<AppSettingsExampleOptions> _appsetingsExampleOptions) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        while (true)
        {
            await console.Output.WriteLineAsync("Current settings value from appsettings.json, appsettings.local.json");
            await console.Output.WriteLineAsync(
                $"AppsetingsExampleOptions.Value1: {_appsetingsExampleOptions.CurrentValue.Value1}"
            );
            await console.Output.WriteLineAsync(
                $"AppsetingsExampleOptions.Value2: {_appsetingsExampleOptions.CurrentValue.Value2}"
            );
            await console.Output.WriteLineAsync("Pres key 'q' to exit program.");

            bool exit = console.ReadKey().KeyChar == 'q';
            if (exit)
            {
                break;
            }

            await console.Output.WriteLineAsync();
        }

        await Task.CompletedTask;
    }
}
