namespace AT.Core.Domain.PushNotificationTokenAggregate;

using AT.Core.Domain.UserAggregate;
using Primitives.Enums;

public class PushNotificationToken : IAggregateRoot
{
    public PushNotificationTokenId Id { get; set; }

    public string Token { get; set; } = null!;

    public UserId UserId { get; set; }

    public DateTime Created { get; set; }

    public PushNotificationTokensType Type { get; set; }

    public virtual User User { get; set; } = null!;
}
