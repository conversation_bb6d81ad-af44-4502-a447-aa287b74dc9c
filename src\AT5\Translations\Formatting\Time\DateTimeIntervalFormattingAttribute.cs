﻿namespace AT.Translations.Formatting.Time;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class DateTimeIntervalFormattingAttribute(DateTimeIntervalFormattingType type) : Attribute
{
    public DateTimeIntervalFormattingType Type { get; init; } = type;
}

// FUTURE: This shouldn't be in the Translations project.
/// <summary>
/// Locale-specific type of <see cref="DataStructures.Time.DateInterval"/> formatting.
/// </summary>
public enum DateTimeIntervalFormattingType
{
    /// <summary>
    /// Combines <see cref="DateFormattingType.Standard"/> and <see cref="TimeFormattingType.Standard"/>
    /// both for Start and End.
    /// </summary>
    Standard,

    /// <summary>
    /// E.g.:<br/>
    /// * "5.5.2025 10:00-14:00" when Start.Date == End.Date.<br/>
    /// * "5.5.2025-6.5.2025" when both Start and End have midnight time. In this case, the End is 7.5.2025 00:00.<br/>
    /// * Otherwise "5.5.2025 7:00-6.5.2025 14:00"
    /// </summary>
    Short,
}
