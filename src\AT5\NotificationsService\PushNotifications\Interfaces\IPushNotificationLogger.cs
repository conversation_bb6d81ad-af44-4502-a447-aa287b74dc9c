﻿namespace AT.NotificationsService.PushNotifications.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using NotificationsService.General.Interfaces;

public interface IPushNotificationLogger : IMessageLogger<PushNotification, PushNotificationRecipient>
{
    void LogPushNotificationTokensLoad(PushNotification message, IEnumerable<string> tokens);

    /// <summary>
    /// When using SendToMultiple, we only know the number of tokens that received the push notification.
    /// Therefore, we cannot log specific recipients who did and who did not receive the push notification.
    /// </summary>
    void LogPushNotificationSendPartiallySuccessful(PushNotification message);
}
