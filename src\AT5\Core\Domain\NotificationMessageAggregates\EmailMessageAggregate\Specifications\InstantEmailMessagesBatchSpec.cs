﻿namespace AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;

public class InstantEmailMessagesBatchSpec : Specification<EmailMessage>
{
    public InstantEmailMessagesBatchSpec(int? batchSize = null)
    {
        Query
            .Where(pn => pn.SendTime == null)
            .Include(e => e.EmailMessageRecipients)
            .Include(e => e.EmailMessageAttachments);

        if (batchSize > 0)
        {
            Query.Take(batchSize.Value);
        }
    }
}
