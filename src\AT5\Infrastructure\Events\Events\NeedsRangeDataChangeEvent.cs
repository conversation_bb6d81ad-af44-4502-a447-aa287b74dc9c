﻿namespace AT.Infrastructure.Events.Events;

using AT.Primitives.Enums;

public class NeedsRangeDataChangeEvent : DataChangeEvent
{
    public int NeedsSiteId { get; set; }

    public DateTimeInterval? DateInterval { get; set; }

    public NeedsRangeDataChangeEvent() { }

    public NeedsRangeDataChangeEvent(int needsSiteId, DateTimeInterval? dateInterval)
    {
        Type = DataChangeType.NeedsRange;
        NeedsSiteId = needsSiteId;
        DateInterval = dateInterval;
    }
}
