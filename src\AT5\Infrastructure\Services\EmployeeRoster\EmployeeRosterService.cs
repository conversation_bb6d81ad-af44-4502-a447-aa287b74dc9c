﻿namespace AT.Infrastructure.Services.EmployeeRoster;

using System.Collections.Generic;
using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.EmployeeRosterAggregate.Queries;
using AT.Primitives.Enums;

internal sealed class EmployeeRosterService(IEmployeeRosterFinishQuery _employeeRosterFinishQuery)
    : IEmployeeRosterService
{
    public async Task<List<DateInterval>> GetFinishedIntervalsAsync(
        UserId employeeId,
        CancellationToken cancellationToken = default
    )
    {
        var employeeRosterFinishedInfos = await _employeeRosterFinishQuery.GetEmployeeRosterFinishesAsync(
            employeeId,
            cancellationToken
        );

        var finishedDateIntervals = employeeRosterFinishedInfos
            .Where(x => x.AttendanceFinishState == AttendanceFinishState.Finished)
            .Select(x => x.DateInterval)
            .ToList();

        SortAndJoinOverlappingAndConnectingIntervals(finishedDateIntervals);

        return finishedDateIntervals;
    }

    public async Task<Dictionary<UserId, List<DateInterval>>> GetFinishedIntervalsPerEmployeeAsync(
        IReadOnlyCollection<UserId> employeeIds,
        CancellationToken cancellationToken = default(CancellationToken)
    )
    {
        var employeeRosterFinishedInfos = await _employeeRosterFinishQuery.GetEmployeeRosterFinishesAsync(
            employeeIds,
            cancellationToken
        );

        var finishedDateIntervalsPerEmployeeId = employeeRosterFinishedInfos
            .Where(x => x.AttendanceFinishState == AttendanceFinishState.Finished)
            .GroupBy(x => x.EmployeeId)
            .ToDictionary(g => g.Key, g => g.Select(x => x.DateInterval).ToList());

        foreach (var employeeIntervals in finishedDateIntervalsPerEmployeeId.Values)
        {
            SortAndJoinOverlappingAndConnectingIntervals(employeeIntervals);
        }

        return finishedDateIntervalsPerEmployeeId;
    }

    // TODO: Move it to appropriate place.
    private static void SortAndJoinOverlappingAndConnectingIntervals(List<DateInterval> dateIntervals)
    {
        dateIntervals.Sort((a, b) => a.Start.CompareTo(b.Start));

        int i = 0;
        while (i < dateIntervals.Count - 1)
        {
            if (dateIntervals[i].End >= dateIntervals[i + 1].Start.AddDays(+1))
            {
                if (dateIntervals[i].End < dateIntervals[i + 1].End)
                {
                    dateIntervals[i] = new DateInterval(dateIntervals[i].Start, dateIntervals[i + 1].End);
                }

                dateIntervals.RemoveAt(i + 1);
            }
            else
            {
                i++;
            }
        }
    }
}
