﻿namespace AT.Core.Domain.Entities;

public class ShiftTemplateFilter
{
    public ShiftTemplateFilterId Id { get; set; }

    public Filter2Id FilterId { get; set; }

    public ShiftTemplateId ShiftTemplateId { get; set; }

    public Validity Validity { get; set; }

    public string? Parameters { get; set; }

    public virtual Filter2 Filter { get; set; } = null!;

    public virtual ShiftTemplate ShiftTemplate { get; set; } = null!;
}
