﻿namespace AT.ApiService.Endpoints.Users;

using System.Threading;
using System.Threading.Tasks;
using AT.Shared.Models.Users;
using FastEndpoints;

public class GetUserEndpoint : Endpoint<GetUserRequest, GetUserResponse>
{
    public override void Configure()
    {
        Get("/users/{UserId}");
        AllowAnonymous();
    }

    public override async Task HandleAsync(GetUserRequest request, CancellationToken ct)
    {
        await SendAsync(new() { Name = $"TestName{request.UserId}", UserId = request.UserId }, cancellation: ct);
    }
}
