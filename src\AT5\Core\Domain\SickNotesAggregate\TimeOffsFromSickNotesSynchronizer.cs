namespace AT.Core.Domain.SickNotesAggregate;

using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.RosterItemAggregate.Specifications;
using AT.Utilities.Collections;
using AT.Utilities.General;
using AT.Utilities.Logging;
using AT.Utilities.Time;
using AT.Utilities.Time.Utils;
using static AT.Core.Domain.SickNotesAggregate.RosterItemsFromSickNotesSyncResult;

public sealed class TimeOffsFromSickNotesSynchronizer(
    ILogger<TimeOffsFromSickNotesSynchronizer> _logger,
    IEmployeeRosterService _employeeRosterService,
    IRosterItemRepository _rosterItemRepository,
    IPeriodTimeOffWriteService _periodTimeOffWriteService
) : ITimeOffsFromSickNotesSynchronizer
{
    public async Task<RosterItemsFromSickNotesSyncResult> Synchronize(
        RosterId rosterId,
        IReadOnlyCollection<SickNoteSyncInfo> sickNoteSyncInfos,
        CancellationToken cancellationToken = default
    )
    {
        var rowsByIsSickNoteNewPredicate = sickNoteSyncInfos.SplitInTwo(x => x.IsNewSickNote);
        var newSickNotes = rowsByIsSickNoteNewPredicate.HasFeature;
        var updatedSickNotes = rowsByIsSickNoteNewPredicate.DoesntHaveFeature;

        var employeeIds = sickNoteSyncInfos.Select(x => x.EmployeeId).ToHashSet();
        var finishedIntervalsPerEmployeeId = await _employeeRosterService.GetFinishedIntervalsPerEmployeeAsync(
            employeeIds,
            cancellationToken
        );

        var createResult = await CreateRosterItemsAsync(
            rosterId,
            newSickNotes,
            finishedIntervalsPerEmployeeId,
            cancellationToken
        );

        var updateResult = await UpdateRosterItemsAsync(
            updatedSickNotes,
            finishedIntervalsPerEmployeeId,
            cancellationToken
        );

        return new()
        {
            CreatedRosterItems = createResult.CreatedRosterItems,
            CreationSkipReasonPerSickNoteId = createResult.SkipReasonPerSickNoteId,
            ChangesPerRosterItemId = updateResult.ChangesPerRosterItemId,
            SkippedChangesPerRosterItemId = updateResult.SkippedChangesPerRosterItemId,
        };
    }

    private async Task<CreateResult> CreateRosterItemsAsync(
        RosterId rosterId,
        IReadOnlyList<SickNoteSyncInfo> syncInfos,
        Dictionary<UserId, List<DateInterval>> finishedIntervalsPerEmployeeId,
        CancellationToken cancellationToken = default
    )
    {
        List<PeriodTimeOffInfo> periodTimeOffInfos = [];
        Dictionary<SickNoteId, PeriodTimeOffCreationSkipReason> skipReasonPerSickNoteId = [];

        foreach (var syncInfo in syncInfos)
        {
            var employeeFinishedIntervals = finishedIntervalsPerEmployeeId.GetValueOr(syncInfo.EmployeeId, []);

            if (employeeFinishedIntervals.Any(i => i.Overlaps(syncInfo.OpenDateInterval)))
            {
                var reason = PeriodTimeOffCreationSkipReason.SickNoteIntervalIsInFinishedRoster;
                skipReasonPerSickNoteId[syncInfo.SickNoteId] = reason;
                continue;
            }

            PeriodTimeOffInfo periodTimeOffInfo =
                new(rosterId, syncInfo.EmployeeId, syncInfo.OpenDateInterval, syncInfo.TimeOffTypeId);

            periodTimeOffInfos.Add(periodTimeOffInfo);
        }

        var createdRosterItems = await _periodTimeOffWriteService.CreatePeriodTimeOffsAsync(
            periodTimeOffInfos,
            cancellationToken
        );

        return new() { CreatedRosterItems = createdRosterItems, SkipReasonPerSickNoteId = skipReasonPerSickNoteId, };
    }

    private async Task<UpdateResult> UpdateRosterItemsAsync(
        IReadOnlyList<SickNoteSyncInfo> syncInfos,
        Dictionary<UserId, List<DateInterval>> finishedIntervalsPerEmployeeId,
        CancellationToken cancellationToken = default
    )
    {
        Dictionary<RosterItemId, RosterItemChanges> changesPerRosterItemId = [];
        Dictionary<RosterItemId, SkippedRosterItemChanges> skippedChangesPerRosterItemId = [];
        List<RosterItem> rosterItemsToUpdate = [];

        var sickNotesIds = syncInfos.Select(x => x.SickNoteId).ToList();
        var rosterItemPerSickNoteId = await LoadRosterItemPerSickNoteAsync(sickNotesIds, cancellationToken);

        foreach (var syncInfo in syncInfos)
        {
            if (!rosterItemPerSickNoteId.TryGetValue(syncInfo.SickNoteId, out var rosterItem))
            {
                _logger.Error(
                    "Requested update of a non-existent RosterItem for an existing SickNote {SickNoteId}",
                    syncInfo.SickNoteId
                );

                continue;
            }

            var employeeFinishedIntervals = finishedIntervalsPerEmployeeId.GetValueOr(syncInfo.EmployeeId, []);

            (var changes, var skippedChanges) = UpdateRosterItemFromSickNoteInfo(
                rosterItem,
                syncInfo,
                employeeFinishedIntervals
            );

            if (changes is not null)
            {
                changesPerRosterItemId[rosterItem.Id] = changes;
                rosterItemsToUpdate.Add(rosterItem);
            }

            if (skippedChanges is not null)
            {
                skippedChangesPerRosterItemId[rosterItem.Id] = skippedChanges;
            }
        }

        await _periodTimeOffWriteService.UpdatePeriodTimeOffsAsync(rosterItemsToUpdate, cancellationToken);

        return new()
        {
            ChangesPerRosterItemId = changesPerRosterItemId,
            SkippedChangesPerRosterItemId = skippedChangesPerRosterItemId,
        };
    }

    private static RosterItemUpdateResult UpdateRosterItemFromSickNoteInfo(
        RosterItem rosterItem,
        SickNoteSyncInfo syncInfo,
        List<DateInterval> employeeFinishedIntervals
    )
    {
        RosterItemChanges rosterItemChanges = new();
        SkippedRosterItemChanges skippedRosterItemChanges = new();

        UpdateRosterItemPartTypeFromSickNoteInfo(
            rosterItem,
            syncInfo,
            rosterItemChanges,
            skippedRosterItemChanges,
            employeeFinishedIntervals
        );

        UpdateRosterItemStartFromSickNoteInfo(
            rosterItem,
            syncInfo,
            rosterItemChanges,
            skippedRosterItemChanges,
            employeeFinishedIntervals
        );

        UpdateRosterItemEndFromSickNoteInfo(
            rosterItem,
            syncInfo,
            rosterItemChanges,
            skippedRosterItemChanges,
            employeeFinishedIntervals
        );

        bool anyRosterItemChanges =
            rosterItemChanges.IntervalStartChange.HasValue
            || rosterItemChanges.IntervalEndChange.HasValue
            || rosterItemChanges.TimeOffTypeChange.HasValue;

        bool anySkippedRosterItemChanges =
            skippedRosterItemChanges.SkippedIntervalStartChange.HasValue
            || skippedRosterItemChanges.SkippedIntervalEndChange.HasValue
            || skippedRosterItemChanges.SkippedTimeOffTypeChange.HasValue;

        return new(
            anyRosterItemChanges ? rosterItemChanges : null,
            anySkippedRosterItemChanges ? skippedRosterItemChanges : null
        );
    }

    private static void UpdateRosterItemPartTypeFromSickNoteInfo(
        RosterItem rosterItem,
        SickNoteSyncInfo syncInfo,
        RosterItemChanges rosterItemChanges,
        SkippedRosterItemChanges skippedRosterItemChanges,
        List<DateInterval> employeeFinishedIntervals
    )
    {
        bool isInFinishedRoster = employeeFinishedIntervals.Any(i => i.Overlaps(syncInfo.OpenDateInterval));
        var typeIdEntityChange = new EntityChange<RosterItemPartTypeId>(
            Old: rosterItem.RosterItemParts.First().RosterItemPartTypeId,
            New: syncInfo.TimeOffTypeId
        );

        PeriodTimeOffFieldUpdateSkipReason? skipReason = null;
        if (!syncInfo.SynchronizationInfo.SynchronizeType)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.SynchronizationSkipRequested;
        }
        else if (typeIdEntityChange.Old == typeIdEntityChange.New)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.ValueIsUnchanged;
        }
        else if (isInFinishedRoster)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.TimeOffIsInFinishedRoster;
        }

        if (skipReason.HasValue)
        {
            skippedRosterItemChanges.SkippedTimeOffTypeChange = new(typeIdEntityChange, skipReason.Value);
            return;
        }

        rosterItemChanges.TimeOffTypeChange = typeIdEntityChange;
        rosterItem.RosterItemParts.First().RosterItemPartTypeId = typeIdEntityChange.New;
    }

    private static void UpdateRosterItemStartFromSickNoteInfo(
        RosterItem rosterItem,
        SickNoteSyncInfo syncInfo,
        RosterItemChanges rosterItemChanges,
        SkippedRosterItemChanges skippedRosterItemChanges,
        List<DateInterval> employeeFinishedIntervals
    )
    {
        var startEntityChange = new EntityChange<DateTime>(
            Old: rosterItem.RosterItemParts.First().Interval.Start,
            New: syncInfo.From.ToDateTime(TimeOnly.MinValue)
        );

        PeriodTimeOffFieldUpdateSkipReason? skipReason = null;
        if (!syncInfo.SynchronizationInfo.SynchronizeStart)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.SynchronizationSkipRequested;
        }
        else if (startEntityChange.Old == startEntityChange.New)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.ValueIsUnchanged;
        }
        else if (
            !IsDateChangeValid(
                currentDate: startEntityChange.Old.ToDateOnly(),
                targetDate: syncInfo.From,
                employeeFinishedIntervals
            )
        )
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.TimeOffIsInFinishedRoster;
        }

        if (skipReason.HasValue)
        {
            skippedRosterItemChanges.SkippedIntervalStartChange = new(startEntityChange, skipReason.Value);
            return;
        }

        rosterItemChanges.IntervalStartChange = startEntityChange;
        rosterItem.RosterItemParts.First().Interval = new(
            startEntityChange.New,
            rosterItem.RosterItemParts.First().Interval.End
        );
    }

    private static void UpdateRosterItemEndFromSickNoteInfo(
        RosterItem rosterItem,
        SickNoteSyncInfo syncInfo,
        RosterItemChanges rosterItemChanges,
        SkippedRosterItemChanges skippedRosterItemChanges,
        List<DateInterval> employeeFinishedIntervals
    )
    {
        var endEntityChange = new EntityChange<DateTime>(
            Old: rosterItem.RosterItemParts.First().Interval.End,
            New: syncInfo.To?.ToDateTime(TimeOnly.MinValue) ?? DbDateLimits.Max
        );

        PeriodTimeOffFieldUpdateSkipReason? skipReason = null;
        if (!syncInfo.SynchronizationInfo.SynchronizeEnd)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.SynchronizationSkipRequested;
        }
        else if (endEntityChange.Old == endEntityChange.New)
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.ValueIsUnchanged;
        }
        else if (
            !IsDateChangeValid(
                currentDate: endEntityChange.Old.ToDateOnly(),
                targetDate: endEntityChange.New.ToDateOnly(),
                employeeFinishedIntervals
            )
        )
        {
            skipReason = PeriodTimeOffFieldUpdateSkipReason.TimeOffIsInFinishedRoster;
        }

        if (skipReason.HasValue)
        {
            skippedRosterItemChanges.SkippedIntervalEndChange = new(endEntityChange, skipReason.Value);
            return;
        }

        rosterItemChanges.IntervalEndChange = endEntityChange;
        rosterItem.RosterItemParts.First().Interval = new(
            rosterItem.RosterItemParts.First().Interval.Start,
            endEntityChange.New
        );
    }

    private async Task<Dictionary<SickNoteId, RosterItem>> LoadRosterItemPerSickNoteAsync(
        IReadOnlyList<SickNoteId> sickNotesIds,
        CancellationToken cancellationToken = default
    )
    {
        if (sickNotesIds.Count == 0)
        {
            return [];
        }

        var rosterItems = await _rosterItemRepository.ListAsync(
            new SickNoteRosterItemsSpec(sickNotesIds),
            cancellationToken
        );

        return rosterItems.ToDictionary(x => x.SickNoteId!.Value);
    }

    private static bool IsDateChangeValid(
        DateOnly currentDate,
        DateOnly targetDate,
        List<DateInterval> employeeFinishedIntervals
    )
    {
        if (currentDate == targetDate)
        {
            return true;
        }

        // This is the interval by which we would shorten/extend the time off.
        var changeInterval =
            targetDate < currentDate
                ? new DateInterval(targetDate, currentDate.AddDays(-1))
                : new DateInterval(currentDate.AddDays(1), targetDate);

        // If the change interval does not overlap any finished interval, then the change is valid.
        return employeeFinishedIntervals.All(x => !x.Overlaps(changeInterval));
    }

    private sealed record RosterItemUpdateResult(
        RosterItemChanges? RosterItemChanges,
        SkippedRosterItemChanges? SkippedRosterItemChanges
    );

    private sealed record UpdateResult
    {
        public required Dictionary<RosterItemId, RosterItemChanges> ChangesPerRosterItemId { get; init; }

        public required Dictionary<RosterItemId, SkippedRosterItemChanges> SkippedChangesPerRosterItemId { get; init; }
    }

    private sealed record CreateResult
    {
        public required List<RosterItem> CreatedRosterItems { get; init; }

        public required Dictionary<SickNoteId, PeriodTimeOffCreationSkipReason> SkipReasonPerSickNoteId { get; init; }
    }
}
