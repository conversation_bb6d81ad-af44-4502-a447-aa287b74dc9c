﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Core.Domain.UserAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Request"/> in the database.
/// </summary>
internal static class RequestGroupModelBuilder
{
    public static void BuildRequestGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<ChangeRequest>(entity =>
        {
            entity.ToTable("ChangeRequests");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CreatedById, "IX_FK_ChangeRequestAuthor");

            entity.HasIndex(e => e.ProcessedById, "IX_FK_ChangeRequestUser");

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Processed).IsStoredAsDateTime();

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.ChangeRequestCreatedBies)
                .HasForeignKey(d => d.CreatedById)
                .HasConstraintName("FK_ChangeRequestAuthor");

            entity
                .HasOne(d => d.ProcessedBy)
                .WithMany(p => p.ChangeRequestProcessedBies)
                .HasForeignKey(d => d.ProcessedById)
                .HasConstraintName("FK_ChangeRequestUser");
        });

        modelBuilder.Entity<Request>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Requests");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity.Property(e => e.Priority).HasColumnOrder(iota);
            entity.Property(e => e.IntervalType).HasColumnOrder(iota);
            entity.Property(e => e.Status).HasColumnOrder(iota);

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.EmployeeId,
                        e.Interval.Start,
                        e.Interval.End
                    },
                    "IX_FK_EmployeeRosterRequest"
                )
                .IncludeProperties(e => new { e.Created })
                .WithSettings(new { FILLFACTOR = 95 });

            entity.Property(e => e.RequestTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RequestTypeId, "IX_FK_RequestTypeRequest");

            entity.Property(e => e.Changed).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.ChangedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.ChangedById, "IX_FK_RequestChangedByUser");

            entity.Property(e => e.Created).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.CreatedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.CreatedById, "IX_FK_RequestCreatedByUser");

            entity.Property(e => e.Action).HasColumnOrder(iota);

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_RequestSite");

            entity
                .HasOne(d => d.ChangedBy)
                .WithMany(p => p.RequestChangedBies)
                .HasForeignKey(d => d.ChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestChangedByUser");

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.RequestCreatedBies)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestCreatedByUser");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.Requests)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeRosterRequest");

            entity
                .HasOne(d => d.RequestType)
                .WithMany(p => p.Requests)
                .HasForeignKey(d => d.RequestTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeRequest");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.Requests)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestSite");

            entity
                .HasMany(d => d.ReferencedBies)
                .WithMany(p => p.RequestReferences)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestRefrence",
                    r =>
                        r.HasOne<Request>()
                            .WithMany()
                            .HasForeignKey("ReferencedById")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestRefrences_RequestReference"),
                    l =>
                        l.HasOne<Request>()
                            .WithMany()
                            .HasForeignKey("RequestReferencesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestRefrences_ReferencedBy"),
                    j =>
                    {
                        j.HasKey("ReferencedById", "RequestReferencesId");
                        j.ToTable("RequestRefrences");
                        j.HasIndex(["RequestReferencesId"], "IX_FK_RequestRefrences_ReferencedBy");
                        j.IndexerProperty<RequestId>("ReferencedById").HasColumnName("ReferencedBy_Id");
                        j.IndexerProperty<RequestId>("RequestReferencesId").HasColumnName("RequestReferences_Id");
                    }
                );

            entity
                .HasMany(d => d.RequestReferences)
                .WithMany(p => p.ReferencedBies)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestRefrence",
                    r =>
                        r.HasOne<Request>()
                            .WithMany()
                            .HasForeignKey("RequestReferencesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestRefrences_ReferencedBy"),
                    l =>
                        l.HasOne<Request>()
                            .WithMany()
                            .HasForeignKey("ReferencedById")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestRefrences_RequestReference"),
                    j =>
                    {
                        j.HasKey("ReferencedById", "RequestReferencesId");
                        j.ToTable("RequestRefrences");
                        j.HasIndex(["RequestReferencesId"], "IX_FK_RequestRefrences_ReferencedBy");
                        j.IndexerProperty<RequestId>("ReferencedById").HasColumnName("ReferencedBy_Id");
                        j.IndexerProperty<RequestId>("RequestReferencesId").HasColumnName("RequestReferences_Id");
                    }
                );

            entity
                .HasMany(d => d.ShiftTemplates)
                .WithMany(p => p.RequestShiftTemplateShiftTemplates)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestShiftTemplate",
                    r =>
                        r.HasOne<ShiftTemplate>()
                            .WithMany()
                            .HasForeignKey("ShiftTemplatesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestShiftTemplate_ShiftTemplate"),
                    l =>
                        l.HasOne<Request>()
                            .WithMany()
                            .HasForeignKey("RequestShiftTemplateShiftTemplateId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestShiftTemplate_Request"),
                    j =>
                    {
                        j.HasKey("RequestShiftTemplateShiftTemplateId", "ShiftTemplatesId");
                        j.ToTable("RequestShiftTemplate");
                        j.HasIndex(["ShiftTemplatesId"], "IX_FK_RequestShiftTemplate_ShiftTemplate");
                        j.IndexerProperty<RequestId>("RequestShiftTemplateShiftTemplateId")
                            .HasColumnName("RequestShiftTemplate_ShiftTemplate_Id");
                        j.IndexerProperty<ShiftTemplateId>("ShiftTemplatesId").HasColumnName("ShiftTemplates_Id");
                    }
                );
        });

        modelBuilder.Entity<RequestFile>(entity =>
        {
            entity.ToTable("RequestFiles");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RequestPropertyId, "IX_FK_RequestFileRequestProperty");

            entity.HasIndex(e => e.AuthorId, "IX_FK_RequestFileUser");

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Name).HasMaxLength(512);

            entity
                .HasOne(d => d.Author)
                .WithMany(p => p.RequestFiles)
                .HasForeignKey(d => d.AuthorId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestFileUser");

            entity
                .HasOne(d => d.RequestProperty)
                .WithMany(p => p.RequestFiles)
                .HasForeignKey(d => d.RequestPropertyId)
                .HasConstraintName("FK_RequestFileRequestProperty");
        });

        modelBuilder.Entity<RequestLimit>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RequestLimits");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasColumnOrder(iota);
            entity.Property(e => e.Period).HasColumnOrder(iota);
            entity.Property(e => e.Limit).HasColumnOrder(iota);
            entity.Property(e => e.Disabled).HasColumnOrder(iota);

            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.AutoApproveLimit).HasColumnOrder(iota);

            entity.HasOpenDateInterval(e => e.TotalInterval, iota);

            entity
                .HasMany(d => d.Employees)
                .WithMany(p => p.RequestLimits)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestLimitEmployee",
                    r =>
                        r.HasOne<Employee>()
                            .WithMany()
                            .HasForeignKey("EmployeesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitEmployee_Employee"),
                    l =>
                        l.HasOne<RequestLimit>()
                            .WithMany()
                            .HasForeignKey("RequestLimitsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitEmployee_RequestLimit"),
                    j =>
                    {
                        j.HasKey("RequestLimitsId", "EmployeesId");
                        j.ToTable("RequestLimitEmployee");
                        j.HasIndex(["EmployeesId"], "IX_FK_RequestLimitEmployee_Employee");
                        j.IndexerProperty<RequestLimitId>("RequestLimitsId").HasColumnName("RequestLimits_Id");
                        j.IndexerProperty<UserId>("EmployeesId").HasColumnName("Employees_Id");
                    }
                );

            entity
                .HasMany(d => d.RequestTypes)
                .WithMany(p => p.Limits)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestLimitRequestType",
                    r =>
                        r.HasOne<RequestType>()
                            .WithMany()
                            .HasForeignKey("RequestTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitRequestType_RequestType"),
                    l =>
                        l.HasOne<RequestLimit>()
                            .WithMany()
                            .HasForeignKey("LimitsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitRequestType_RequestLimit"),
                    j =>
                    {
                        j.HasKey("LimitsId", "RequestTypesId");
                        j.ToTable("RequestLimitRequestType");
                        j.HasIndex(["RequestTypesId"], "IX_FK_RequestLimitRequestType_RequestType");
                        j.IndexerProperty<RequestLimitId>("LimitsId").HasColumnName("Limits_Id");
                        j.IndexerProperty<RequestTypeId>("RequestTypesId").HasColumnName("RequestTypes_Id");
                    }
                );

            entity
                .HasMany(d => d.ShiftTemplates)
                .WithMany(p => p.RequestLimits)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestLimitShiftTemplate",
                    r =>
                        r.HasOne<ShiftTemplate>()
                            .WithMany()
                            .HasForeignKey("ShiftTemplatesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitShiftTemplate_ShiftTemplate"),
                    l =>
                        l.HasOne<RequestLimit>()
                            .WithMany()
                            .HasForeignKey("RequestLimitsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitShiftTemplate_RequestLimit"),
                    j =>
                    {
                        j.HasKey("RequestLimitsId", "ShiftTemplatesId");
                        j.ToTable("RequestLimitShiftTemplate");
                        j.HasIndex(["ShiftTemplatesId"], "IX_FK_RequestLimitShiftTemplate_ShiftTemplate");
                        j.IndexerProperty<RequestLimitId>("RequestLimitsId").HasColumnName("RequestLimits_Id");
                        j.IndexerProperty<ShiftTemplateId>("ShiftTemplatesId").HasColumnName("ShiftTemplates_Id");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.RequestLimits)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestLimitSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitSite_Site"),
                    l =>
                        l.HasOne<RequestLimit>()
                            .WithMany()
                            .HasForeignKey("RequestLimitsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitSite_RequestLimit"),
                    j =>
                    {
                        j.HasKey("RequestLimitsId", "SitesId");
                        j.ToTable("RequestLimitSite");
                        j.HasIndex(["SitesId"], "IX_FK_RequestLimitSite_Site");
                        j.IndexerProperty<RequestLimitId>("RequestLimitsId").HasColumnName("RequestLimits_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );

            entity
                .HasMany(d => d.Teams)
                .WithMany(p => p.RequestLimits)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestLimitTeam",
                    r =>
                        r.HasOne<Team>()
                            .WithMany()
                            .HasForeignKey("TeamsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitTeam_Team"),
                    l =>
                        l.HasOne<RequestLimit>()
                            .WithMany()
                            .HasForeignKey("RequestLimitsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestLimitTeam_RequestLimit"),
                    j =>
                    {
                        j.HasKey("RequestLimitsId", "TeamsId");
                        j.ToTable("RequestLimitTeam");
                        j.HasIndex(["TeamsId"], "IX_FK_RequestLimitTeam_Team");
                        j.IndexerProperty<RequestLimitId>("RequestLimitsId").HasColumnName("RequestLimits_Id");
                        j.IndexerProperty<TeamId>("TeamsId").HasColumnName("Teams_Id");
                    }
                );
        });

        modelBuilder.Entity<RequestProperty>(entity =>
        {
            entity.ToTable("RequestProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.PropertyId, "IX_FK_RequestPropertyProperty");

            entity.HasIndex(e => e.RequestId, "IX_FK_RequestPropertyRequest");

            entity.HasIndex(e => e.StateChangedById, "IX_FK_RequestPropertyStateChangedByUser");

            entity.HasIndex(e => e.ValueChangedById, "IX_FK_RequestPropertyValueChangedByUser");

            entity.Property(e => e.ProcessNote).HasMaxLength(4000);
            entity.Property(e => e.StateChanged).IsStoredAsDateTime();
            entity.Property(e => e.Value).HasMaxLength(4000);
            entity.Property(e => e.ValueChanged).IsStoredAsDateTime();

            entity
                .HasOne(d => d.Property)
                .WithMany(p => p.RequestProperties)
                .HasForeignKey(d => d.PropertyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestPropertyProperty");

            entity
                .HasOne(d => d.Request)
                .WithMany(p => p.RequestProperties)
                .HasForeignKey(d => d.RequestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestPropertyRequest");

            entity
                .HasOne(d => d.StateChangedBy)
                .WithMany(p => p.RequestPropertyStateChangedBies)
                .HasForeignKey(d => d.StateChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestPropertyStateChangedByUser");

            entity
                .HasOne(d => d.ValueChangedBy)
                .WithMany(p => p.RequestPropertyValueChangedBies)
                .HasForeignKey(d => d.ValueChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestPropertyValueChangedByUser");
        });

        modelBuilder.Entity<RequestSyncQueueEntry>(entity =>
        {
            entity.ToTable("RequestSyncQueueEntries");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RequestId, "IX_FK_RequestSyncQueueEntryRequest");

            entity
                .HasOne(d => d.Request)
                .WithMany(p => p.RequestSyncQueueEntries)
                .HasForeignKey(d => d.RequestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestSyncQueueEntryRequest");
        });

        modelBuilder.Entity<RequestSyncState>(entity =>
        {
            entity.ToTable("RequestSyncStates");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RequestId, "IX_FK_RequestSyncStateRequest");

            entity.Property(e => e.Changed).IsStoredAsDateTime();
            entity.Property(e => e.Created).IsStoredAsDateTime();

            entity
                .HasOne(d => d.Request)
                .WithMany(p => p.RequestSyncStates)
                .HasForeignKey(d => d.RequestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestSyncStateRequest");
        });

        modelBuilder.Entity<RequestType>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RequestTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Abbreviation).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.CreateStatus).HasColumnOrder(iota);
            entity.Property(e => e.AvailableStatuses).HasColumnOrder(iota);
            entity.Property(e => e.AvailableIntervalTypes).HasColumnOrder(iota);
            entity.Property(e => e.LongTerm).HasColumnOrder(iota);
            entity.Property(e => e.Disabled).HasColumnOrder(iota);
            entity.Property(e => e.IsGlobal).HasColumnOrder(iota);
            entity.Property(e => e.ShiftsSelection).HasColumnOrder(iota);
            entity.Property(e => e.ImplicitAssignment).HasColumnOrder(iota);
            entity.Property(e => e.AllPeriods).HasColumnOrder(iota);
            entity.Property(e => e.Parameters).HasMaxLength(1024).HasColumnOrder(iota);
            entity.Property(e => e.ProcessPermission).HasColumnOrder(iota);
            entity.Property(e => e.PreProcessPermission).HasColumnOrder(iota);
            entity.Property(e => e.CreatePermission).HasColumnOrder(iota);
            entity.Property(e => e.Anywhere).HasColumnOrder(iota);
            entity.Property(e => e.RequiredFutureDays).HasColumnOrder(iota);
            entity.Property(e => e.AllowDateTimeRange).HasColumnOrder(iota);
            entity.Property(e => e.AllowDateRange).HasColumnOrder(iota);

            entity
                .HasMany(d => d.Properties)
                .WithMany(p => p.RequestTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RequestTypeProperty",
                    r =>
                        r.HasOne<Property>()
                            .WithMany()
                            .HasForeignKey("PropertiesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestTypeProperty_Property"),
                    l =>
                        l.HasOne<RequestType>()
                            .WithMany()
                            .HasForeignKey("RequestTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RequestTypeProperty_RequestType"),
                    j =>
                    {
                        j.HasKey("RequestTypesId", "PropertiesId");
                        j.ToTable("RequestTypeProperty");
                        j.HasIndex(["PropertiesId"], "IX_FK_RequestTypeProperty_Property");
                        j.IndexerProperty<RequestTypeId>("RequestTypesId").HasColumnName("RequestTypes_Id");
                        j.IndexerProperty<PropertyId>("PropertiesId").HasColumnName("Properties_Id");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.RosterRequestTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RosterRequestTypeSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RosterRequestTypeSite_Site"),
                    l =>
                        l.HasOne<RequestType>()
                            .WithMany()
                            .HasForeignKey("RosterRequestTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RosterRequestTypeSite_RosterRequestType"),
                    j =>
                    {
                        j.HasKey("RosterRequestTypesId", "SitesId");
                        j.ToTable("RosterRequestTypeSite");
                        j.HasIndex(["SitesId"], "IX_FK_RosterRequestTypeSite_Site");
                        j.IndexerProperty<RequestTypeId>("RosterRequestTypesId").HasColumnName("RosterRequestTypes_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<RequestTypeFilter>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RequestTypeFilters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.FilterId).HasColumnOrder(iota);
            entity.HasIndex(e => e.FilterId, "IX_FK_RequestTypeFilterFilter");

            entity.Property(e => e.RequestTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RequestTypeId, "IX_FK_RequestTypeRequestTypeFilter");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.RequestTypeFilters)
                .HasForeignKey(d => d.FilterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeFilterFilter");

            entity
                .HasOne(d => d.RequestType)
                .WithMany(p => p.RequestTypeFilters)
                .HasForeignKey(d => d.RequestTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeRequestTypeFilter");
        });

        modelBuilder.Entity<RequestTypeActivity>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeActivity");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.ActivityId).HasColumnOrder(0);
            entity.Property(e => e.Id).HasColumnOrder(1);

            entity.HasIndex(e => e.ActivityId, "IX_FK_RequestTypeActivityActivityType");

            entity
                .HasOne(d => d.Activity)
                .WithMany(p => p.ActivityRequestTypes)
                .HasForeignKey(d => d.ActivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeActivityActivityType");

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeActivity>(d => d.Id)
                .HasConstraintName("FK_RequestTypeActivity_inherits_RequestType");
        });

        modelBuilder.Entity<RequestTypeBreak>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeBreak");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.BreakId).HasColumnOrder(0);
            entity.Property(e => e.Id).HasColumnOrder(1);

            entity.HasIndex(e => e.BreakId, "IX_FK_RequestTypeBreakBreakType");

            entity
                .HasOne(d => d.Break)
                .WithMany(p => p.BreakRequestTypes)
                .HasForeignKey(d => d.BreakId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeBreakBreakType");

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeBreak>(d => d.Id)
                .HasConstraintName("FK_RequestTypeBreak_inherits_RequestType");
        });

        modelBuilder.Entity<RequestTypeChangeShift>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeChangeShift");
            entity.HasKey(e => e.Id);

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeChangeShift>(d => d.Id)
                .HasConstraintName("FK_RequestTypeChangeShift_inherits_RequestType");
        });

        modelBuilder.Entity<RequestTypeFreeTime>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeFreeTime");
            entity.HasKey(e => e.Id);

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeFreeTime>(d => d.Id)
                .HasConstraintName("FK_RequestTypeFreeTime_inherits_RequestType");
        });

        modelBuilder.Entity<RequestTypeNewShift>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeNewShift");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.ActivityId).HasColumnOrder(0);
            entity.Property(e => e.ParallelActivityId).HasColumnOrder(1);
            entity.Property(e => e.Id).HasColumnOrder(2);
            entity.Property(e => e.LabelActivityId).HasColumnOrder(3);
            entity.Property(e => e.OvertimeActivityId).HasColumnOrder(4);

            entity.HasIndex(e => e.ActivityId, "IX_FK_RequestTypeNewShiftActivityType");

            entity.HasIndex(e => e.ParallelActivityId, "IX_FK_RequestTypeNewShiftActivityType1");

            entity.HasIndex(e => e.LabelActivityId, "IX_FK_RequestTypeNewShiftActivityTypeLabel");

            entity.HasIndex(e => e.OvertimeActivityId, "IX_FK_RequestTypeNewShiftActivityTypeOvertime");

            entity
                .HasOne(d => d.Activity)
                .WithMany(p => p.NewShiftRequestTypes)
                .HasForeignKey(d => d.ActivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeNewShiftActivityType");

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeNewShift>(d => d.Id)
                .HasConstraintName("FK_RequestTypeNewShift_inherits_RequestType");

            entity
                .HasOne(d => d.ParallelActivity)
                .WithMany(p => p.NewParallelShiftRequestTypes)
                .HasForeignKey(d => d.ParallelActivityId)
                .HasConstraintName("FK_RequestTypeNewShiftActivityType1");

            entity
                .HasOne(d => d.LabelActivity)
                .WithMany(p => p.NewLabelShiftRequestTypes)
                .HasForeignKey(d => d.LabelActivityId)
                .HasConstraintName("FK_RequestTypeNewShiftActivityTypeLabel");

            entity
                .HasOne(d => d.OvertimeActivity)
                .WithMany(p => p.NewOvertimeShiftRequestTypes)
                .HasForeignKey(d => d.OvertimeActivityId)
                .HasConstraintName("FK_RequestTypeNewShiftActivityTypeOvertime");
        });

        modelBuilder.Entity<RequestTypeTimeOff>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeTimeOff");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.TimeOffId).HasColumnOrder(0);
            entity.Property(e => e.Id).HasColumnOrder(1);

            entity.HasIndex(e => e.TimeOffId, "IX_FK_RequestTypeTimeOffTimeOffType");

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeTimeOff>(d => d.Id)
                .HasConstraintName("FK_RequestTypeTimeOff_inherits_RequestType");

            entity
                .HasOne(d => d.TimeOff)
                .WithMany(p => p.TimeOffs)
                .HasForeignKey(d => d.TimeOffId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeTimeOffTimeOffType");
        });

        modelBuilder.Entity<RequestTypeWork>(entity =>
        {
            entity.ToTable("RequestTypes_RequestTypeWork");
            entity.HasKey(e => e.Id);

            entity
                .HasOne(d => d.RequestType)
                .WithOne()
                .HasForeignKey<RequestTypeWork>(d => d.Id)
                .HasConstraintName("FK_RequestTypeWork_inherits_RequestType");
        });

        modelBuilder.Entity<TradeAnswer>(entity =>
        {
            entity.ToTable("TradeAnswers");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RosterItemId, "IX_FK_RosterItemTradeAnswer");

            entity.HasIndex(e => e.TradeOfferId, "IX_FK_TradeOfferTradeAnswer");

            entity.Property(e => e.Created).IsStoredAsDateTime();

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.TradeAnswers)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemTradeAnswer");

            entity
                .HasOne(d => d.TradeOffer)
                .WithMany(p => p.TradeAnswers)
                .HasForeignKey(d => d.TradeOfferId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TradeOfferTradeAnswer");
        });

        modelBuilder.Entity<TradeOffer>(entity =>
        {
            entity.ToTable("TradeOffers");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.ChangeRequestId, "IX_FK_ChangeRequestTradeOffer");

            entity.HasIndex(e => e.RosterItemId, "IX_FK_TradeOfferRosterItem");

            entity.HasIndex(e => e.RosterItemId2, "IX_FK_TradeOfferRosterItem2");

            entity.Property(e => e.ChangeRequestId).HasColumnName("ChangeRequest_Id");
            entity.Property(e => e.Created).IsStoredAsDateTime();

            entity
                .HasOne(d => d.ChangeRequest)
                .WithMany(p => p.TradeOffers)
                .HasForeignKey(d => d.ChangeRequestId)
                .HasConstraintName("FK_ChangeRequestTradeOffer");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.TradeOfferRosterItems)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_TradeOfferRosterItem");

            entity
                .HasOne(d => d.RosterItem2)
                .WithMany(p => p.TradeOfferRosterItems2)
                .HasForeignKey(d => d.RosterItemId2)
                .HasConstraintName("FK_TradeOfferRosterItem2");
        });
    }
}
