﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <WarningsAsErrors>Nullable</WarningsAsErrors>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
    <RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\ApiClient\ApiClient.csproj" />
    <ProjectReference Include="..\SharedResources\SharedResources.csproj" />

    <ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
    <ProjectReference Include="..\Primitives\Primitives.csproj" />
    <ProjectReference Include="..\DataStructures\DataStructures.csproj" />
    <ProjectReference Include="..\Utilities\Utilities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" />
  </ItemGroup>

</Project>
