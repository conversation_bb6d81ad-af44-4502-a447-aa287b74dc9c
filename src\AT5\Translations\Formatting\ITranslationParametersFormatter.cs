﻿namespace AT.Translations.Formatting;

public interface ITranslationParametersFormatter
{
    /// <summary>
    /// The keys of the dictionary are the names of the parameters. Not using Vogen types in here
    /// because we typically pass this dictionary into the list of arguments (string.Format's or SmartFormat's),
    /// which could be problematic.
    /// </summary>
    Dictionary<string, object?> Format(
        string messageTemplate,
        ITranslationWithParameters translation,
        Language? language = null
    );
}
