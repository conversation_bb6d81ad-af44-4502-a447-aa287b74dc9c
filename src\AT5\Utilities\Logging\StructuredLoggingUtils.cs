﻿namespace AT.Utilities.Logging;

using System.Collections.Generic;
using System.Diagnostics;

public static class StructuredLoggingUtils
{
    private const string organizationName = "aristotelos.organization.name";
    private const string alwaysInclude = "AlwaysInclude";

    /// <summary>
    /// Sets the organization name as a tag on the current Activity.
    /// This tag is propagated through OpenTelemetry and appears as a custom dimension
    /// in Application Insights for the request.
    /// </summary>
    /// <param name="organizationName">The name of the organization to set.</param>
    public static void SetOrganizationName(string organizationName)
    {
        Activity.Current?.SetTag(StructuredLoggingUtils.organizationName, organizationName);
    }

    /// <summary>
    /// Creates a log state dictionary containing the organization name.
    /// This dictionary can be used with ILogger's BeginScope to propagate
    /// the organization name as a custom dimension in logs.
    /// </summary>
    /// <param name="organizationName">The name of the organization to include in the log state.</param>
    /// <returns>A dictionary containing the organization name as a log scope property.</returns>
    public static Dictionary<string, object> CreateLogState(string organizationName)
    {
        return new Dictionary<string, object>() { { StructuredLoggingUtils.organizationName, organizationName } };
    }

    /// <summary>
    /// Marks the current activity to always be included in telemetry data.
    /// Ensures that the request associated with an exception is not excluded due to sampling.
    /// </summary>
    public static void MarkCurrentActivityAsAlwaysInclude()
    {
        Activity.Current?.SetTag(alwaysInclude, true);
    }

    /// <summary>
    /// Checks if the current activity is marked to always be included in telemetry data.
    /// </summary>
    /// <returns>True if the current activity is marked as always included; otherwise, false.</returns>
    public static bool IsActivityMarkedAsAlwaysInclude(Activity activity)
    {
        return activity.GetTagItem(alwaysInclude) as bool? == true;
    }
}
