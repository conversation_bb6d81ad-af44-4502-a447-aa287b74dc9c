﻿namespace AT.Core.Domain.Entities;

public class EmployeeContract
{
    public EmployeeContractId Id { get; set; }

    public ContractId ContractId { get; set; }

    public UserId EmployeeId { get; set; }

    public SiteId SiteId { get; set; }

    public Validity Validity { get; set; }

    public Validity ShiftPlanning { get; set; }

    public string? Code { get; set; }

    public virtual Contract Contract { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    public virtual ICollection<EmployeeDefaultWorkingTimeModel> EmployeeDefaultWorkingTimeModels { get; set; } =
        new List<EmployeeDefaultWorkingTimeModel>();

    public virtual ICollection<EmployeeWorkingTimeModel> EmployeeWorkingTimeModels { get; set; } =
        new List<EmployeeWorkingTimeModel>();

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<Site> AllowedSites { get; set; } = new List<Site>();
}
