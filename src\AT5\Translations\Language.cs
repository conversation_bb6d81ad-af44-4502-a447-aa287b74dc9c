﻿namespace AT.Translations;

using System.Globalization;
using Ardalis.SmartEnum;

public sealed class Language : SmartEnum<Language>
{
    public static readonly Language Invariant = new(nameof(Invariant), value: 1, LanguageLocaleName.From(""));
    public static readonly Language English_US = new(nameof(English_US), value: 2, LanguageLocaleName.From("en-US"));
    public static readonly Language English_GB = new(nameof(English_GB), value: 3, LanguageLocaleName.From("en-GB"));
    public static readonly Language Czech = new(nameof(Czech), value: 4, LanguageLocaleName.From("cs-CZ"));
    public static readonly Language Slovak = new(nameof(Slovak), value: 5, LanguageLocaleName.From("sk-SK"));
    public static readonly Language Hungarian = new(nameof(Hungarian), value: 6, LanguageLocaleName.From("hu-HU"));
    public static readonly Language Polish = new(nameof(Polish), value: 7, LanguageLocaleName.From("pl-PL"));

    public LanguageLocaleName LanguageLocaleName { get; init; }

    public CultureInfo CultureInfo { get; init; }

    private Language(string name, int value, LanguageLocaleName languageLocaleName)
        : base(name, value)
    {
        LanguageLocaleName = languageLocaleName;
        CultureInfo = CultureInfo.GetCultureInfo(languageLocaleName.Value);
    }
}
