﻿namespace AT.Infrastructure.DependencyInjection;

using System.Collections.Concurrent;
using AT.Core.Domain.Base;
using AT.Core.MasterDomain.Entities;
using Autofac;
using Autofac.Multitenant;

public sealed class AppServiceProvider(MultitenantContainer _multitenantContainer) : IAppServiceProvider
{
    private readonly Lock _tenantConfigurationLock = new();
    private readonly ConcurrentDictionary<OrganizationId, bool> _configuredOrganizations =
        new ConcurrentDictionary<OrganizationId, bool>();

    public IOrgServiceProvider GetOrgServiceProvider(OrganizationId orgId)
    {
        return GetTenantLifetimeScope(orgId).Resolve<IOrgServiceProvider>();
    }

    public TService Resolve<TService>()
        where TService : class
    {
        return _multitenantContainer.ApplicationContainer.Resolve<TService>();
    }

    public ILifetimeScope GetTenantLifetimeScopeUntyped(object? tenantId)
    {
        if (tenantId is OrganizationId orgId)
        {
            return GetTenantLifetimeScope(orgId);
        }

        if (tenantId is null)
        {
            return GetDefaultTenantLifetimeScope();
        }

        throw new ArgumentException(
            $"Invalid {nameof(tenantId)} type {tenantId.GetType()}",
            paramName: nameof(tenantId)
        );
    }

    public ILifetimeScope GetTenantLifetimeScope(OrganizationId orgId)
    {
        if (!_configuredOrganizations.ContainsKey(orgId))
        {
            ConfigureTenantIfNotConfigured(orgId);
        }

        return _multitenantContainer.GetTenantScope(orgId);
    }

    public ILifetimeScope GetDefaultTenantLifetimeScope()
    {
        // Default tenant must be configured on creation.
        return _multitenantContainer.GetTenantScope(null);
    }

    private void ConfigureTenantIfNotConfigured(OrganizationId orgId)
    {
        // We want to configure each tenant once.
        lock (_tenantConfigurationLock)
        {
            if (_configuredOrganizations.ContainsKey(orgId))
            {
                return; // Already configured.
            }

            // In ApiService MultitenantContainer is always first to create a tenant (currently) so we have to reconfigure it here.
            _multitenantContainer.ReconfigureTenant(
                orgId,
                containerBuilder => ConfigureTenant(containerBuilder, orgId)
            );
            _configuredOrganizations.TryAdd(orgId, true);
        }
    }

    public static void ConfigureTenant(ContainerBuilder containerBuilder, OrganizationId orgId)
    {
        containerBuilder.RegisterInstance(new TenantIdProvider(_organizationId: orgId)).AsSelf();
    }

    public static void ConfigureDefaultTenant(ContainerBuilder containerBuilder)
    {
        containerBuilder.RegisterInstance(new TenantIdProvider(_organizationId: null)).AsSelf();
    }
}
