﻿namespace AT.Core.Domain.Entities;

using Base;

public class ExternalStatus
{
    public ExternalStatusId Id { get; set; }

    public string Name { get; set; } = null!;

    public int? ExternalId { get; set; }

    public string ExternalCode { get; set; } = null!;

    public int? ReasonId { get; set; }

    public string? ReasonCode { get; set; }

    public StatusTypeId? StatusTypeId { get; set; }

    public int Rank { get; set; }

    public virtual StatusType? StatusType { get; set; }

    public virtual ICollection<StatusType> ClosingExternalStatusesExternalStatuses { get; set; } =
        new List<StatusType>();
}
