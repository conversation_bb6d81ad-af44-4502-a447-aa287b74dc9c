﻿namespace AT.DataStructures.Time;

using System.Text.Json.Serialization;

public readonly partial record struct OpenDateInterval
{
    /// <summary>
    /// Gets the duration of the interval. Null represents infinity.
    /// </summary>
    public TimeSpan? Duration
    {
        get
        {
            if (IsEmpty)
            {
                return TimeSpan.Zero;
            }

            if (!Start.HasValue || !End.HasValue)
            {
                return null;
            }

            return TimeSpan.FromDays(End.Value.DayNumber - Start.Value.DayNumber + 1);
        }
    }

    /// <summary>
    /// Gets the start of the interval as a <see cref="DateTime"/> at the beginning of the day.
    /// </summary>
    public DateTime? StartDateTime => Start?.ToDateTime(TimeOnly.MinValue);

    /// <summary>
    /// Gets the end of the interval as a <see cref="DateTime"/> at the end of the day. Inclusive.
    /// </summary>
    public DateTime? EndDateTime => End?.ToDateTime(TimeOnly.MaxValue);

    /// <summary>
    /// Implicitly converts a <see cref="OpenDateInterval"/> to an <see cref="OpenDateTimeInterval"/> using <see cref="ToOpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="interval">The <see cref="OpenDateInterval"/> to convert.</param>
    public static implicit operator OpenDateTimeInterval(OpenDateInterval interval)
    {
        return interval.ToOpenDateTimeInterval();
    }

    /// <summary>
    /// Converts a <see cref="OpenDateInterval"/> to an <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <returns>An <see cref="OpenDateTimeInterval"/> representing the same interval.</returns>
    public OpenDateTimeInterval ToOpenDateTimeInterval()
    {
        return new OpenDateTimeInterval(
            Start?.ToDateTime(TimeOnly.MinValue),
            End?.ToDateTime(TimeOnly.MaxValue).AddTicks(1)
        );
    }
}
