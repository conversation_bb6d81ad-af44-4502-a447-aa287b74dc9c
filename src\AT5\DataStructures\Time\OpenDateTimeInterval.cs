﻿namespace AT.DataStructures.Time;

using System.Text.Json.Serialization;

public readonly partial record struct OpenDateTimeInterval
{
    /// <summary>
    /// Gets the duration of the interval. Null represents infinity.
    /// </summary>
    public TimeSpan? Duration
    {
        get
        {
            if (IsEmpty)
            {
                return TimeSpan.Zero;
            }

            if (!Start.HasValue || !End.HasValue)
            {
                return null;
            }

            return End - Start;
        }
    }

    /// <summary>
    /// Gets the start date of the interval as a <see cref="DateOnly"/>.
    /// </summary>
    public DateOnly? StartDate => Start.HasValue ? DateOnly.FromDateTime(Start.Value.Date) : null;

    /// <summary>
    /// Gets the end date of the interval as a <see cref="DateOnly"/>.
    /// </summary>
    public DateOnly? EndDate
    {
        get
        {
            if (!End.HasValue)
            {
                return null;
            }

            return DateOnly.FromDateTime(End.Value.TimeOfDay == TimeSpan.Zero ? End.Value.AddDays(-1) : End.Value.Date);
        }
    }

    /// <summary>
    /// Explicitly converts an <see cref="OpenDateTimeInterval"/> to a <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="interval">The interval to convert.</param>
    /// <returns>A <see cref="DateInterval"/> representing the same range.</returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the start or end values are not set, or if they are not aligned to date boundaries.
    /// </exception>
    public static explicit operator DateInterval(OpenDateTimeInterval interval)
    {
        return interval.ToDateInterval();
    }

    /// <summary>
    /// Converts this <see cref="OpenDateTimeInterval"/> to a <see cref="DateInterval"/>.
    /// </summary>
    /// <returns>A <see cref="DateInterval"/> representing the same range.</returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the start or end values are not set, or if they are not aligned to date boundaries.
    /// </exception>
    public DateInterval ToDateInterval()
    {
        if (!Start.HasValue || !End.HasValue)
        {
            throw new InvalidOperationException(
                $"Both {nameof(Start)} and {nameof(End)} values must be set to create a {nameof(DateInterval)}."
            );
        }

        if (Start != Start?.Date || End != End?.Date)
        {
            throw new InvalidOperationException(
                $"{nameof(Start)} and {nameof(End)} must be aligned to date boundaries to convert to {nameof(DateInterval)}."
            );
        }

        var startDate = DateOnly.FromDateTime(Start.Value);
        var endDate = DateOnly.FromDateTime(End.Value);
        return new DateInterval(startDate, endDate);
    }

    /// <summary>
    /// Explicitly converts an <see cref="OpenDateTimeInterval"/> to an <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="interval">The interval to convert.</param>
    /// <returns>An <see cref="OpenDateInterval"/> representing the same range.</returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the start or end values are not aligned to date boundaries.
    /// </exception>
    public static explicit operator OpenDateInterval(OpenDateTimeInterval interval)
    {
        return interval.ToOpenDateInterval();
    }

    /// <summary>
    /// Converts this <see cref="OpenDateTimeInterval"/> to an <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <returns>An <see cref="OpenDateInterval"/> representing the same range.</returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the start or end values are not aligned to date boundaries.
    /// </exception>
    public OpenDateInterval ToOpenDateInterval()
    {
        if (Start != Start?.Date || End != End?.Date)
        {
            throw new InvalidOperationException(
                $"{nameof(Start)} and {nameof(End)} must be aligned to date boundaries to convert to {nameof(OpenDateInterval)}."
            );
        }

        var startDate = Start.HasValue ? DateOnly.FromDateTime(Start.Value) : default(DateOnly?);
        var endDate = End.HasValue ? DateOnly.FromDateTime(End.Value) : default(DateOnly?);
        return new OpenDateInterval(startDate, endDate);
    }

    /// <summary>
    /// Explicitly converts an <see cref="OpenDateTimeInterval"/> to a <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="interval">The interval to convert.</param>
    /// <returns>A <see cref="DateTimeInterval"/> representing the same range.</returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the start or end values are not set.
    /// </exception>
    public static explicit operator DateTimeInterval(OpenDateTimeInterval interval)
    {
        return interval.ToDateTimeInterval();
    }

    /// <summary>
    /// Converts this <see cref="OpenDateTimeInterval"/> to a <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <returns>A <see cref="DateTimeInterval"/> representing the same range.</returns>
    /// <exception cref="InvalidOperationException">
    /// Thrown if the start or end values are not set.
    /// </exception>
    public DateTimeInterval ToDateTimeInterval()
    {
        if (!Start.HasValue || !End.HasValue)
        {
            throw new InvalidOperationException(
                $"Both {nameof(Start)} and {nameof(End)} values must be set to create a {nameof(DateTimeInterval)}."
            );
        }

        return new DateTimeInterval(Start.Value, End.Value);
    }
}
