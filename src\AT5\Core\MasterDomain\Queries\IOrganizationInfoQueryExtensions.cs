﻿namespace AT.Core.MasterDomain.Queries;

public static class IOrganizationInfoQueryExtensions
{
    public static OrganizationInfo GetOrganizationInfoOrThrow(this IOrganizationInfoQuery query, OrganizationId id)
    {
        var orgInfo = query.TryGetOrganizationInfo(id);
        if (orgInfo is null)
        {
            throw new ArgumentException($"Organization with Id {id} not found.", paramName: nameof(id));
        }

        return orgInfo;
    }
}
