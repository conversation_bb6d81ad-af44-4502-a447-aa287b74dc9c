﻿namespace AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;

public class ScheduledPushNotificationsSpec : Specification<PushNotification>
{
    public ScheduledPushNotificationsSpec(DateTime untilExclusive, int? batchSize = null)
    {
        Query.Where(pn => pn.SendTime < untilExclusive).Include(pn => pn.PushNotificationRecipients);

        if (batchSize > 0)
        {
            Query.Take(batchSize.Value);
        }
    }
}
