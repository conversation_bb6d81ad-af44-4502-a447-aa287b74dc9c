﻿namespace AT.Primitives.Enums;

public enum JobType
{
    None = 0,
    NullJob = 1,
    AlbertCalculateProductivityJob = 2,
    ImportAlbertEmployeesJob = 3,
    ReportJob = 4,
    AgencyWorkersDeactivationJob = 5,
    BreaksPlanningJob = 6,
    CsobGenesysImportVolumesJob = 7,
    DaktelaImportVolumesRestJob = 8,
    DaktelaImportEmailsJob = 9,
    DaktelaImportActivitiesJob = 10,
    AxaKsImportEmployeeEntitlementsJob = 11,
    PayrollExportJob = 12,
    CheckAgentStatesJob = 13,
    InnogyEmailsLoadVolumesJob = 14,
    DeactivateEmployeesJob = 15,
    InnogyImportMluviiChatsJob = 16,
    PlanningJob = 17,
    TMobileSynchronizationJob = 18,
    TMobileImportRosterItemsJob = 19,
    ImportVolumesServiceNetRexJob = 20,

    // EmployeeConfirmationsReportJob = 21, Removed.
    RosterItemPushNotificationsJob = 22,
    UcsLoadVolumesJob = 23,
    StatesShiftUpdateJob = 24,
    DaktelaLoadAgentStatesJob = 25,
    ReportForManagerJob = 26,
    AutogenerateAndPublishShiftsJob = 27,
    AsteaLoadAgentStatesJob = 28,
    AgentStatesConnectionCheckJob = 29,
    SFApiExportJob = 30,
    ImportVolumesFromFileJob = 31,
    ImportAgentStatesFromFileJob = 32,
    DownloadDataHubDataJob = 33,
    AlbertPositionsImportJob = 34,
    AlbertSitesImportJob = 35,
    AlbertPersonalDataImportJob = 36,
    AlbertHourlyWagesImportJob = 37,
    AlbertAgencyEmployeesDeactivationJob = 38,
    AlbertVolunteeringDayImportJob = 39,
    PositionsDataImporterJob = 40,
    OrganizationUnitsImporterJob = 41,
    PersonalDataImporterJob = 42,
}
