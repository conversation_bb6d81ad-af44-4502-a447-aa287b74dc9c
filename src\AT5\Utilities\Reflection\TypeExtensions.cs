﻿namespace AT.Utilities.Reflection;

public static class TypeExtensions
{
    /// <summary>
    /// Generates short version of type name including generic arguments.
    /// </summary>
    public static string GetShortName(this Type type)
    {
        // type.Name doesn't return name of generic type arguments if type is a generic type.
        // type.FullName is very, very long, so we format the name together with type arguments ourselves.
        var typeName = type.Name;

        var genericTypeArguments = type.GenericTypeArguments;
        if (genericTypeArguments.Length > 0)
        {
            var typeArgumentNames = genericTypeArguments.Select(typeArg => typeArg.Name);
            typeName = $"{typeName}<{string.Join(',', typeArgumentNames)}>";
        }

        return typeName;
    }

    /// <summary>
    /// There is no built-in way to check whether class is a record. We use this heuristic approach
    /// to determine that. More info: https://stackoverflow.com/a/64810188.
    /// </summary>
    public static bool IsRecordType(this Type type)
    {
        return type.IsClass && type.GetMethod("<Clone>$") is not null;
    }

    public static bool IsStaticClass(this Type type)
    {
        return type.IsClass && type.IsAbstract && type.IsSealed;
    }
}
