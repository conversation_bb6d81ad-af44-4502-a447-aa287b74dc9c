﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;

public class WorkOrderItem
{
    public long Id { get; set; }

    public DateTime PlannedStart { get; set; }

    public DateTime Deadline { get; set; }

    public DateTime? Preparation { get; set; }

    public double Volume { get; set; }

    public double Weight { get; set; }

    public WorkOrderId WorkOrderId { get; set; }

    public UserId? EmployeeId { get; set; }

    public PreparationWayId? PreparationWayId { get; set; }

    public int? ShopNo { get; set; }

    public int? RouteNo { get; set; }

    public int? LotNo { get; set; }

    public int? ArtRn { get; set; }

    public long? SystemId { get; set; }

    public string? SystemCode { get; set; }

    public virtual Employee? Employee { get; set; }

    public virtual PreparationWay? PreparationWay { get; set; }

    public virtual WorkOrder WorkOrder { get; set; } = null!;
}
