﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class WorkingTimeModel
{
    public WorkingTimeModelId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Code { get; set; }

    public string? Description { get; set; }

    public bool IsGlobal { get; set; }

    public WorkingTimeId WorkingTimeId { get; set; }

    public WorkingTimeModelId? ParentModelId { get; set; }

    public string? Parameters { get; set; }

    public AutoGenerateShifts AutoGenerateShifts { get; set; }

    public BalanceConfigurationId? BalanceConfigurationId { get; set; }

    public virtual BalanceConfiguration? BalanceConfiguration { get; set; }

    public virtual ICollection<EmployeeDefaultWorkingTimeModel> EmployeeDefaultWorkingTimeModels { get; set; } =
        new List<EmployeeDefaultWorkingTimeModel>();

    public virtual ICollection<EmployeeWorkingTimeModel> EmployeeWorkingTimeModels { get; set; } =
        new List<EmployeeWorkingTimeModel>();

    public virtual ICollection<WorkingTimeModel> InverseParentModel { get; set; } = new List<WorkingTimeModel>();

    public virtual WorkingTimeModel? ParentModel { get; set; }

    public virtual WorkingTime WorkingTime { get; set; } = null!;

    public virtual ICollection<WorkingTimeModelShiftSystem> WorkingTimeModelShiftSystems { get; set; } =
        new List<WorkingTimeModelShiftSystem>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
