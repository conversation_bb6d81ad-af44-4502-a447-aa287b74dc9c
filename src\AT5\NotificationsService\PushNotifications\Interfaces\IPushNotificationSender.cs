﻿namespace AT.NotificationsService.PushNotifications.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;

public interface IPushNotificationSender
{
    Task<PushNotificationSendToSingleResult> SendPushNotificationToSingleAsync(
        string token,
        PushNotification pushNotification,
        bool dryRun = false,
        CancellationToken cancellationToken = default
    );

    Task<PushNotificationSendToMultipleResult> SendPushNotificationToMultipleAsync(
        IReadOnlyList<string> tokens,
        PushNotification pushNotification,
        bool dryRun = false,
        CancellationToken cancellationToken = default
    );
}
