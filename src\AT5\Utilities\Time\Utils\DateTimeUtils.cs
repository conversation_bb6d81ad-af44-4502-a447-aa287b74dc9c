﻿namespace AT.Utilities.Time.Utils;

/// <summary>
/// Provides utility methods for performing common operations on <see cref="DateTime"/> and nullable <see cref="DateTime"/> values.
/// </summary>
public static class DateTimeUtils
{
    public static DateTime CreateLocal(int year, int month, int day)
    {
        return new DateTime(year, month, day, 0, 0, 0, DateTimeKind.Local);
    }

    /// <summary>
    /// Returns the later of two <see cref="DateTime"/> values.
    /// </summary>
    /// <param name="first">The first <see cref="DateTime"/> value.</param>
    /// <param name="second">The second <see cref="DateTime"/> value.</param>
    /// <returns>The later of the two <see cref="DateTime"/> values.</returns>
    public static DateTime Max(DateTime first, DateTime second)
    {
        return first >= second ? first : second;
    }

    /// <summary>
    /// Returns the later of two nullable <see cref="DateTime"/> values, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateTime"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateTime? Max(DateTime? first, DateTime? second)
    {
        if (first.HasValue && second.HasValue)
        {
            return Max(first.Value, second.Value);
        }

        return first ?? second;
    }

    /// <summary>
    /// Returns the later of two <see cref="DateTime"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateTime"/> value.</param>
    /// <param name="second">The second <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateTime Max(DateTime? first, DateTime second)
    {
        if (!first.HasValue)
        {
            return second;
        }

        return Max(first.Value, second);
    }

    /// <summary>
    /// Returns the later of two <see cref="DateTime"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first <see cref="DateTime"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateTime Max(DateTime first, DateTime? second)
    {
        return Max(second, first);
    }

    /// <summary>
    /// Returns the earlier of two <see cref="DateTime"/> values.
    /// </summary>
    /// <param name="first">The first <see cref="DateTime"/> value.</param>
    /// <param name="second">The second <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two <see cref="DateTime"/> values.</returns>
    public static DateTime Min(DateTime first, DateTime second)
    {
        return first <= second ? first : second;
    }

    /// <summary>
    /// Returns the earlier of two nullable <see cref="DateTime"/> values, treating <c>null</c> as the largest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateTime"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the largest value.</returns>
    public static DateTime? Min(DateTime? first, DateTime? second)
    {
        if (first.HasValue && second.HasValue)
        {
            return Min(first.Value, second.Value);
        }

        return first ?? second;
    }

    /// <summary>
    /// Returns the earlier of two <see cref="DateTime"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first nullable <see cref="DateTime"/> value.</param>
    /// <param name="second">The second <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateTime Min(DateTime? first, DateTime second)
    {
        if (!first.HasValue)
        {
            return second;
        }

        return Min(first.Value, second);
    }

    /// <summary>
    /// Returns the earlier of two <see cref="DateTime"/> values, where one of them is nullable, treating <c>null</c> as the smallest value.
    /// </summary>
    /// <param name="first">The first <see cref="DateTime"/> value.</param>
    /// <param name="second">The second nullable <see cref="DateTime"/> value.</param>
    /// <returns>The earlier of the two values, treating <c>null</c> as the larger value.</returns>
    public static DateTime Min(DateTime first, DateTime? second)
    {
        return Min(second, first);
    }

    /// <summary>
    /// Converts a <see cref="DateTime"/> to a <see cref="DateOnly"/>.
    /// </summary>
    /// <param name="dateTime">The <see cref="DateTime"/> value to convert.</param>
    /// <returns>
    /// A <see cref="DateOnly"/> representing the date component of the input value.
    /// </returns>
    public static DateOnly ToDateOnly(this DateTime dateTime)
    {
        return DateOnly.FromDateTime(dateTime);
    }

    /// <summary>
    /// Converts a nullable <see cref="DateTime"/> to a nullable <see cref="DateOnly"/>.
    /// </summary>
    /// <param name="dateTime">The nullable <see cref="DateTime"/> value to convert.</param>
    /// <returns>
    /// A nullable <see cref="DateOnly"/> representing the date component of the input value,
    /// or <c>null</c> if the input is <c>null</c>.
    /// </returns>
    public static DateOnly? ToDateOnly(this DateTime? dateTime)
    {
        return dateTime?.ToDateOnly();
    }
}
