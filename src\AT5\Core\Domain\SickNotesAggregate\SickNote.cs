﻿namespace AT.Core.Domain.SickNotesAggregate;

using AT.Core.Domain.RosterItemAggregate;

public class SickNote : IAggregateRoot
{
    public SickNoteId Id { get; set; }

    public string SickNoteCode { get; set; } = null!;

    public string PersonalId { get; set; } = null!;

    public UserId? EmployeeId { get; set; }

    public DateOnly From { get; set; }

    public DateOnly? To { get; set; }

    public string IllnessTypeCode { get; set; } = null!;

    public bool WorkAccident { get; set; }

    public DateOnly LastChangeDate { get; set; }

    public bool SynchronizeStart { get; set; }

    public bool SynchronizeEnd { get; set; }

    public bool SynchronizeType { get; set; }

    public int TimeOffUpdateFailCount { get; set; }

    public DateTime Created { get; set; }

    public DateTime Changed { get; set; }

    public virtual Employee? Employee { get; set; }

    public virtual RosterItem? RosterItem { get; set; }
}
