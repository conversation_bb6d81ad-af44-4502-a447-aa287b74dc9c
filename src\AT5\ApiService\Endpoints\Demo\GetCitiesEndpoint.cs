﻿namespace AT.ApiService.Endpoints.Demo;

using System.Threading;
using System.Threading.Tasks;
using AT.Shared.Models.Demo;
using FastEndpoints;

public class GetCitiesEndpoint(CitiesRepository citiesRepository) : Endpoint<GetCitiesRequest, GetCitiesResponse>
{
    public override void Configure()
    {
        Get("/demo/cities");
        AllowAnonymous();
    }

    public override async Task HandleAsync(GetCitiesRequest req, CancellationToken ct)
    {
        var cities = citiesRepository.GetCities();
        var filteredCities = cities.AsEnumerable();
        if (req.MinId.HasValue)
        {
            filteredCities = filteredCities.Where(x => x.Id >= req.MinId.Value);
        }

        if (req.MaxId.HasValue)
        {
            filteredCities = filteredCities.Where(x => x.Id <= req.MaxId.Value);
        }

        cities = filteredCities.ToArray();

        await SendAsync(new GetCitiesResponse() { Cities = cities, }, cancellation: ct);
    }
}
