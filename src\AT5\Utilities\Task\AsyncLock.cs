﻿namespace AT.Utilities.Task;

using System.Threading.Tasks;

/// <summary>
/// A simple lock that is usable in async code.
///
/// Interface is modelled on the System.Threading.Lock class.
/// FUTURE: Consider using the NEXT library https://dotnet.github.io/dotNext/features/threading/index.html
/// </summary>
public sealed class AsyncLock
{
    private readonly SemaphoreSlim _lockingSemaphore = new(1, 1);

    /// <summary>
    /// This is the preferred method to use.
    /// Example:
    /// <code>
    /// using (await _asyncLock.EnterScope())
    /// {
    ///     Write your critical section here.
    /// }
    /// </code>
    /// </summary>
    public async Task<AsyncLockScope> EnterScope(CancellationToken ct = default)
    {
        await Enter(ct);
        return new AsyncLockScope(this);
    }

    public async Task Enter(CancellationToken ct = default)
    {
        await _lockingSemaphore.WaitAsync(ct);
    }

    public void Exit()
    {
        _lockingSemaphore.Release();
    }

    // Immutable.
    public sealed class AsyncLockScope : IDisposable
    {
        private readonly AsyncLock _lockedAsyncLock;

        internal AsyncLockScope(AsyncLock lockedAsyncLock)
        {
            _lockedAsyncLock = lockedAsyncLock;
        }

        public void Dispose()
        {
            _lockedAsyncLock.Exit();
        }
    }
}
