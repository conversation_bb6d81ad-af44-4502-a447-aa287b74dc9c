﻿namespace AT.Core.Domain.Entities;

public class EmployeeWorkingTimeModel
{
    public EmployeeWorkingTimeModelId Id { get; set; }

    public Validity Validity { get; set; }

    public DateOnly? ShiftSystemStart { get; set; }

    public EmployeeContractId EmployeeContractId { get; set; }

    public WorkingTimeModelId WorkingTimeModelId { get; set; }

    public bool DoNotSynchronize { get; set; }

    public virtual EmployeeContract EmployeeContract { get; set; } = null!;

    public virtual WorkingTimeModel WorkingTimeModel { get; set; } = null!;
}
