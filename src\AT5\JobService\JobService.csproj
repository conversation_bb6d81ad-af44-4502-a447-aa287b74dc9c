﻿<Project Sdk="Microsoft.NET.Sdk.Worker">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
		<ImplicitUsings>disable</ImplicitUsings>
		<LangVersion>13.0</LangVersion>
		<Platforms>x64</Platforms>
		<RuntimeIdentifier>win-x64</RuntimeIdentifier>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>

	<ItemGroup>
		<!-- Referenced packages. Package versions are managed centrally in Directory.Packages.props file -->
		<PackageReference Include="Autofac" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" />
		<PackageReference Include="Autofac.Multitenant" />
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" />
		<PackageReference Include="CliFx" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\JobsService\JobsServiceObsolete.csproj" />
		<ProjectReference Include="..\Core\Core.csproj" />
		<ProjectReference Include="..\Infrastructure.Utilities\Infrastructure.Utilities.csproj" />
		<ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
		<ProjectReference Include="..\ServiceDefaults\ServiceDefaults.csproj" />
		<ProjectReference Include="..\UseCases\UseCases.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.local.json" Condition="'$(Configuration)'=='Debug'">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>
