﻿namespace AT.Core.Domain.ApprovalsAggregate;

public class ApprovalType : IEntity
{
    public ApprovalTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public ApprovalKind Kind { get; set; }

    public string? Parameters { get; set; }

    public virtual ICollection<Approval> Approvals { get; set; } = new List<Approval>();

    public virtual ICollection<ApprovalTypeFilter> Filters { get; set; } = new List<ApprovalTypeFilter>();
}
