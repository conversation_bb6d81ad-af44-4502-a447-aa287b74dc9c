namespace AT.Core.Domain.Entities;

using Base;

public class CalculationPhase
{
    public CalculationPhaseId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string Parameters { get; set; } = null!;

    public virtual ICollection<CalculationTypePhase> CalculationTypePhases { get; set; } =
        new List<CalculationTypePhase>();

    public virtual ICollection<RosteringRule> RosteringRules { get; set; } = new List<RosteringRule>();
}
