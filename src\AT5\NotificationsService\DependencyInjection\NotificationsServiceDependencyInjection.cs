﻿namespace AT.NotificationsService.DependencyInjection;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.Database;
using AT.Infrastructure.DependencyInjection;
using AT.Infrastructure.DependencyInjection.Extensions;
using AT.Infrastructure.Jobs.DependencyInjection;
using AT.Infrastructure.Utilities.AzureMonitor;
using AT.Infrastructure.Utilities.DependencyInjection;
using AT.Infrastructure.Utilities.DependencyInjection.Extensions;
using AT.NotificationsService.Emails;
using AT.NotificationsService.Emails.Interfaces;
using AT.NotificationsService.Emails.Mailkit;
using AT.NotificationsService.General;
using AT.NotificationsService.General.Interfaces;
using AT.NotificationsService.General.Job;
using AT.NotificationsService.NotificationsLimiting;
using AT.NotificationsService.NotificationsLimiting.CleanupJob;
using AT.NotificationsService.NotificationsLimiting.Interfaces;
using AT.NotificationsService.PushNotifications;
using AT.NotificationsService.PushNotifications.Firebase;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.NotificationsService.PushNotifications.Tokens;
using AT.Utilities.Exceptions;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Autofac.Multitenant;
using Microsoft.Extensions.DependencyInjection;

internal static class NotificationsServiceDependencyInjection
{
    /// <summary>
    /// MultitenantContainerFactory has already been registered. This method instructs how exactly should the MultitenantContainer be created.
    /// </summary>
    public static MultitenantContainer CreateMultitenantContainer(IContainer rootContainer)
    {
        var strategy = new DummyMultitenantResolutionStrategy();
        var mtc = new MultitenantContainer(strategy, rootContainer);

        // In here, we can register tenant-specific implementations via mtc.ConfigureTenant(tenantId, () => { }),
        // where tenantId can be `new DefaultTenantId().ToString()` to register implementations for the default tenant.

        return mtc;
    }

    /// <summary>
    /// Registrations from builder.Services were already added. Now, add AutoFac registrations to the container.
    /// </summary>
    public static void Populate(ContainerBuilder containerBuilder, IConfiguration configuration)
    {
        PopulateNonAutofac(containerBuilder, configuration);

        containerBuilder.AddCustomMultitenantServiceProviders();

        containerBuilder.AddCurrentTime();

        containerBuilder.AddSimpleJobScheduling();

        // Tenant info
        // TenantIdProvider is configured by AppServiceProvider
        containerBuilder
            .Register(
                (TenantIdProvider tenantIdProvider, IOrganizationInfoQuery orgInfoQuery) =>
                {
                    if (tenantIdProvider.OrganizationId is null)
                    {
                        return OrganizationInfo.DefaultTenantOrg;
                    }

                    return orgInfoQuery.GetOrganizationInfoOrThrow(tenantIdProvider.OrganizationId.Value);
                }
            )
            .InstancePerTenant();
        containerBuilder
            .Register((OrganizationInfo orgInfo) => new FullOrgId(orgInfo.Id, orgInfo.DatabaseName))
            .InstancePerTenant();
        containerBuilder
            .Register((OrganizationInfo orgInfo) => new OrgDbName(orgInfo.DatabaseName))
            .InstancePerTenant(); // Necessary for Organization DbContext

        // Org DB Access.
        containerBuilder.AddOrgDbContextServicesFromOrganizationDbTemplate(configuration);
        containerBuilder.AddRepositories();
        containerBuilder.AddQueries();

        // Notifications Service services

        containerBuilder.RegisterType<SingleOrganizationWorker>().AsSelf().InstancePerTenant();

        // Push notifications
        containerBuilder
            .RegisterType<PushNotificationLogger>()
            .As<IPushNotificationLogger>()
            .As<IMessageLogger<PushNotification, PushNotificationRecipient>>()
            .InstancePerTenant();
        containerBuilder
            .RegisterType<FirebasePushNotificationSender>()
            .As<IPushNotificationSender>()
            .InstancePerTenant();

        containerBuilder
            .RegisterType<PushNotificationService>()
            .As<IMessageService<PushNotification>>()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<PushNotificationDatabaseProvider>()
            .As<IMessageProvider<PushNotification>>()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<MessagesProcessor<PushNotification, PushNotificationRecipient>>()
            .AsSelf()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<
                InstantSendingLoop<
                    PushNotificationConfig,
                    MessagesProcessor<PushNotification, PushNotificationRecipient>
                >
            >()
            .AsSelf()
            .InstancePerTenant();
        containerBuilder
            .RegisterType<ScheduledSendingJob<PushNotification, PushNotificationRecipient>>()
            .AsSelf()
            .InstancePerLifetimeScope();

        // Push notifications cleanup
        containerBuilder
            .RegisterType<PushNotificationTokensCleanupService>()
            .As<IPushNotificationTokensCleanupService>()
            .InstancePerTenant();
        containerBuilder
            .RegisterType<PushNotificationTokenUpdateService>()
            .As<IPushNotificationTokenUpdateService>()
            .InstancePerTenant();

        // Email messages
        containerBuilder
            .RegisterType<EmailMessageLogger>()
            .As<IEmailLogger>()
            .As<IMessageLogger<EmailMessage, EmailMessageRecipient>>()
            .InstancePerTenant();
        containerBuilder.RegisterType<MailkitEmailMessageSender>().As<IEmailMessageSender>().InstancePerTenant();

        containerBuilder
            .RegisterType<EmailMessageService>()
            .As<IMessageService<EmailMessage>>()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<EmailMessageDatabaseProvider>()
            .As<IMessageProvider<EmailMessage>>()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<MessagesProcessor<EmailMessage, EmailMessageRecipient>>()
            .AsSelf()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<InstantSendingLoop<EmailConfig, MessagesProcessor<EmailMessage, EmailMessageRecipient>>>()
            .AsSelf()
            .InstancePerTenant();
        containerBuilder
            .RegisterType<ScheduledSendingJob<EmailMessage, EmailMessageRecipient>>()
            .AsSelf()
            .InstancePerLifetimeScope();

        // Rate-limiting
        containerBuilder.RegisterType<SentNotificationsCache>().AsSelf().InstancePerTenant();
        containerBuilder
            .RegisterType<SentNotificationService>()
            .As<ISentNotificationService>()
            .InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<OrganizationMessageLimitingService>()
            .As<IOrganizationMessageLimitingService>()
            .InstancePerLifetimeScope();
        containerBuilder.RegisterType<SentNotificationsCleanupJob>().AsSelf().InstancePerLifetimeScope();
    }

    private static void PopulateNonAutofac(ContainerBuilder containerBuilder, IConfiguration configuration)
    {
        var serviceCollection = new ServiceCollection();

        serviceCollection.AddHostedService<NotificationsWorker>();

        serviceCollection.AddLogger(configuration);

        serviceCollection.AddAzureMonitor(configuration);
        serviceCollection.AddQuartzCustom();

        serviceCollection.AddMasterDatabaseServices(configuration);

        // Configurations
        serviceCollection.Configure<GeneralConfig>(configuration.GetSection(nameof(GeneralConfig)));
        serviceCollection.Configure<PushNotificationConfig>(configuration.GetSection(nameof(PushNotificationConfig)));
        serviceCollection.Configure<EmailConfig>(configuration.GetSection(nameof(EmailConfig)));
        serviceCollection.Configure<RateLimitingConfig>(configuration.GetSection(nameof(RateLimitingConfig)));

        containerBuilder.Populate(serviceCollection);
    }

    public static IServiceProvider CreateSingleOrganizationServiceProviderForCliFx(
        IReadOnlyList<Type> commandTypes,
        IConfiguration configuration
    )
    {
        // Build standard multitenant service provider.
        var serviceProviderFactory = new CustomMultitenantAutofacServiceProviderFactory(
            CreateMultitenantContainer,
            containerBuilder =>
            {
                Populate(containerBuilder, configuration);

                // Add CLI commands
                foreach (var commandType in commandTypes)
                {
                    // Do not inject required properties of commands. Commands properties are parameters filled by CliFx.
                    // https://autofac.readthedocs.io/en/latest/register/prop-method-injection.html
                    containerBuilder
                        .RegisterType(commandType)
                        .AsSelf()
                        .InstancePerDependency()
                        .ResolveRequiredCliFxParametersWithNull();
                }
            }
        );

        var containerBuilder = serviceProviderFactory.CreateBuilder(new ServiceCollection());
        var rootServiceProvider = serviceProviderFactory.CreateServiceProvider(containerBuilder);

        // Get lifetime scope for the organization specified in configuration.
        var dbName = configuration.GetSection($"{nameof(GeneralConfig)}:{nameof(GeneralConfig.DbName)}").Get<string>()!;
        if (dbName is null)
        {
            throw new InvalidConfigurationFileException("Single DbName has to be specified in order to run commands.");
        }

        var fullOrgIdsQuery = rootServiceProvider.GetRequiredService<IFullOrgIdsQuery>();
        var fullOrgId = fullOrgIdsQuery.GetOrgIds().FirstOrDefault(x => x.DatabaseName == dbName);
        if (fullOrgId is null)
        {
            throw new InvalidConfigurationFileException($"Organization with DbName {dbName} not found.");
        }

        var appServiceProvider = rootServiceProvider.GetRequiredService<AppServiceProvider>();
        var orgLifetimeScope = appServiceProvider.GetTenantLifetimeScope(fullOrgId.Id);

        // Commands are resolved in a scope inside a single organization.
        return new AutofacServiceProvider(orgLifetimeScope.BeginLifetimeScope());
    }
}
