﻿namespace AT.Infrastructure.Jobs.JobService;

using AT.Core.Jobs.JobService.JobWrapper;
using Quartz;

// FUTURE: Remove, it seems that it is not needed.
public record FireTimeParameters(DateTimeOffset FireTimeUtc, DateTimeOffset? NextFireTimeUtc) : IFireTimeParameters
{
    public static FireTimeParameters FromQuartzContext(IJobExecutionContext quartzContext)
    {
        return new FireTimeParameters(quartzContext.FireTimeUtc, quartzContext.NextFireTimeUtc);
    }
}
