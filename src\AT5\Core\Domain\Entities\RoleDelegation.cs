﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class RoleDelegation
{
    public RoleDelegationId Id { get; set; }

    public Validity Validity { get; set; }

    public DateTime Created { get; set; }

    public DateTime? Changed { get; set; }

    public UserId? ChangedByUserId { get; set; }

    public UserId DelegatingEmployeeId { get; set; }

    public RoleDelegationTypeId RoleDelegationTypeId { get; set; }

    public SiteId SiteId { get; set; }

    public bool IncludeSubsites { get; set; }

    public virtual User? ChangedByUser { get; set; }

    public virtual Employee DelegatingEmployee { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual RoleDelegationType RoleDelegationType { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<Employee> DelegatedEmployees { get; set; } = new List<Employee>();

    public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();

    public virtual ICollection<Permission> PermissionsTypes { get; set; } = new List<Permission>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();

    public virtual ICollection<Team> Teams { get; set; } = new List<Team>();
}
