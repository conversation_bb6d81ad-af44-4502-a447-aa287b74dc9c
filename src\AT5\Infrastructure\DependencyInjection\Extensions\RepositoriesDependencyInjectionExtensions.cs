﻿namespace AT.Infrastructure.DependencyInjection.Extensions;

using AT.Core.DataAccess;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.SickNotesAggregate;
using AT.Core.Domain.UserAggregate;
using AT.Infrastructure.DataAccess;
using AT.Infrastructure.Repositories;
using Autofac;
using Autofac.Multitenant;

public static class RepositoriesDependencyInjectionExtensions
{
    public static ContainerBuilder AddRepositories(this ContainerBuilder containerBuilder)
    {
        // Users
        containerBuilder.AddStandardRepositoryWithFactory<IUserRepository, UserRepository>();

        // Notifications
        containerBuilder.AddStandardRepositoryWithFactory<
            IPushNotificationTokenRepository,
            PushNotificationTokenRepository
        >();
        containerBuilder.AddStandardRepositoryWithFactory<IEmailMessageRepository, EmailMessageRepository>();
        containerBuilder.AddStandardRepositoryWithFactory<IPushNotificationRepository, PushNotificationRepository>();
        containerBuilder.AddStandardRepositoryWithFactory<ISentNotificationRepository, SentNotificationRepository>();

        // Sick notes
        containerBuilder.AddStandardRepositoryWithFactory<ISickNoteRepository, SickNoteRepository>();

        return containerBuilder;
    }

    private static void AddStandardRepositoryWithFactory<TRepoInterface, TRepoImplementation>(
        this ContainerBuilder containerBuilder
    )
        where TRepoInterface : class, IDisposable
        where TRepoImplementation : class, TRepoInterface, IRepositoryWithFactoryMethod<TRepoInterface>
    {
        containerBuilder.RegisterType<TRepoImplementation>().As<TRepoInterface>().InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<EfRepositoryFactory<TRepoInterface, TRepoImplementation>>()
            .As<IRepositoryFactory<TRepoInterface>>()
            .InstancePerTenant();
    }
}
