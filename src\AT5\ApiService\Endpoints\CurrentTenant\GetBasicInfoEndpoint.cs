﻿namespace AT.ApiService.Endpoints.CurrentTenant;

using AT.Core.MasterDomain;
using AT.Shared.Models.CurrentTenant;
using FastEndpoints;

public class GetBasicInfoEndpoint(OrganizationInfo _currentOrganizationInfo)
    : EndpointWithoutRequest<CurrentTenantBasicInfoResponse>
{
    public override void Configure()
    {
        Get("/currenttenant/basicinfo");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var response = new CurrentTenantBasicInfoResponse(
            Name: _currentOrganizationInfo.Name,
            HeaderTitle: _currentOrganizationInfo.HeaderTitle,
            HeaderColor: _currentOrganizationInfo.HeaderColor
        );
        await SendAsync(response, cancellation: ct);
    }
}
