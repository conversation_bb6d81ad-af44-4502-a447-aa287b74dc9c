﻿namespace AT.DataStructures.Time;

using System.Runtime.Serialization;

/// <summary>
/// Represents a time interval with a start time and a duration.
/// </summary>
[DataContract]
public readonly record struct TimeInterval
{
    /// <summary>
    /// Gets the start time of the interval.
    /// </summary>
    [DataMember]
    public TimeOnly Start { get; }

    /// <summary>
    /// Gets the duration of the interval.
    /// </summary>
    [DataMember]
    public TimeSpan Duration { get; }

    /// <summary>
    /// Gets the end time of the interval, calculated as the start time plus the duration.
    /// </summary>
    public TimeSpan End => Start.ToTimeSpan() + Duration;

    /// <summary>
    /// Gets a value indicating whether the interval spans past midnight into the next day.
    /// </summary>
    public bool Overnight => End > TimeSpan.FromDays(1);

    /// <summary>
    /// Converts this time interval to a <see cref="DateTimeInterval"/> using a specific date.
    /// </summary>
    /// <param name="date">The date to use as the base for the interval's start time.</param>
    /// <returns>
    /// A <see cref="DateTimeInterval"/> representing the same interval on the specified date.
    /// </returns>
    public DateTimeInterval ToDateTimeInterval(DateOnly date)
    {
        var start = date.ToDateTime(Start);
        return new DateTimeInterval(start, start + Duration);
    }

    /// <summary>
    /// Determines whether the specified <see cref="TimeInterval"/> is entirely contained within this interval.
    /// </summary>
    /// <param name="interval">The interval to check.</param>
    /// <returns>
    /// <c>true</c> if the specified interval's start and end times are within this interval; otherwise, <c>false</c>.
    /// </returns>
    public bool Contains(TimeInterval interval)
    {
        return interval.Start >= Start && interval.End <= End;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="TimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check for overlap.</param>
    /// <returns>
    /// <c>true</c> if the intervals overlap; otherwise, <c>false</c>.
    /// </returns>
    public bool Overlaps(TimeInterval other)
    {
        return Start.ToTimeSpan() < other.End && End > other.Start.ToTimeSpan();
    }
}
