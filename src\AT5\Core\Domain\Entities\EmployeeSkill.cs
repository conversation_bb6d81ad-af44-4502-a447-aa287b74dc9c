﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using EntitySubtypes;

public class EmployeeSkill
{
    public EmployeeSkillId Id { get; set; }

    public SkillId SkillId { get; set; }

    public UserId EmployeeId { get; set; }

    public SkillLevel Level { get; set; }

    public Validity Validity { get; set; }

    public CommunicationChannel? CommunicationType { get; set; }

    public Language? Language { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Skill Skill { get; set; } = null!;
}
