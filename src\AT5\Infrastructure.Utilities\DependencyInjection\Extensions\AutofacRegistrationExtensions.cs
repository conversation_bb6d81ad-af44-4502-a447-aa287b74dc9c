﻿namespace AT.Infrastructure.Utilities.DependencyInjection.Extensions;

using System.Reflection;
using Autofac;
using Autofac.Builder;
using Autofac.Core;
using CliFx.Attributes;

public static class AutofacRegistrationExtensions
{
    /// <summary>
    /// Sets required properties that are CliFx command parameters to be initialized to null by Autofac.
    /// Autofac by default automatically resolved all required properties, we want command parameters to be set by CliFx, not Autofac,
    /// so we tell Autofac to just set null.
    /// </summary>
    public static IRegistrationBuilder<
        TLimit,
        TConcreteActivatorData,
        SingleRegistrationStyle
    > ResolveRequiredCliFxParametersWithNull<TLimit, TConcreteActivatorData>(
        this IRegistrationBuilder<TLimit, TConcreteActivatorData, SingleRegistrationStyle> registration
    )
        where TConcreteActivatorData : ReflectionActivatorData
    {
        var propertiesSetToNull = new List<Parameter>();
        var implementationType = registration.ActivatorData.ImplementationType;

        foreach (var propInfo in implementationType.GetRuntimeProperties())
        {
            if (!propInfo.HasRequiredMemberAttribute())
            {
                continue;
            }

            var hasOptionAttribute = propInfo.GetCustomAttribute<CommandOptionAttribute>() is not null;
            var hasParameterAttribute = propInfo.GetCustomAttribute<CommandParameterAttribute>() is not null;

            if (hasOptionAttribute || hasParameterAttribute)
            {
                // Required properties with CommandOptionAttribute or CommandParameterAttribute are automatically required by CliFx.
                propertiesSetToNull.Add(new NamedPropertyParameter(propInfo.Name, null));
            }
            else
            {
                throw new InvalidOperationException(
                    $"Required command property {propInfo.Name} on type {implementationType.Name} has to have a CommandOption or CommandParameters with IsRequired set to true."
                );
            }
        }

        return registration.WithProperties(propertiesSetToNull);
    }

    /// <summary>
    /// Based on Autofac ReflectionExtensions HasRequiredMemberAttribute.
    /// Checks if a provided member has a <c>RequiredMemberAttribute</c>.
    /// </summary>
    /// <remarks>
    /// <para>
    /// On NET7+ this would <em>typically</em> be the framework supplied <c>RequiredMemberAttribute</c>, <em>but</em> internally the compiler
    /// <em>only</em> requires an attribute with that specific type <em>name</em>, not that specific type <em>reference</em>.
    /// </para>
    /// <para>
    /// This could very well be an internally defined custom polyfill attribute using that type name, so this
    /// check is done <em>only</em> via type <em>name</em>, not reference.
    /// </para>
    /// </remarks>
    /// <param name="memberInfo">Member to check.</param>
    /// <returns>
    /// <see langword="true" /> if <paramref name="memberInfo"/> carries a <see cref="MemberInfo.CustomAttributes">CustomAttributeData</see> with
    /// a type <em>name</em> of <c>System.Runtime.CompilerServices.RequiredAttribute</c>; <see langword="false" /> otherwise.
    /// </returns>
    public static bool HasRequiredMemberAttribute(this MemberInfo memberInfo)
    {
        return memberInfo.CustomAttributes.Any(cad =>
            cad.AttributeType.FullName == "System.Runtime.CompilerServices.RequiredMemberAttribute"
        );
    }
}
