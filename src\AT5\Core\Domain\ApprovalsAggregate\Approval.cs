﻿namespace AT.Core.Domain.ApprovalsAggregate;

using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;

public class Approval : IAggregateRoot
{
    public ApprovalId Id { get; set; }

    public ApprovalTypeId ApprovalTypeId { get; set; }

    public RosterItemId? RosterItemId { get; set; }

    public DateTime LastChanged { get; set; }

    public RequestStatus Status { get; set; }

    public UserId? LastChangedById { get; set; }

    public string? LastNote { get; set; }

    public UserId EmployeeId { get; set; }

    public virtual ApprovalType ApprovalType { get; set; } = null!;

    public virtual RosterItem? RosterItem { get; set; }

    public virtual User? LastChangedBy { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual ICollection<ApprovalHistory> ApprovalHistories { get; set; } = new List<ApprovalHistory>();
}
