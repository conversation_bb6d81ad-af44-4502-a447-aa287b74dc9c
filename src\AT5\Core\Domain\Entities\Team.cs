﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using AT.Core.Domain.UserAggregate;

public class Team
{
    public TeamId Id { get; set; }

    public SiteId SiteId { get; set; }

    public string? CcId { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool VisibleInRoster { get; set; }

    public bool VisibelForPlanning { get; set; }

    public bool CheckedForPlanning { get; set; }

    public int Rank { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public bool GroupInRoster { get; set; }

    public bool HideInReport { get; set; }

    public Validity Validity { get; set; }

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    public virtual ICollection<RoleDelegation> ExplicitlyGovernedByRoleDelegations { get; set; } =
        new List<RoleDelegation>();

    public virtual ICollection<RequestLimit> RequestLimits { get; set; } = new List<RequestLimit>();

    public virtual ICollection<Requirement> RequirementTeamTeams { get; set; } = new List<Requirement>();
}
