﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;

public class Position
{
    public PositionId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Abbreviation { get; set; }

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public int? ExternalId { get; set; }

    public string? Code { get; set; }

    public bool Disabled { get; set; }

    public bool IsGlobal { get; set; }

    public bool DoNotSynchronize { get; set; }

    public virtual ICollection<EmployeePosition> EmployeePositions { get; set; } = new List<EmployeePosition>();

    public virtual ICollection<Role> Roles { get; set; } = new List<Role>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
