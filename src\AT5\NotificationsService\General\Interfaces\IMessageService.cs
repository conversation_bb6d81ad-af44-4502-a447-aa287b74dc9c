﻿namespace AT.NotificationsService.General.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates;

public interface IMessageService<TMessageType>
    where TMessageType : INotificationMessage
{
    Task<SendMessageResult<TMessageType>> SendMessagesAsync(
        IReadOnlyCollection<TMessageType> messages,
        CancellationToken cancellationToken = default
    );
}

/// <param name="FailedMessages">The messages that were not sent to <b>any</b> recipient.</param>
/// <param name="PartiallyFailedMessages">The messages where <b>some</b> (but not all) recipients did not receive the message.</param>
public record SendMessageResult<TMessageType>(
    IReadOnlyCollection<TMessageType> FailedMessages,
    IReadOnlyCollection<TMessageType> PartiallyFailedMessages
)
    where TMessageType : INotificationMessage
{
    public bool Success => FailedMessages.Count == 0 && PartiallyFailedMessages.Count == 0;

    public bool PartialSuccess => FailedMessages.Count == 0 && PartiallyFailedMessages.Count > 0;
}
