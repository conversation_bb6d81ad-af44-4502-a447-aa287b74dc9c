﻿namespace AT.Primitives.Enums;

public enum NeedType
{
    Normal = 0,
    Break = 1,
    Scheduling = 2,

    // ForScheduling = 3, // Need will be used in scheduling, not in planning - Obsolete. Historic usages in at_Albert, at_Innogy were removed from prod dbs
    TeamMeeting = 4, // Special need for TeamMeetingRosteringRule
    Additional = 5,
    AdditionalWithBreaks = 6,
    AdditionalByInterval = 7,
    AdditionalByIntervalWithBreaks = 8,

    // EmployeeCountGroups = 9, // Obsolete, no usages at all
    OpeningHours = 10,

    // SchedulingByInterval = 11, // Obsolete, replaced by NewScheduling. Historic usages in at_Uniqa were removed from prod db
    /// <summary>
    /// Like AdditionalByInterval, but exclusive (one shift can satisfy only one need at one time).
    /// Only merge-able (restrict to same set of roster items) needs at one time is supported.
    /// </summary>
    ByInterval = 12,

    /// <summary>
    /// Like ByInterval, but breaks dont satisfy the need.
    /// </summary>
    ByIntervalWithBreaks = 13,
    ShiftPart = 14,
    ShiftPartWorkHours = 15,
    OperatingHours = 16,
    ShiftPartSplittable = 17,
    OperatingHoursNight = 18,
    NewScheduling = 19 // used only in NewSchedulingRosteringRule
}
