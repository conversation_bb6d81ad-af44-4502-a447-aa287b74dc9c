﻿namespace AT.NotificationsService.NotificationsLimiting;

using AT.Core.DataAccess;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.Specifications;
using AT.Primitives.Enums;
using AT.Utilities.Task;

public class SentNotificationsCache
{
    private readonly AsyncLock _asyncLock = new();

    private List<SentNotification> _sentEmailMessages = [];

    private volatile bool _cacheInvalidated = true;

    // FUTURE: Redo without repo factory as a parameter.
    public async Task<IReadOnlyCollection<SentNotification>> GetSentEmailMessagesAsync(
        IRepositoryFactory<ISentNotificationRepository> sentNotificationRepoFactory,
        CancellationToken cancellationToken = default
    )
    {
        await EnsureDataAreFreshAsync(sentNotificationRepoFactory, cancellationToken);

        return _sentEmailMessages;
    }

    public async Task InvalidateAsync(CancellationToken cancellationToken = default)
    {
        using (await _asyncLock.EnterScope(cancellationToken))
        {
            _cacheInvalidated = true;
        }
    }

    private async Task EnsureDataAreFreshAsync(
        IRepositoryFactory<ISentNotificationRepository> sentNotificationRepoFactory,
        CancellationToken cancellationToken = default
    )
    {
        using (await _asyncLock.EnterScope(cancellationToken))
        {
            if (!_cacheInvalidated)
            {
                return;
            }

            _sentEmailMessages = [];

            using var sentNotificationRepository = sentNotificationRepoFactory.Create();
            var sentNotifications = await sentNotificationRepository.ListAsync(
                new SentNotificationsSpec(),
                cancellationToken
            );

            foreach (var notification in sentNotifications)
            {
                switch (notification.NotificationType)
                {
                    case SentNotificationType.EmailMessage:
                        _sentEmailMessages.Add(notification);
                        break;
                }
            }

            _cacheInvalidated = false;
        }
    }
}
