using AT.ApiClient;
using AT.Web.Client;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;

var configuration = new ConfigurationBuilder().AddJsonFile("appsettings.json").Build();

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

var httpClient = new HttpClient() { BaseAddress = new Uri(configuration.GetConnectionString("ApiService")!), };
httpClient.DefaultRequestHeaders.Add("tenant-id", "20"); // FUTURE: Load from cookies.
httpClient.DefaultRequestHeaders.Add("user-id", "2"); // FUTURE: Load from cookies.

builder.Services.AddSingleton(new ApiClient(httpClient));

await builder.Build().RunAsync();
