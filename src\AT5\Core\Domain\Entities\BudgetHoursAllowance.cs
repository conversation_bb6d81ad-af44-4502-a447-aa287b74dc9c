﻿namespace AT.Core.Domain.Entities;

public class BudgetHoursAllowance
{
    public BudgetHoursAllowanceId Id { get; set; }

    public BudgetActivityId BudgetActivityId { get; set; }

    public SiteId SiteId { get; set; }

    public Validity Validity { get; set; }

    public int Variant { get; set; }

    public double Budget { get; set; }

    public double Value { get; set; }

    public virtual BudgetActivity BudgetActivity { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
