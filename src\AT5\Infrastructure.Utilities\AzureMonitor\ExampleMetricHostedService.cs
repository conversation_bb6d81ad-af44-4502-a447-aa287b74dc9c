﻿namespace AT.Infrastructure.Utilities.AzureMonitor;

using System;
using System.Diagnostics.Metrics;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

public class ExampleMetricHostedService(
    IMeterFactory _meterFactory,
    IOptions<AzureMonitorOptions> _options,
    ILogger<ExampleMetricHostedService> _logger
) : IHostedService, IDisposable
{
    private readonly AzureMonitorOptions _options = _options.Value;
    private Timer? _timer;
    private Counter<int>? _exampleMetricCounter;

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("ExampleMetric running.");

        if (string.IsNullOrEmpty(_options.ConnectionString))
        {
            return Task.CompletedTask;
        }

        var meter = _meterFactory.Create("Aristotelos.ExampleMetric");
        _exampleMetricCounter = meter.CreateCounter<int>(
            "aristotelos.example_metric.count",
            "count",
            "Tracks the number of calls."
        );
        _timer = new Timer(Count, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));

        return Task.CompletedTask;
    }

    private void Count(object? state)
    {
        _logger.LogTrace("ExampleMetric is sending count metric.");
        _exampleMetricCounter?.Add(1);
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("ExampleMetric is stoppping.");

        _timer?.Change(Timeout.Infinite, 0);

        return Task.CompletedTask;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposing && _timer is not null)
        {
            _timer.Dispose();
            _timer = null;
        }
    }
}
