﻿namespace AT.ApiService.DependencyInjection;

using AT.ApiService.Authentication;
using AT.ApiService.Middleware;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.Database;
using AT.Infrastructure.DependencyInjection;
using AT.Infrastructure.DependencyInjection.Extensions;
using AT.Primitives.Identifiers;
using Autofac;
using Autofac.Multitenant;
using FastEndpoints;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;

internal static class ApiServiceDependencyInjection
{
    private const string allowAnyLocalhostCorsPolicy = "allowAnyOriginCorsPolicy";

    /// <summary>
    /// MultitenantContainerFactory has already been registered. This method instructs how exactly should the MultitenantContainer be created.
    /// </summary>
    public static MultitenantContainer CreateMultitenantContainer(IContainer rootContainer)
    {
        var strategy = new UserClaimsTenantIdentificationStrategy(rootContainer.Resolve<IHttpContextAccessor>());
        var mtc = new MultitenantContainer(strategy, rootContainer);

        // In here, we can register tenant-specific implementations via mtc.ConfigureTenant(tenantId, () => { }),
        // where tenantId can be `new DefaultTenantId().ToString()` to register implementations for the default tenant.

        // Must be here, the default tenant is otherwise not created through AppServiceProvider and thus is not correctly initialized.
        mtc.ConfigureTenant(null, AppServiceProvider.ConfigureDefaultTenant);

        return mtc;
    }

    /// <summary>
    /// Registrations from builder.Services were already added. Now, add AutoFac registrations to the container.
    /// </summary>
    public static void PopulateWithAutoFacRegistrations(ContainerBuilder containerBuilder, IConfiguration configuration)
    {
        // Configures TenantIdProvider for each tenant inside AppServiceProvider.
        containerBuilder.AddCustomMultitenantServiceProviders();

        containerBuilder.AddCurrentTime();

        containerBuilder.Register((x) => AppInstanceId.CreateRandomAppId()).AsSelf().SingleInstance();

        // This explicit registration is needed because TenantAwareScopeFactory uses it.
        containerBuilder
            .RegisterType<UserClaimsTenantIdentificationStrategy>()
            .As<ITenantIdentificationStrategy>()
            .SingleInstance();

        // When the call to app.UseFastEndpoints() is reached, it tests that every single endpoint can be instantiated with the dependencies it injects.
        // While doing so, it creates a scope by calling serviceScopeFactory.CreateScope(). The problem is, when it proceeds to resolve dependencies
        // from it that are registered as InstancePerTenant, the resolve process fails (even if tenant identification strategy returns a dummyTenantId
        // and returns true) because tenant scopes are "tagged scope" (i.e., the scope must be created with a tenantScope tag).
        // Therefore, we use TenantAwareScopeFactory which always gets or creates a tenant scope from MultitenantContainer and creates
        // a disposable tenant-specific subscope, which allows us to resolve dependencies registered as InstancePerTenant anywhere.
        containerBuilder.RegisterType<TenantAwareScopeFactory>().As<IServiceScopeFactory>().SingleInstance();

        // Custom middlewares
        containerBuilder.RegisterType<HeaderIdAuthenticationMiddleware>().AsSelf();
        containerBuilder.RegisterType<MultitenantRequestServicesMiddleware>().AsSelf();
        containerBuilder.RegisterType<OrganizationLoggingScopeMiddleware>().AsSelf();

        // Tenant info
        containerBuilder
            .Register(
                (TenantIdProvider tenantIdProvider, IOrganizationInfoQuery orgInfoQuery) =>
                {
                    if (tenantIdProvider.OrganizationId is null)
                    {
                        return OrganizationInfo.DefaultTenantOrg;
                    }

                    return orgInfoQuery.GetOrganizationInfoOrThrow(tenantIdProvider.OrganizationId.Value);
                }
            )
            .InstancePerTenant();
        containerBuilder
            .Register((OrganizationInfo orgInfo) => new FullOrgId(orgInfo.Id, orgInfo.DatabaseName))
            .InstancePerTenant();
        containerBuilder
            .Register((OrganizationInfo orgInfo) => new OrgDbName(orgInfo.DatabaseName))
            .InstancePerTenant(); // Necessary for Organization DbContext

        containerBuilder.AddEventsServices();

        containerBuilder.AddApiServiceDomainServices();

        // DB Access.
        containerBuilder.AddOrgDbContextServicesFromOrganizationDbTemplate(configuration);
        containerBuilder.AddRepositories();
        containerBuilder.AddQueries();
    }

    public static IServiceCollection AddNonAutofacApiServiceServices(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        // Needed by MultitenantRequestServicesMiddleware at least.
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

        services.AddMasterDatabaseServices(configuration);

        // Also adds AspNetCore scopes, see https://github.com/dotnet/aspnetcore/issues/31028
        services.AddLogger(configuration);

        // Cors policy for development environment.
        // https://learn.microsoft.com/en-us/aspnet/core/security/cors?view=aspnetcore-9.0
        services.AddCors(options =>
        {
            options.AddPolicy(
                name: allowAnyLocalhostCorsPolicy,
                policy =>
                {
                    policy.AllowAnyOrigin();
                    policy.WithHeaders("tenant-id", "user-id");
                }
            );
        });

        // Authorization doesn't work without authentication.
        // https://learn.microsoft.com/en-us/aspnet/core/security/authentication/?view=aspnetcore-9.0
        // https://gist.github.com/dj-nitehawk/d2f585747c0711f92ab7c7923f670e29
        services
            .AddAuthentication()
            // For authentication there must be a challenge specified. For now we want to just return 401.
            //.AddCookie()
            .AddBearerToken();
        // Require authorization for endpoints (if not specified otherwise).

        services
            .AddAuthorizationBuilder()
            .SetDefaultPolicy(
                new AuthorizationPolicyBuilder()
                    // FUTURE: We may want to require the user to be authenticated?
                    //.RequireAuthenticatedUser()
                    // Require claim with TenantId and UserId.
                    .RequireClaim(AristoTelosClaimTypes.TenantId)
                    .RequireClaim(AristoTelosClaimTypes.UserId)
                    .Build()
            );

        services.AddProblemDetails();

        services.AddFastEndpoints();

        services.AddApiServiceApiClientGenerationServices();

        services.AddParsers(configuration);

        return services;
    }

    public static async Task ConfigureApiServiceAsync(this WebApplication app)
    {
        // Auth middlewares.
        app.UseMiddleware<HeaderIdAuthenticationMiddleware>();

        // Adds middleware that identifies current tenant and selects its tenant-specific DI container from MultitenantContainer.
        app.UseMiddleware<MultitenantRequestServicesMiddleware>();

        // Use Cors policy for development environment.
        if (app.Environment.IsDevelopment())
        {
            app.UseCors(allowAnyLocalhostCorsPolicy);
        }
        else
        {
            app.UseExceptionHandler("/Error", createScopeForErrors: true);
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }

        app.UseMiddleware<OrganizationLoggingScopeMiddleware>();

        //app.UseAuthentication();
        app.UseAuthorization();

        app.UseFastEndpoints();

        // Configure the HTTP request pipeline.
        app.UseExceptionHandler();

        await app.ConfigureApiServiceForApiClientGenerationAsync();
    }
}
