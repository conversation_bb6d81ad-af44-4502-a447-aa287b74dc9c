﻿namespace AT.DataStructures.Time;

/// <summary>
/// A builder for creating and configuring instances of <see cref="TimeValidity"/>.
/// </summary>
public class TimeValidityBuilder
{
    /// <summary>
    /// Gets the start date and time of the validity interval being built.
    /// </summary>
    public DateTime? Start { get; private set; }

    /// <summary>
    /// Gets the end date and time of the validity interval being built.
    /// </summary>
    public DateTime? End { get; private set; }

    /// <summary>
    /// Gets a value indicating whether the interval being built is marked as invalid.
    /// </summary>
    public bool IsInvalid { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="TimeValidityBuilder"/> class with default values.
    /// </summary>
    public TimeValidityBuilder()
        : this(new TimeValidity()) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="TimeValidityBuilder"/> class using an existing <see cref="TimeValidity"/> instance.
    /// </summary>
    /// <param name="validity">The <see cref="TimeValidity"/> instance to initialize the builder with.</param>
    public TimeValidityBuilder(TimeValidity validity)
    {
        Start = validity.Start;
        End = validity.End;
        IsInvalid = validity.IsInvalid;
    }

    /// <summary>
    /// Sets the start date and time for the interval being built.
    /// </summary>
    /// <param name="dateTime">The start date and time to set. Can be <c>null</c> for an unlimited start.</param>
    /// <returns>The current instance of the <see cref="TimeValidityBuilder"/> for method chaining.</returns>
    public TimeValidityBuilder WithStart(DateTime? dateTime)
    {
        Start = dateTime;
        return this;
    }

    /// <summary>
    /// Removes the start date and time, making the interval's start unlimited.
    /// </summary>
    /// <returns>The current instance of the <see cref="TimeValidityBuilder"/> for method chaining.</returns>
    public TimeValidityBuilder WithUnlimitedStart()
    {
        return WithStart(null);
    }

    /// <summary>
    /// Sets the end date and time for the interval being built.
    /// </summary>
    /// <param name="dateTime">The end date and time to set. Can be <c>null</c> for an unlimited end.</param>
    /// <returns>The current instance of the <see cref="TimeValidityBuilder"/> for method chaining.</returns>
    public TimeValidityBuilder WithEnd(DateTime? dateTime)
    {
        End = dateTime;
        return this;
    }

    /// <summary>
    /// Removes the end date and time, making the interval's end unlimited.
    /// </summary>
    /// <returns>The current instance of the <see cref="TimeValidityBuilder"/> for method chaining.</returns>
    public TimeValidityBuilder WithUnlimitedEnd()
    {
        return WithEnd(null);
    }

    /// <summary>
    /// Marks the validity being built as valid.
    /// </summary>
    /// <returns>The current instance of the <see cref="TimeValidityBuilder"/> for method chaining.</returns>
    public TimeValidityBuilder AsValid()
    {
        IsInvalid = false;
        return this;
    }

    /// <summary>
    /// Marks the validity being built as invalid.
    /// </summary>
    /// <returns>The current instance of the <see cref="TimeValidityBuilder"/> for method chaining.</returns>
    public TimeValidityBuilder AsInvalid()
    {
        IsInvalid = true;
        return this;
    }

    /// <summary>
    /// Builds and returns a new <see cref="TimeValidity"/> instance based on the current configuration.
    /// </summary>
    /// <returns>A new <see cref="TimeValidity"/> instance.</returns>
    public TimeValidity Build()
    {
        return new TimeValidity(Start, End, IsInvalid);
    }
}
