﻿namespace AT.Infrastructure.DataAccess;

using System;
using System.Linq;
using AT.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

public class EfDataSource<TEntity>(OrganizationDbContextReadOnly _dbContext) : IDataSource<TEntity>
    where TEntity : class
{
    private readonly Lazy<DbSet<TEntity>> _dataLazy = new(_dbContext.Set<TEntity>, LazyThreadSafetyMode.None);

    public IQueryable<TEntity> Data => _dataLazy.Value.AsQueryable().AsNoTracking();
}
