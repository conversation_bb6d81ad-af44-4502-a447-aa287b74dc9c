﻿namespace AT.Core.Domain.EmployeeRosterAggregate.Queries;

public interface IEmployeeRosterFinishQuery
{
    Task<List<EmployeeRosterFinishInfo>> GetEmployeeRosterFinishesAsync(
        UserId employeeId,
        CancellationToken cancellationToken = default
    );

    Task<List<EmployeeRosterFinishInfo>> GetEmployeeRosterFinishesAsync(
        IReadOnlyCollection<UserId> employeeIds,
        CancellationToken cancellationToken = default
    );
}
