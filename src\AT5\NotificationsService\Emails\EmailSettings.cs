﻿namespace AT.NotificationsService.Emails;

public class EmailSettings
{
    public string? FromName { get; set; }

    public required string FromAddress { get; set; }

    public required string Host { get; set; }

    public int Port { get; set; }

    public string? Username { get; set; }

    public string? Password { get; set; }

    public bool IgnoreServerCertificationValidation { get; set; } = false;

    public bool EnableSsl { get; set; } = true;
}
