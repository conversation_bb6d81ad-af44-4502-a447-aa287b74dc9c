﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Entities;
using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.SickNotesAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="RosterItem"/> in the database.
/// </summary>
internal static class RosterItemGroupModelBuilder
{
    public static void BuildRosterItemGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Change>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Changes");
            entity.HasKey(e => e.Id);
            entity.HasIndex(
                context.AdditionalIndexCollection,
                "ALTER INDEX [PK_Changes] ON [dbo].[Changes] SET (STATISTICS_NORECOMPUTE = ON)"
            );

            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Type).HasMaxLength(64).HasColumnOrder(iota);
            entity.Property(e => e.TypeData).HasMaxLength(1024).HasColumnOrder(iota);

            entity.Property(e => e.ChangeLogId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.ChangeLogId, "IX_FK_ChangeLogChange")
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.RosterItemId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.RosterItemId,
                        e.AffectedInterval.Start,
                        e.AffectedInterval.End
                    },
                    "IX_FK_RosterItemChange"
                )
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.ChangeRequestId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.ChangeRequestId, "IX_FK_ChangeRequestChange")
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.CalculationId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.CalculationId, "IX_FK_ChangeCalculation")
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.HasDateTimeInterval(e => e.AffectedInterval, iota);

            entity.Property(e => e.NewEmployeeId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.NewEmployeeId,
                        e.AffectedInterval.Start,
                        e.AffectedInterval.End
                    },
                    "IX_FK_RosterItemChangeNewEmployee"
                )
                .IncludeProperties(e => new { e.ChangeLogId, e.RosterItemId })
                .WithSettings(new { FILLFACTOR = 90, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.OldEmployeeId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.OldEmployeeId,
                        e.AffectedInterval.Start,
                        e.AffectedInterval.End
                    },
                    "IX_FK_RosterItemChangeOldEmployee"
                )
                .IncludeProperties(e => new { e.ChangeLogId, e.RosterItemId })
                .WithSettings(new { FILLFACTOR = 90, STATISTICS_NORECOMPUTE = "ON" });

            entity
                .HasOne(d => d.Calculation)
                .WithMany(p => p.Changes)
                .HasForeignKey(d => d.CalculationId)
                .HasConstraintName("FK_ChangeCalculation");

            entity
                .HasOne(d => d.ChangeLog)
                .WithMany(p => p.Changes)
                .HasForeignKey(d => d.ChangeLogId)
                .HasConstraintName("FK_ChangeLogChange");

            entity
                .HasOne(d => d.ChangeRequest)
                .WithMany(p => p.Changes)
                .HasForeignKey(d => d.ChangeRequestId)
                .HasConstraintName("FK_ChangeRequestChange");

            entity
                .HasOne(d => d.NewEmployee)
                .WithMany(p => p.ChangeNewEmployees)
                .HasForeignKey(d => d.NewEmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemChangeNewEmployee");

            entity
                .HasOne(d => d.OldEmployee)
                .WithMany(p => p.ChangeOldEmployees)
                .HasForeignKey(d => d.OldEmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemChangeOldEmployee");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.Changes)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemChange");
        });

        modelBuilder.Entity<ChangeLog>(entity =>
        {
            entity.ToTable("ChangeLogs");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.UserId, "IX_FK_ChangeLogUser");

            entity.Property(e => e.Timestamp).IsStoredAsDateTime();

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.ChangeLogs)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ChangeLogUser");
        });

        modelBuilder.Entity<Meeting>(entity =>
        {
            entity.ToTable("Meetings");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.AppointmentId).HasMaxLength(512);
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<ParallelPart>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ParallelParts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity.Property(e => e.RosterItemPartTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RosterItemPartTypeId, "IX_FK_ParallelPartRosterItemPartType");

            entity.Property(e => e.RosterItemId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.RosterItemId, "IX_FK_ParallelPartRosterItem")
                .IncludeProperties(e => new
                {
                    e.Interval.Start,
                    e.Interval.End,
                    e.RosterItemPartTypeId
                })
                .WithSettings(new { FILLFACTOR = 80 });

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.ParallelParts)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ParallelPartRosterItem");

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.ParallelParts)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ParallelPartRosterItemPartType");

            entity.HasIndex(
                context.AdditionalIndexCollection,
                e => new
                {
                    e.Interval.Start,
                    e.Interval.End,
                    e.RosterItemPartTypeId
                },
                "IX_NC_ParallelParts_Fetch1"
            );
        });

        modelBuilder.Entity<Part>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Parts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity.Property(e => e.RosterItemPartTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RosterItemPartTypeId, "IX_FK_PartRosterItemPartType");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new { e.Interval.Start, e.Interval.End },
                    "Interval_StartEnd"
                )
                .WithSettings(new { FILLFACTOR = 80 });

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.Parts)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PartRosterItemPartType");
        });

        modelBuilder.Entity<ReservationPart>(entity =>
        {
            entity.ToTable("Parts_ReservationPart");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.ReservationId).HasColumnOrder(0);
            entity.Property(e => e.Id).HasColumnOrder(1);

            entity.HasIndex(e => e.ReservationId, "IX_FK_ReservationReservationPart");

            entity
                .HasOne(d => d.Part)
                .WithOne()
                .HasForeignKey<ReservationPart>(d => d.Id)
                .HasConstraintName("FK_ReservationPart_inherits_Part");

            entity
                .HasOne(d => d.Reservation)
                .WithMany(p => p.ReservationParts)
                .HasForeignKey(d => d.ReservationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReservationReservationPart");
        });

        modelBuilder.Entity<Reservation>(entity =>
        {
            entity.ToTable("Reservations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeReservation");

            entity.HasIndex(e => e.RequirementId, "IX_FK_ReservationRequirement");

            entity.HasIndex(e => e.ProcessedById, "IX_FK_UserReservation");

            entity.HasIndex(e => e.CreatedById, "IX_FK_UserReservationCreated");

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Date).IsStoredAsDateTime();
            entity.Property(e => e.Processed).IsStoredAsDateTime();

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.ReservationCreatedBies)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserReservationCreated");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.Reservations)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeReservation");

            entity
                .HasOne(d => d.ProcessedBy)
                .WithMany(p => p.ReservationProcessedBies)
                .HasForeignKey(d => d.ProcessedById)
                .HasConstraintName("FK_UserReservation");

            entity
                .HasOne(d => d.Requirement)
                .WithMany(p => p.Reservations)
                .HasForeignKey(d => d.RequirementId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ReservationRequirement");
        });

        modelBuilder.Entity<RosterItem>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RosterItems");
            entity.HasKey(e => e.Id);
            entity.HasIndex(
                context.AdditionalIndexCollection,
                "ALTER INDEX [PK_RosterItems] ON [dbo].[RosterItems] REBUILD WITH(FILLFACTOR = 95, STATISTICS_NORECOMPUTE = ON)"
            );
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.RosterId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.RosterId, "IX_FK_RosterRosterItem")
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.EmployeeId, "IX_FK_EmployeeRosterItem")
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.ShiftId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.ShiftId, "IX_FK_ShiftTemplateRosterItem")
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.Deleted).HasColumnOrder(iota);
            entity.Property(e => e.Created).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.CreatedById).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.CreatedById, "IX_FK_RosterItemCreator")
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.LocationId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.LocationId, "IX_FK_LocationRosterItem")
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.SiteId, "IX_FK_SiteRosterItem")
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.Type).HasColumnOrder(iota);

            entity.HasDateTimeInterval(e => e.TotalInterval, iota);

            entity.Property(e => e.LastChangeRequestId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => e.LastChangeRequestId,
                    "IX_FK_RosterItemLastChangeRequest"
                )
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.LastChangeLogId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.LastChangeLogId, "IX_FK_RosterItemLastChangeLog")
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.SickNoteId).HasColumnName("SickNote_Id").HasColumnOrder(iota);
            entity.HasIndex(e => e.SickNoteId, "IX_FK_SickNoteRosterItem");

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.CreatedRosterItems)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemCreator");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeRosterItem");

            entity
                .HasOne(d => d.LastChangeLog)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.LastChangeLogId)
                .HasConstraintName("FK_RosterItemLastChangeLog");

            entity
                .HasOne(d => d.LastChangeRequest)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.LastChangeRequestId)
                .HasConstraintName("FK_RosterItemLastChangeRequest");

            entity
                .HasOne(d => d.Location)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.LocationId)
                .HasConstraintName("FK_LocationRosterItem");

            entity
                .HasOne(d => d.Roster)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.RosterId)
                .HasConstraintName("FK_RosterRosterItem");

            entity
                .HasOne(d => d.Shift)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.ShiftId)
                .HasConstraintName("FK_ShiftTemplateRosterItem");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.RosterItems)
                .HasForeignKey(d => d.SiteId)
                .HasConstraintName("FK_SiteRosterItem");

            entity
                .HasOne(d => d.SickNote)
                .WithOne(d => d.RosterItem)
                .HasForeignKey<RosterItem>(d => d.SickNoteId)
                .HasConstraintName("FK_SickNoteRosterItem")
                .IsRequired(false);

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.RosterId,
                        e.Deleted,
                        e.EmployeeId,
                        e.TotalInterval.Start,
                        e.TotalInterval.End,
                        e.SiteId
                    },
                    "IX_NC_RosterItems_Fetch"
                )
                .IncludeProperties(e => new { e.Created, e.Type })
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.EmployeeId,
                        e.RosterId,
                        e.TotalInterval.Start,
                        e.TotalInterval.End,
                        e.LastChangeRequestId
                    },
                    "NCI_EmployeeId_RosterId_TotalInterval_Start_TotalInterval_End_INCL"
                )
                .IncludeProperties(e => e.Deleted)
                .WithSettings(new { FILLFACTOR = "80", STATISTICS_NORECOMPUTE = "ON" });

            entity
                .HasIndex(context.AdditionalIndexCollection, e => new { e.Deleted, e.Id }, "NC_Deleted_INCL")
                .IncludeProperties(e => new
                {
                    e.RosterId,
                    e.EmployeeId,
                    e.TotalInterval.Start,
                    e.TotalInterval.End
                })
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.TotalInterval.Start,
                        e.Deleted,
                        e.RosterId
                    },
                    "NC_TotalInterval_Start_RosterID_Deleted_INCL"
                )
                .IncludeProperties(e => new
                {
                    e.EmployeeId,
                    e.SiteId,
                    e.TotalInterval.End
                })
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new { e.Id, e.Deleted },
                    "NonClusteredIndex-20210906-060448"
                )
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });
        });

        modelBuilder.Entity<RosterItemPart>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RosterItemParts");
            entity.HasKey(e => e.Id);
            entity.HasIndex(
                context.AdditionalIndexCollection,
                "ALTER INDEX [PK_RosterItemParts] ON [dbo].[RosterItemParts] SET (STATISTICS_NORECOMPUTE = ON)"
            );
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity.Property(e => e.RosterItemPartTypeId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => e.RosterItemPartTypeId,
                    "IX_FK_RosterItemPartRosterItemPartType_SMT_JR"
                )
                .IncludeProperties(e => new
                {
                    e.Interval.End,
                    e.Interval.Start,
                    e.RosterItemId
                })
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.RosterItemId).HasColumnOrder(iota);

            entity.Property(e => e.MeetingId).HasColumnOrder(iota);
            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.MeetingId, "IX_FK_MeetingRosterItemPart")
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });

            entity.Property(e => e.Seconds).HasColumnOrder(iota);
            entity.Property(e => e.Order).HasColumnOrder(iota);
            entity.Property(e => e.Productive).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Meeting)
                .WithMany(p => p.RosterItemParts)
                .HasForeignKey(d => d.MeetingId)
                .HasConstraintName("FK_MeetingRosterItemPart");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.RosterItemParts)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemPartRosterItem");

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.RosterItemParts)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemPartRosterItemPartType");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.Interval.Start,
                        e.Interval.End,
                        e.RosterItemPartTypeId
                    },
                    "IX_NC_RosterItemParts_Fetch1"
                )
                .WithSettings(new { FILLFACTOR = 80, STATISTICS_NORECOMPUTE = "ON" });

            entity
                .HasIndex(context.AdditionalIndexCollection, e => e.RosterItemId, "NCI_RosterItemId_INCL")
                .IncludeProperties(e => new
                {
                    e.Id,
                    e.RosterItemPartTypeId,
                    e.MeetingId,
                    e.Interval.Start,
                    e.Interval.End
                })
                .WithSettings(new { STATISTICS_NORECOMPUTE = "ON" });
        });

        modelBuilder.Entity<RosterItemProperty>(entity =>
        {
            entity.ToTable("RosterItemProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.PropertyId, "IX_FK_PropertyRosterItemProperty");

            entity.HasIndex(e => e.RosterItemId, "IX_FK_RosterItemRosterItemProperty");

            entity.Property(e => e.Note).HasMaxLength(1024);
            entity.Property(e => e.StateChanged).IsStoredAsDateTime();
            entity.Property(e => e.Value).HasMaxLength(4000);
            entity.Property(e => e.ValueChanged).IsStoredAsDateTime();

            entity
                .HasOne(d => d.Property)
                .WithMany(p => p.RosterItemProperties)
                .HasForeignKey(d => d.PropertyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PropertyRosterItemProperty");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.RosterItemProperties)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemRosterItemProperty");
        });

        modelBuilder.Entity<RosterItemSyncQueueEntry>(entity =>
        {
            entity.ToTable("RosterItemSyncQueueEntries");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RosterItemId, "IX_FK_RosterItemSyncQueueEntryRosterItem");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.RosterItemSyncQueueEntries)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemSyncQueueEntryRosterItem");
        });

        modelBuilder.Entity<RosterItemSyncState>(entity =>
        {
            entity.ToTable("RosterItemSyncStates");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RosterItemId, "IX_FK_RosterItemRosterItemSyncState");

            entity.Property(e => e.Changed).IsStoredAsDateTime();
            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Data).HasMaxLength(2048);

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.RosterItemSyncStates)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemRosterItemSyncState");
        });

        modelBuilder.Entity<SickNote>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("SickNotes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.SickNoteCode).HasMaxLength(255).HasColumnOrder(iota).IsRequired();
            entity.Property(e => e.PersonalId).HasMaxLength(255).HasColumnOrder(iota).IsRequired();
            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.Property(e => e.From).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.To).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.IllnessTypeCode).HasMaxLength(100).HasColumnOrder(iota).IsRequired();
            entity.Property(e => e.WorkAccident).HasColumnOrder(iota);
            entity.Property(e => e.LastChangeDate).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.SynchronizeStart).HasColumnOrder(iota);
            entity.Property(e => e.SynchronizeEnd).HasColumnOrder(iota);
            entity.Property(e => e.SynchronizeType).HasColumnOrder(iota);
            entity.Property(e => e.TimeOffUpdateFailCount).HasColumnOrder(iota);
            entity.Property(e => e.Created).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Changed).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.HasIndex(e => e.EmployeeId, "IX_FK_SickNoteEmployee");

            entity
                .HasOne(d => d.Employee)
                .WithMany()
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SickNoteEmployee");
        });

        modelBuilder.Entity<StatusPart>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("StatusParts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_StatusPartEmployee");

            entity.Property(e => e.StatusTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.StatusTypeId, "IX_FK_StatusPartStatusType");

            entity.Property(e => e.QueueId).HasColumnOrder(iota);
            entity.HasIndex(e => e.QueueId, "IX_FK_StatusPartQueue");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.StatusParts)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_StatusPartEmployee");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.StatusParts)
                .HasForeignKey(d => d.QueueId)
                .HasConstraintName("FK_StatusPartQueue");

            entity
                .HasOne(d => d.StatusType)
                .WithMany(p => p.StatusParts)
                .HasForeignKey(d => d.StatusTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_StatusPartStatusType");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new { e.EmployeeId, e.Interval.End },
                    "IX_NC_StatusParts_Employee"
                )
                .IncludeProperties(e => new
                {
                    e.Interval.Start,
                    e.StatusTypeId,
                    e.QueueId
                });

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new { e.Interval.Start, e.Interval.End },
                    "IX_NC_StatusParts_Interval"
                )
                .IncludeProperties(e => new
                {
                    e.EmployeeId,
                    e.StatusTypeId,
                    e.QueueId
                });
        });

        modelBuilder.Entity<Tag>(entity =>
        {
            entity.ToTable("Tags");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);

            entity
                .HasMany(d => d.RosterItems)
                .WithMany(p => p.Tags)
                .UsingEntity<Dictionary<string, object>>(
                    "TagRosterItem",
                    r =>
                        r.HasOne<RosterItem>()
                            .WithMany()
                            .HasForeignKey("RosterItemsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_TagRosterItem_RosterItem"),
                    l =>
                        l.HasOne<Tag>()
                            .WithMany()
                            .HasForeignKey("TagsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_TagRosterItem_Tag"),
                    j =>
                    {
                        j.HasKey("TagsId", "RosterItemsId");
                        j.ToTable("TagRosterItem");
                        j.HasIndex(["RosterItemsId"], "IX_FK_TagRosterItem_RosterItem");
                        j.IndexerProperty<TagId>("TagsId").HasColumnName("Tags_Id");
                        j.IndexerProperty<RosterItemId>("RosterItemsId").HasColumnName("RosterItems_Id");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.Tags)
                .UsingEntity<Dictionary<string, object>>(
                    "TagSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_TagSite_Site"),
                    l =>
                        l.HasOne<Tag>()
                            .WithMany()
                            .HasForeignKey("TagsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_TagSite_Tag"),
                    j =>
                    {
                        j.HasKey("TagsId", "SitesId");
                        j.ToTable("TagSite");
                        j.HasIndex(["SitesId"], "IX_FK_TagSite_Site");
                        j.IndexerProperty<TagId>("TagsId").HasColumnName("Tags_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });
    }
}
