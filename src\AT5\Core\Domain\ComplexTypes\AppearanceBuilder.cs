﻿namespace AT.Core.Domain.ComplexTypes;

using AT.Primitives.Enums;

/// <summary>
/// A builder for creating and configuring instances of <see cref="Appearance"/>.
/// </summary>
public class AppearanceBuilder
{
    /// <summary>
    /// Gets the width of the border being configured.
    /// </summary>
    public short BorderWidth { get; private set; }

    /// <summary>
    /// Gets the type of the border being configured.
    /// </summary>
    public BorderType BorderType { get; private set; }

    /// <summary>
    /// Gets the foreground color being configured.
    /// </summary>
    public string ForeColor { get; private set; }

    /// <summary>
    /// Gets the border color being configured.
    /// </summary>
    public string BorderColor { get; private set; }

    /// <summary>
    /// Gets the background color being configured.
    /// </summary>
    public string BackColor { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="AppearanceBuilder"/> class with default values.
    /// </summary>
    public AppearanceBuilder()
        : this(new Appearance()) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="AppearanceBuilder"/> class using an existing <see cref="Appearance"/> instance.
    /// </summary>
    /// <param name="appearance">The <see cref="Appearance"/> instance to initialize the builder with.</param>
    public AppearanceBuilder(Appearance appearance)
    {
        BorderWidth = appearance.BorderWidth;
        BorderType = appearance.BorderType;
        ForeColor = appearance.ForeColor;
        BorderColor = appearance.BorderColor;
        BackColor = appearance.BackColor;
    }

    /// <summary>
    /// Sets the border width for the appearance being configured.
    /// </summary>
    /// <param name="borderWidth">The border width to set.</param>
    /// <returns>The current instance of the <see cref="AppearanceBuilder"/> for method chaining.</returns>
    public AppearanceBuilder WithBorderWidth(short borderWidth)
    {
        BorderWidth = borderWidth;
        return this;
    }

    /// <summary>
    /// Sets the border type for the appearance being configured.
    /// </summary>
    /// <param name="borderType">The border type to set.</param>
    /// <returns>The current instance of the <see cref="AppearanceBuilder"/> for method chaining.</returns>
    public AppearanceBuilder WithBorderType(BorderType borderType)
    {
        BorderType = borderType;
        return this;
    }

    /// <summary>
    /// Sets the foreground color for the appearance being configured.
    /// </summary>
    /// <param name="foreColor">The foreground color to set.</param>
    /// <returns>The current instance of the <see cref="AppearanceBuilder"/> for method chaining.</returns>
    public AppearanceBuilder WithForeColor(string foreColor)
    {
        // FUTURE: Extend or replace WithForeColor by a method accepting a Color data type.
        ForeColor = foreColor;
        return this;
    }

    /// <summary>
    /// Sets the border color for the appearance being configured.
    /// </summary>
    /// <param name="borderColor">The border color to set.</param>
    /// <returns>The current instance of the <see cref="AppearanceBuilder"/> for method chaining.</returns>
    public AppearanceBuilder WithBorderColor(string borderColor)
    {
        // FUTURE: Extend or replace WithBorderColor by a method accepting a Color data type.
        BorderColor = borderColor;
        return this;
    }

    /// <summary>
    /// Sets the background color for the appearance being configured.
    /// </summary>
    /// <param name="backColor">The background color to set.</param>
    /// <returns>The current instance of the <see cref="AppearanceBuilder"/> for method chaining.</returns>
    public AppearanceBuilder WithBackColor(string backColor)
    {
        // FUTURE: Extend or replace WithBackColor by a method accepting a Color data type.
        BackColor = backColor;
        return this;
    }

    /// <summary>
    /// Builds and returns a new <see cref="Appearance"/> instance based on the current configuration.
    /// </summary>
    /// <returns>A new <see cref="Appearance"/> instance.</returns>
    public Appearance Build()
    {
        return new Appearance(BorderWidth, BorderType, ForeColor, BorderColor, BackColor);
    }
}
