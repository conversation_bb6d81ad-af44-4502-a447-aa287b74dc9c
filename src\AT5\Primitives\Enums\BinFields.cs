﻿namespace AT.Primitives.Enums;

/// <summary>
/// Aggregated interactions properties over time interval bin. Used to display data
/// </summary>
public enum BinFields
{
    /// <summary>
    /// Count of offered interactions
    /// </summary>
    Offered = 0,

    /// <summary>
    /// Count of handled (answered) interactions
    /// </summary>
    Handled = 1,

    /// <summary>
    /// Count of interactions handled over acceptable waiting time
    /// </summary>
    QueuedOver = 3,

    /// <summary>
    /// Total count of abandoned interactions
    /// </summary>
    Abandoned = 5,

    /// <summary>
    /// Count of interactions abandoned over the limit of abandon time
    /// </summary>
    AbandonedOver = 6,

    /// <summary>
    /// Count of interactions abandoned in the limit of abandon time
    /// </summary>
    AbandonedInLimit = 7,

    /// <summary>
    /// Service Level
    /// </summary>
    ServiceLevel = 8,

    /// <summary>
    /// Service time Mu parameter (mean service time)
    /// </summary>
    AverageHandleTime = 11,

    /// <summary>
    /// Count of communications abandoned within short limit different from abandoned in limit
    /// </summary>
    AbandonedShort = 13,
}

/// <summary>
/// Enum used to query bin fields actually stored in db.
/// </summary>
public enum BinFieldsInternal
{
    Offered,
    Handled,
    QueuedOver,
    Abandoned,
    AbandonedOver,
    AverageHandleTime,
    AbandonedShort,
}

public enum PredictionBinFields
{
    Offered,
    AverageHandleTime
}

public static class BinFieldExtensions
{
    private static readonly HashSet<BinFields> s_notSumFields = [BinFields.ServiceLevel, BinFields.AverageHandleTime,];

    public static bool IsSumField(this BinFields field)
    {
        return !s_notSumFields.Contains(field);
    }

    public static readonly IReadOnlyDictionary<BinFields, BinFieldsInternal> ToInternal = new Dictionary<
        BinFields,
        BinFieldsInternal
    >
    {
        { BinFields.Offered, BinFieldsInternal.Offered },
        { BinFields.Handled, BinFieldsInternal.Handled },
        { BinFields.QueuedOver, BinFieldsInternal.QueuedOver },
        { BinFields.Abandoned, BinFieldsInternal.Abandoned },
        { BinFields.AbandonedOver, BinFieldsInternal.AbandonedOver },
        { BinFields.AverageHandleTime, BinFieldsInternal.AverageHandleTime },
        { BinFields.AbandonedShort, BinFieldsInternal.AbandonedShort }
    };

    public static bool IsInternalField(this BinFields field)
    {
        return ToInternal.Keys.Contains(field);
    }

    public static bool IsSumInternalField(this BinFieldsInternal field)
    {
        return field != BinFieldsInternal.AverageHandleTime;
    }

    public static bool IsSumPredictionField(this PredictionBinFields field)
    {
        return field != PredictionBinFields.AverageHandleTime;
    }

    public static readonly IReadOnlyList<BinFieldsInternal> AllInternalFields = Enum.GetValues(
            typeof(BinFieldsInternal)
        )
        .Cast<BinFieldsInternal>()
        .ToList();

    public static readonly IReadOnlyList<PredictionBinFields> AllPredictionFields = Enum.GetValues(
            typeof(PredictionBinFields)
        )
        .Cast<PredictionBinFields>()
        .ToList();
}
