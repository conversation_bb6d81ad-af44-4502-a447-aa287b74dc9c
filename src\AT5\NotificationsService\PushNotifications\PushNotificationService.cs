﻿namespace AT.NotificationsService.PushNotifications;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate.Queries;
using AT.NotificationsService.General.Interfaces;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Utilities.Collections;
using AT.Utilities.Logging;

public class PushNotificationService(
    IPushNotificationTokenForUsersQuery _pushNotificationTokenForUsersQuery,
    IPushNotificationSender _pushNotificationSender,
    IPushNotificationTokensCleanupService _pushNotificationTokensCleanupService,
    IPushNotificationLogger _pushNotificationLogger,
    ILogger<PushNotificationService> _logger
) : IMessageService<PushNotification>
{
    public async Task<SendMessageResult<PushNotification>> SendMessagesAsync(
        IReadOnlyCollection<PushNotification> messages,
        CancellationToken cancellationToken = default
    )
    {
        var recipientUserIds = messages
            .SelectMany(pn => pn.PushNotificationRecipients)
            .Select(pnr => pnr.UserId)
            .Distinct()
            .ToList();

        var tokensPerUserId = (
            await _pushNotificationTokenForUsersQuery.GetTokensPerUserId(recipientUserIds, cancellationToken)
        )
            .GroupBy(x => x.UserId)
            .ToDictionary(g => g.Key, g => g.ToList());

        _logger.Info(
            "Found tokens for {UsersWithTokenCount}/{TotalUsersCount} recipients.",
            tokensPerUserId.Count,
            recipientUserIds.Count
        );

        var failedPushNotifications = new List<PushNotification>();
        var partiallyFailedPushNotifications = new List<PushNotification>();

        foreach (var pushNotification in messages)
        {
            _logger.Info("Sending push notification {PushNotification}", pushNotification);

            var recipientsUserIds = pushNotification.PushNotificationRecipients.Select(r => r.UserId).ToHashSet();

            var tokens = recipientsUserIds
                .SelectMany(uId => tokensPerUserId.GetValueOr(uId) ?? Enumerable.Empty<UserPnToken>())
                .ToArray();
            var tokenStrings = tokens.Select(x => x.Token).ToList();

            var recipientsWithoutToken = recipientsUserIds
                .Where(uId => !tokensPerUserId.TryGetValue(uId, out var userTokens) || userTokens.IsEmpty())
                .ToHashSet();

            if (recipientsWithoutToken.Count > 0)
            {
                _logger.Warn(
                    "These users have no tokens and will not receive push notification {NotificationId}: {UsersWithoutToken}",
                    pushNotification.Id,
                    string.Join(", ", recipientsWithoutToken)
                );
            }

            if (recipientsWithoutToken.Count == recipientsUserIds.Count)
            {
                _pushNotificationLogger.LogMessageSendFailed(pushNotification, eventReason: "No tokens found");
                _logger.Warn(
                    "Push notification {NotificationId} will not be sent because no tokens were found for any recipient",
                    pushNotification.Id
                );
                continue;
            }

            _logger.Trace("Using tokens: {Tokens}", string.Join(", ", tokenStrings));
            _pushNotificationLogger.LogPushNotificationTokensLoad(pushNotification, tokens: tokenStrings);

            var result = await _pushNotificationSender.SendPushNotificationToMultipleAsync(
                tokenStrings,
                pushNotification,
                cancellationToken: cancellationToken
            );

            if (!result.Success)
            {
                if (result.PartialSuccess)
                {
                    partiallyFailedPushNotifications.Add(pushNotification);
                }
                else
                {
                    failedPushNotifications.Add(pushNotification);
                }
            }

            ProcessSendToMultipleResult(pushNotification, result, tokens);
        }

        return new SendMessageResult<PushNotification>(
            FailedMessages: failedPushNotifications,
            PartiallyFailedMessages: partiallyFailedPushNotifications
        );
    }

    private void ProcessSendToMultipleResult(
        PushNotification notification,
        PushNotificationSendToMultipleResult result,
        IReadOnlyCollection<UserPnToken> tokens
    )
    {
        if (result.Success)
        {
            _pushNotificationLogger.LogMessageSendSuccessful(notification);
            _logger.Info("Push notification {NotificationId} successfully sent to everyone.", notification.Id);
            return;
        }

        _pushNotificationTokensCleanupService.EnqueueTokensToDeleteFromDatabaseIfInvalid(tokens);

        var exceptionsCsv = string.Join(", ", result.Exceptions.Select(e => e.ToString()));

        if (result.SuccessCount == 0)
        {
            _pushNotificationLogger.LogMessageSendFailed(
                notification,
                eventReason: string.Join(", ", result.Exceptions.Select(e => e.Message))
            );

            _logger.Warn(
                "Push notification {Notification} was not sent to anyone. Failures: {Exceptions}",
                notification,
                exceptionsCsv
            );

            return;
        }

        _logger.Warn(
            "Push notification {Notification} was not sent to everyone. Failures: {Exceptions}",
            notification,
            exceptionsCsv
        );
        _pushNotificationLogger.LogPushNotificationSendPartiallySuccessful(notification);
    }
}
