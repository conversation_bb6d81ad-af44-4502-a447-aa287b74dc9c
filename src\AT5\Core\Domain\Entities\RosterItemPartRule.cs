﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class RosterItemPartRule
{
    public RosterItemPartRuleId Id { get; set; }

    public RosterItemPartRuleType RuleType { get; set; }

    public string RuleEntity { get; set; } = null!;

    public RosterItemPartTypeId RosterItemPartTypeId { get; set; }

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;

    public virtual ICollection<RosterItemPartType> RosterItemPartTypes { get; set; } = new List<RosterItemPartType>();
}
