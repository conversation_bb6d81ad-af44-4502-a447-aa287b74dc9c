﻿namespace AT.NotificationsService.NotificationsLimiting.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.ConfigurationParameters;

/// <summary>
/// Message-limiting means not sending the same message to the same recipient multiple times within a certain period of time.
/// The types of messages and the specific periods of time are given by <see cref="NotificationsRateLimitingConfig">.
/// </summary>
public interface IOrganizationMessageLimitingService
{
    Task<NotificationsRateLimitingConfig?> LoadRateLimitingConfigAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Based on the <paramref name="rateLimitingConfig"/>, returns only the <paramref name="emailMessages"/> that were not filtered.
    /// </summary>
    Task<IEnumerable<EmailMessage>> ApplyLimitingAsync(
        NotificationsRateLimitingConfig rateLimitingConfig,
        IReadOnlyCollection<EmailMessage> emailMessages,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Based on the <paramref name="rateLimitingConfig" />, stores relevant <paramref name="sentEmailMessages" />
    /// as <see cref="SentNotification" /> entities in the database.
    /// </summary>
    /// <param name="sentEmailMessages">
    /// Email message may not have been sent successfully to all recipient. Therefore, instead of taking recipients directly from each
    /// email message's container of recipients, there is an explicit collection of recipients who actually received the email message.
    /// </param>>
    Task StoreAsSentNotificationsAsync(
        NotificationsRateLimitingConfig rateLimitingConfig,
        IReadOnlyDictionary<EmailMessage, IReadOnlyCollection<EmailMessageRecipient>> sentEmailMessages,
        CancellationToken cancellationToken = default
    );
}
