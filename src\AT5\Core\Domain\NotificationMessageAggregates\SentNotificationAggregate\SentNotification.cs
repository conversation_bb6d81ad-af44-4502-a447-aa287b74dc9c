﻿namespace AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using Primitives.Enums;

/// <summary>
/// Closely related to <see cref="ConfigurationParameters.NotificationsRateLimitingConfig"/>. In order to determine whether
/// the NotificationsService has already sent some message (e.g., an email) for which a rate-limiting is configured, we need
/// to store the information about the sent message somewhere. Thus, we use a database table with <see cref="SentNotification"/> entities.
/// </summary>
public class SentNotification : IAggregateRoot
{
    public long Id { get; set; }

    public SentNotificationType NotificationType { get; set; }

    /// <summary>
    /// Because each message type stores their own enum of entity types, this has to be an integer.
    /// </summary>
    public int NotificationEntityType { get; set; }

    public int? EntityId { get; set; }

    public string? EventParameters { get; set; }

    public string Title { get; set; } = null!;

    public string Body { get; set; } = null!;

    public DateTime? SendTime { get; set; }

    public virtual ICollection<SentNotificationRecipient> SentNotificationRecipients { get; set; } =
        new List<SentNotificationRecipient>();

    public static SentNotification CreateFromEmailMessage(
        EmailMessage emailMessage,
        IEnumerable<EmailMessageRecipient> recipients,
        DateTime sendTime
    )
    {
        return new SentNotification()
        {
            NotificationType = SentNotificationType.EmailMessage,
            NotificationEntityType = (int)emailMessage.EmailType,
            EntityId = emailMessage.EntityId,
            EventParameters = emailMessage.EventParameters,
            Title = emailMessage.Subject,
            Body = emailMessage.Body,
            SendTime = sendTime,
            SentNotificationRecipients = recipients
                .Select(SentNotificationRecipient.CreateFromEmailMessageRecipient)
                .ToArray(),
        };
    }

    public override string ToString()
    {
        var basic =
            $"Id: {Id}, NotificationType: {NotificationType}, NotificationEntityType: {NotificationEntityType}, EntityId: {EntityId}, EventParameters: {EventParameters}, Title: {Title}, Body: {Body}, SendTime: {SendTime}";
        var recipients = string.Join(
            ", ",
            SentNotificationRecipients.Select(r => $"{{ IdentifierType: {r.IdentifierType} Address: {r.Identifier} }}")
        );

        return $"{{ {basic}, Recipients: [ {recipients} ] }}";
    }
}
