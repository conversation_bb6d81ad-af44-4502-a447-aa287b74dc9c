namespace AT.ConsoleExample.Commands.EfCoreExample;

using AT.Core.Domain.UserAggregate;
using AT.Utilities.Time;
using AT.Utilities.Time.Utils;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Domain.Entities;

[Command("ef adduser", Description = "Adds a new user with the given username.")]
public class AddUserCommand(ITimeProvider _timeProvider, IUserRepository _userRepo) : ICommand
{
    [CommandParameter(order: 0, Description = "The Username of the user.")]
    public required string Username { get; init; }

    [CommandParameter(order: 1, Description = "The number of randomly generated notes.")]
    public required int NoteCount { get; init; }

    public async ValueTask ExecuteAsync(IConsole console)
    {
        var defaultDate = DateTimeUtils.CreateLocal(2000, 1, 1);
        var now = _timeProvider.Now;

        var user = new User
        {
            Username = Username,
            Created = now,
            Changed = now,
            LastLogin = defaultDate,
            LastLockout = defaultDate,
            FailedPasswordStart = defaultDate,
            LastPasswordChange = defaultDate,
            FirstName = "first",
            LastName = "last",

            // Generate random notes in the specified count
            Notes = Enumerable
                .Range(1, NoteCount)
                .Select(_ => new Note { Text = Guid.NewGuid().ToString(), Created = now })
                .ToList()
        };

        _userRepo.AddForInsert(user);
        await _userRepo.CommitAsync();

        await console.Output.WriteLineAsync(
            $"Added user {user.Username} with {user.Notes.Count} notes. User id: {user.Id}."
        );
    }
}
