﻿namespace AT.Shared.Models;

public class ForecastResponse
{
    public required Forecast[] Forecasts { get; set; }
}

public class Forecast
{
    public DateOnly Date { get; set; }

    public int Temperature { get; set; }

    public string Summary { get; set; }

    public Forecast(DateOnly date, int temperature, string summary)
    {
        Date = date;
        Temperature = temperature;
        Summary = summary;
    }
}
