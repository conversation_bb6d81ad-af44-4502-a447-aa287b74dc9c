﻿namespace AT.Core.Domain.Entities;

public class SiteProperty
{
    public SitePropertyId Id { get; set; }

    public string? Value { get; set; }

    public Validity Validity { get; set; }

    public PropertyId PropertyId { get; set; }

    public SiteId SiteId { get; set; }

    public byte Source { get; set; }

    public virtual Property Property { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
