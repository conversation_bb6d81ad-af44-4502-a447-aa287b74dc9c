﻿namespace AT.Core.Domain.Entities;

using Base;

public class AuditUpdateProperty
{
    public AuditUpdatePropertyId Id { get; set; }

    public string PropertyName { get; set; } = null!;

    public string PropertyOldValue { get; set; } = null!;

    public string PropertyNewValue { get; set; } = null!;

    public AuditEntityId AuditUpdateId { get; set; }

    public virtual AuditUpdate AuditUpdate { get; set; } = null!;
}
