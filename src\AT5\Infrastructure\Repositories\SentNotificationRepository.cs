﻿namespace AT.Infrastructure.Repositories;

using Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using DataAccess;
using Database;

public sealed class SentNotificationRepository(OrganizationDbContext _dbContext)
    : EfRepository<SentNotification>(_dbContext),
        ISentNotificationRepository,
        IRepositoryWithFactoryMethod<ISentNotificationRepository>
{
    public static new ISentNotificationRepository Create(OrganizationDbContext dbContext)
    {
        return new SentNotificationRepository(dbContext);
    }

    public override void AddForDelete(SentNotification sentNotification)
    {
        foreach (var recipient in sentNotification.SentNotificationRecipients)
        {
            DbContext.Remove(recipient);
        }

        DbContext.Remove(sentNotification);
    }
}
