﻿namespace AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;

public class SentNotificationsSpec : Specification<SentNotification>
{
    public SentNotificationsSpec(DateTime? untilExclusive = null)
    {
        Query.Include(pn => pn.SentNotificationRecipients);

        if (untilExclusive.HasValue)
        {
            Query.Where(pn => pn.SendTime < untilExclusive);
        }
    }
}
