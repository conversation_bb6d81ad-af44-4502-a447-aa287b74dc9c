﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public interface IAdditionalIndexCollection
{
    void Clear();

    void CreateFixedIndexBuilder(string indexCreationCommand);

    ITypedAdditionalIndexBuilder<T> GetOrCreateIndexBuilder<T>(
        EntityTypeBuilder<T> entityTypeBuilder,
        Expression<Func<T, object?>> indexExpression,
        string name
    )
        where T : class;

    ITypedAdditionalIndexBuilder<T> GetOrCreateIndexBuilder<T>(
        EntityTypeBuilder<T> entityTypeBuilder,
        string[] propertyNames,
        string name
    )
        where T : class;

    IEnumerable<FormattableString> GetIndexes();
}
