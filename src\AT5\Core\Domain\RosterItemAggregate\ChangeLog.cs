﻿namespace AT.Core.Domain.RosterItemAggregate;

using AT.Core.Domain.UserAggregate;
using Primitives.Enums;

public class ChangeLog
{
    public ChangeLogId Id { get; set; }

    public DateTime Timestamp { get; set; }

    public UserId UserId { get; set; }

    public bool Reverted { get; set; }

    public RosterItemChangeSource ChangeSource { get; set; }

    public virtual ICollection<Change> Changes { get; set; } = new List<Change>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual User User { get; set; } = null!;
}
