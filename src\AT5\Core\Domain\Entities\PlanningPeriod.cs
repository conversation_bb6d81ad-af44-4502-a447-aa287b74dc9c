﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class PlanningPeriod
{
    public PlanningPeriodId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public SiteId SiteId { get; set; }

    public DateInterval Interval { get; set; }

    public DateInterval RequestInterval { get; set; }

    public DateTime Created { get; set; }

    public UserId CreatedById { get; set; }

    public bool HideInRoster { get; set; }

    public RosterId? InputRosterId { get; set; }

    public RosterId? PrepublicRosterId { get; set; }

    public virtual ICollection<Calculation> Calculations { get; set; } = new List<Calculation>();

    public virtual User CreatedBy { get; set; } = null!;

    public virtual Roster? InputRoster { get; set; }

    public virtual Roster? PrepublicRoster { get; set; }

    public virtual ICollection<Roster> Rosters { get; set; } = new List<Roster>();

    public virtual Site Site { get; set; } = null!;
}
