﻿@page "/cities"

@using AT.ApiClient
@using AT.Shared.Models

@inject ApiClient ApiClient

<PageTitle>Cities</PageTitle>

<h1>Cities</h1>

@if (city == null)
{
    <p><em>Loading...</em></p>
}
else
{
    <p>City: @city.Name</p>
}

@code {
    private City? city;

    protected override async Task OnInitializedAsync()
    {
        // ISSUE: This returns Unauthorized right now because we don't add user-id and tenant-id in the requests.
        // PostCityRequest req = new() { Name = "Prague", Population = 42 };
        // await ApiClient.ATApiServiceEndpointsDemoPostCityEndpointAsync(req);

        city = (await ApiClient.ATApiServiceEndpointsCitiesEndpointAsync()).Cities.FirstOrDefault();
    }
}
