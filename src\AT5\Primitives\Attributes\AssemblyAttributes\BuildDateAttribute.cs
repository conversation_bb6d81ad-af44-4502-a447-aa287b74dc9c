﻿namespace AT.Primitives.Attributes.AssemblyAttributes;

using System.Globalization;

/// <summary>
/// NOTE: Currently in the "Primitives" project because we need to share this between AT4 and AT5.
/// </summary>
[AttributeUsage(AttributeTargets.Assembly)]
public class BuildDateAttribute(string _value) : Attribute
{
    public DateTime DateTime { get; } =
        DateTime.ParseExact(_value, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None);
}
