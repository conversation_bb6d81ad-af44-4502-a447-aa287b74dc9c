﻿namespace AT.Core.Domain.ComplexTypes;

using AT.Primitives.Enums;

/// <summary>
/// Represents the appearance properties of an object.
/// </summary>
public record Appearance
{
    /// <summary>
    /// Gets the width of the border.
    /// </summary>
    public short BorderWidth { get; }

    /// <summary>
    /// Gets the type of the border.
    /// </summary>
    public BorderType BorderType { get; }

    /// <summary>
    /// Gets the color of the foreground.
    /// </summary>
    public string ForeColor { get; }

    /// <summary>
    /// Gets the color of the border.
    /// </summary>
    public string BorderColor { get; }

    /// <summary>
    /// Gets the color of the background.
    /// </summary>
    public string BackColor { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="Appearance"/> class with default values.
    /// </summary>
    public Appearance()
        : this(default(short), default(BorderType), string.Empty, string.Empty, string.Empty) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Appearance"/> class with the specified appearance properties.
    /// </summary>
    /// <param name="borderWidth">The width of the border.</param>
    /// <param name="borderType">The type of the border.</param>
    /// <param name="foreColor">The color of the foreground.</param>
    /// <param name="borderColor">The color of the border.</param>
    /// <param name="backColor">The color of the background.</param>
    public Appearance(short borderWidth, BorderType borderType, string foreColor, string borderColor, string backColor)
    {
        BorderWidth = borderWidth;
        BorderType = borderType;
        ForeColor = foreColor;
        BorderColor = borderColor;
        BackColor = backColor;
    }
}
