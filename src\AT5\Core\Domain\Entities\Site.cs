﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ConfigurationParameterAggregate;
using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;

public class Site
{
    public SiteId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Location { get; set; }

    public string? Description { get; set; }

    public string? Code { get; set; }

    public bool AllowEmployees { get; set; }

    public bool AllowContracts { get; set; }

    public bool AllowPlanningPeriods { get; set; }

    public bool AllowPlanning { get; set; }

    public bool AllowAsLocation { get; set; }

    public SiteTypeId TypeId { get; set; }

    public string? CostCenter { get; set; }

    public string? ExternalId { get; set; }

    public bool AllowNeeds { get; set; }

    public SiteSubtypeId? SubtypeId { get; set; }

    public Validity Validity { get; set; }

    public AutoGenerateShiftsOnSite AutoGenerateShifts { get; set; }

    public BalanceConfigurationId? BalanceConfigurationId { get; set; }

    public virtual BalanceConfiguration? BalanceConfiguration { get; set; }

    public virtual ICollection<BudgetHoursAllowance> BudgetHoursAllowances { get; set; } =
        new List<BudgetHoursAllowance>();

    public virtual ICollection<EmployeeActivity> EmployeeActivities { get; set; } = new List<EmployeeActivity>();

    public virtual ICollection<EmployeeBalance> EmployeeBalances { get; set; } = new List<EmployeeBalance>();

    public virtual ICollection<EmployeeContract> EmployeeContracts { get; set; } = new List<EmployeeContract>();

    public virtual ICollection<EmployeeLocation> EmployeeLocations { get; set; } = new List<EmployeeLocation>();

    public virtual ICollection<EmployeePosition> EmployeePositions { get; set; } = new List<EmployeePosition>();

    public virtual ICollection<EmployeeProperty> EmployeeProperties { get; set; } = new List<EmployeeProperty>();

    public virtual ICollection<EmployeePublicRoster> EmployeePublicRosters { get; set; } =
        new List<EmployeePublicRoster>();

    public virtual ICollection<EmployeeRequestType> EmployeeRequestTypes { get; set; } =
        new List<EmployeeRequestType>();

    public virtual ICollection<EmployeeRosterFinish> EmployeeRosterFinishes { get; set; } =
        new List<EmployeeRosterFinish>();

    public virtual ICollection<EmployeeShiftTemplate> EmployeeShiftTemplates { get; set; } =
        new List<EmployeeShiftTemplate>();

    public virtual ICollection<DateNote> DateNotes { get; set; } = new List<DateNote>();

    public virtual ICollection<PlanningPeriod> PlanningPeriods { get; set; } = new List<PlanningPeriod>();

    public virtual ICollection<PreparationWay> PreparationWays { get; set; } = new List<PreparationWay>();

    public virtual ICollection<ReportColumn> ReportColumns { get; set; } = new List<ReportColumn>();

    public virtual ICollection<Request> Requests { get; set; } = new List<Request>();

    public virtual ICollection<Requirement> Requirements { get; set; } = new List<Requirement>();

    public virtual ICollection<RoleDelegation> RoleDelegations { get; set; } = new List<RoleDelegation>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<Roster> Rosters { get; set; } = new List<Roster>();

    public virtual ICollection<ShiftPopularity> ShiftPopularities { get; set; } = new List<ShiftPopularity>();

    public virtual ICollection<SiteProperty> SiteProperties { get; set; } = new List<SiteProperty>();

    public virtual ICollection<SiteRelation> SiteRelationChildSites { get; set; } = new List<SiteRelation>();

    public virtual ICollection<SiteRelation> SiteRelationParentSites { get; set; } = new List<SiteRelation>();

    public virtual ICollection<SitesConfigParameter> SitesConfigParameters { get; set; } =
        new List<SitesConfigParameter>();

    public virtual SiteSubtype? Subtype { get; set; }

    public virtual ICollection<Team> Teams { get; set; } = new List<Team>();

    public virtual SiteType Type { get; set; } = null!;

    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    public virtual ICollection<WorkOrderPrediction> WorkOrderPredictions { get; set; } =
        new List<WorkOrderPrediction>();

    public virtual ICollection<WorkOrderProductivity> WorkOrderProductivities { get; set; } =
        new List<WorkOrderProductivity>();

    public virtual ICollection<WorkOrder> WorkOrders { get; set; } = new List<WorkOrder>();

    public virtual ICollection<Site> AdditionalPermissionsSources { get; set; } = new List<Site>();

    public virtual ICollection<Agency> Agencies { get; set; } = new List<Agency>();

    public virtual ICollection<Property> AllowedProperties { get; set; } = new List<Property>();

    public virtual ICollection<CalculationType> CalculationTypes { get; set; } = new List<CalculationType>();

    public virtual ICollection<Calculation> Calculations { get; set; } = new List<Calculation>();

    public virtual ICollection<Contract> Contracts { get; set; } = new List<Contract>();

    public virtual ICollection<EmployeeContract> EmployeeContractAllowedSiteSites { get; set; } =
        new List<EmployeeContract>();

    public virtual ICollection<RoleDelegation> ExplicitlyGovernedByRoleDelegations { get; set; } =
        new List<RoleDelegation>();

    public virtual ICollection<LicenceRule> LicenceRules { get; set; } = new List<LicenceRule>();

    public virtual ICollection<Location> Locations { get; set; } = new List<Location>();

    public virtual ICollection<Site> PermissionsDestinations { get; set; } = new List<Site>();

    public virtual ICollection<Position> Positions { get; set; } = new List<Position>();

    public virtual ICollection<PublicHoliday> PublicHolidays { get; set; } = new List<PublicHoliday>();

    public virtual ICollection<Queue> Queues { get; set; } = new List<Queue>();

    public virtual ICollection<RequestLimit> RequestLimits { get; set; } = new List<RequestLimit>();

    public virtual ICollection<Requirement> RequirementSites { get; set; } = new List<Requirement>();

    public virtual ICollection<RosterItemPartType> RosterItemPartTypes { get; set; } = new List<RosterItemPartType>();

    public virtual ICollection<RequestType> RosterRequestTypes { get; set; } = new List<RequestType>();

    public virtual ICollection<Routing> Routings { get; set; } = new List<Routing>();

    public virtual ICollection<ShiftSystem> ShiftSystems { get; set; } = new List<ShiftSystem>();

    public virtual ICollection<ShiftTemplate> ShiftTemplates { get; set; } = new List<ShiftTemplate>();

    public virtual ICollection<ReportColumn> SiteReportColumnSites { get; set; } = new List<ReportColumn>();

    public virtual ICollection<Tag> Tags { get; set; } = new List<Tag>();

    public virtual ICollection<WorkMissionType> WorkMissionTypes { get; set; } = new List<WorkMissionType>();

    public virtual ICollection<WorkingTimeModel> WorkingTimeModels { get; set; } = new List<WorkingTimeModel>();

    public virtual ICollection<WorkingTime> WorkingTimes { get; set; } = new List<WorkingTime>();
}
