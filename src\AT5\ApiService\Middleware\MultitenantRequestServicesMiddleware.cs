﻿namespace AT.ApiService.Middleware;

using AT.Infrastructure.DependencyInjection;
using Autofac;
using Autofac.Integration.AspNetCore.Multitenant;
using Autofac.Multitenant;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;

/// <summary>
/// Middleware that forces the request lifetime scope to be created from the multitenant container directly to avoid inadvertent incorrect tenant identification.
/// Based on Autofac MultitenantRequestServicesMiddleware from Autofac.Integration.AspNetCore.Multitenant but call directly AppServiceProvider
/// so that organizations are configured.
/// </summary>
internal class MultitenantRequestServicesMiddleware(
    IHttpContextAccessor _contextAccessor,
    ITenantIdentificationStrategy _tenantIdentificationStrategy,
    AppServiceProvider _appServiceProvider
) : IMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // If there isn't already an HttpContext set on the context
        // accessor for this async/thread operation, set it. This allows
        // tenant identification to use it.
        _contextAccessor.HttpContext ??= context;

        IServiceProvidersFeature existingFeature = null!;
        try
        {
            _tenantIdentificationStrategy.TryIdentifyTenant(out var tenantId);
            var serviceScopeFactoryAdapter = _appServiceProvider
                .GetTenantLifetimeScopeUntyped(tenantId) // This will initialize the tenant, direct usage of Multitenant container would not.
                .Resolve<MultitenantServiceScopeFactoryAdapter>();

            // The feature will be disposed at the end of the response, not here.
#pragma warning disable CA2000
            var autofacFeature = new RequestServicesFeature(context, serviceScopeFactoryAdapter.Factory);
#pragma warning restore

            context.Response.RegisterForDisposeAsync(autofacFeature);

            existingFeature = context.Features.Get<IServiceProvidersFeature>()!;
            context.Features.Set<IServiceProvidersFeature>(autofacFeature);

            await next.Invoke(context);
        }
        finally
        {
            // In ASP.NET Core 1.x the existing feature will disposed as part of
            // a using statement; in ASP.NET Core 2.x it is registered directly
            // with the response for disposal. In either case, we don't have to
            // do that. We do put back any existing feature, though, since
            // at this point there may have been some default tenant or base
            // container level stuff resolved and after this middleware it needs
            // to be what it was before.
            context.Features.Set(existingFeature);
        }
    }
}
