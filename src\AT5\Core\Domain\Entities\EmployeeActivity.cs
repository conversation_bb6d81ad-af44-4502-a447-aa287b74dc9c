﻿namespace AT.Core.Domain.Entities;

public class EmployeeActivity
{
    public EmployeeActivityId Id { get; set; }

    public int? Min { get; set; }

    public int? Max { get; set; }

    public double Priority { get; set; }

    public UserId EmployeeId { get; set; }

    public RosterItemPartTypeId ActivityId { get; set; }

    public SiteId SiteId { get; set; }

    public Validity Validity { get; set; }

    public virtual ActivityType Activity { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
