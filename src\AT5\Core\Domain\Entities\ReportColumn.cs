﻿namespace AT.Core.Domain.Entities;

public class ReportColumn
{
    public ReportColumnId Id { get; set; }

    public int Rank { get; set; }

    public string Name { get; set; } = null!;

    public ReportId ReportId { get; set; }

    public SiteId SiteDepId { get; set; }

    public AgregateFunctions AgregateFunction { get; set; }

    public bool Global { get; set; }

    public string? Expression { get; set; }

    public AgregateFunctions TotalAggregateFuncion { get; set; }

    public string? Parameters { get; set; }

    public bool Hidden { get; set; }

    public string? Description { get; set; }

    public Validity Validity { get; set; }

    public virtual Report Report { get; set; } = null!;

    public virtual ICollection<ReportColumnField> ReportColumnFields { get; set; } = new List<ReportColumnField>();

    public virtual Site SiteDep { get; set; } = null!;

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
