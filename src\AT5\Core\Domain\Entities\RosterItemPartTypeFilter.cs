﻿namespace AT.Core.Domain.Entities;

public class RosterItemPartTypeFilter
{
    public RosterItemPartTypeFilterId Id { get; set; }

    public Filter2Id FilterId { get; set; }

    public RosterItemPartTypeId RosterItemPartTypeId { get; set; }

    public Validity Validity { get; set; }

    public string? Parameters { get; set; }

    public virtual Filter2 Filter { get; set; } = null!;

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;
}
