﻿namespace AT.Core.Domain.Entities;

using System;

public class JobLogEntry
{
    public JobLogEntryId Id { get; set; }

    public JobRunId JobRunId { get; set; }

    public DateTime Timestamp { get; set; }

    public ErrorLevel Level { get; set; }

    public string MessageId { get; set; } = null!;

    public string MessageTranslated { get; set; } = null!;

    public string MessageParameters { get; set; } = null!;

    public virtual JobRun JobRun { get; set; } = null!;
}
