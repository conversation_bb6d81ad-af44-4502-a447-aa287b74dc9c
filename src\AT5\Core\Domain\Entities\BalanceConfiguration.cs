﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class BalanceConfiguration
{
    public BalanceConfigurationId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Code { get; set; } = null!;

    public BalanceType Type { get; set; }

    public int? BalancingMonths { get; set; }

    public bool ForcePaymentAtContractEnd { get; set; }

    public bool DisallowNegativeBalance { get; set; }

    public bool DisallowCumulativePaidOvertime { get; set; }

    public string? Parameters { get; set; }

    public bool DisallowPositiveBalance { get; set; }

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();

    public virtual ICollection<WorkingTimeModel> WorkingTimeModels { get; set; } = new List<WorkingTimeModel>();
}
