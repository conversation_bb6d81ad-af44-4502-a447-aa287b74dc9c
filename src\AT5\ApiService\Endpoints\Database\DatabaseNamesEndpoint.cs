﻿#pragma warning disable S3261 // This file is empty in DEBUG mode but we want to ignore that.
namespace AT.ApiService.Endpoints.Database;

using AT.Infrastructure.Database;
using AT.Shared.Models.Database;
using FastEndpoints;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;

#if DEBUG
public class DatabaseNamesEndpoint(
    IDbContextFactory<MasterDbContext> _masterDbContextFactory,
    IDbContextFactory<OrganizationDbContext> _dbContextFactory,
    OrganizationDbContext _dbContext,
    OrganizationDbContextReadOnly _dbContextReadOnly
) : EndpointWithoutRequest<DatabaseNamesResponse>
{
    public override void Configure()
    {
        Get("/database/namestest");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        using var masterDbContext = _masterDbContextFactory.CreateDbContext();
        var masterDbName = await GetDbName(masterDbContext.Database, ct);

        using var dbContextFromFactory = _dbContextFactory.CreateDbContext();
        var orgDbName1 = await GetDbName(dbContextFromFactory.Database, ct);
        var orgDbName2 = await GetDbName(_dbContext.Database, ct);
        var orgDbName3 = await GetDbName(_dbContextReadOnly.Database, ct);

        if (orgDbName1 != orgDbName2 || orgDbName1 != orgDbName3)
        {
            throw new Exception($"Organization DB names are not the same: {orgDbName1}, {orgDbName2}, {orgDbName3}");
        }

        await SendAsync(new DatabaseNamesResponse(masterDbName, orgDbName3), cancellation: ct);
    }

    private Task<string> GetDbName(DatabaseFacade databse, CancellationToken ct)
    {
        return databse.SqlQuery<string>($"SELECT DB_NAME() as Value").SingleAsync(ct);
    }
}
#endif
