﻿namespace AT.Core.Domain.Entities;

using Primitives.Enums;

public class Permission
{
    public PermissionsEnum Type { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool Delegable { get; set; }

    public virtual ICollection<PermissionsGroup> PermissionsGroupPermissionPermissions { get; set; } =
        new List<PermissionsGroup>();

    public virtual ICollection<RoleDelegationType> RoleDelegationTypes { get; set; } = new List<RoleDelegationType>();

    public virtual ICollection<RoleDelegation> RoleDelegations { get; set; } = new List<RoleDelegation>();
}
