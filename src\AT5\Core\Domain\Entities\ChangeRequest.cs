namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class ChangeRequest
{
    public ChangeRequestId Id { get; set; }

    public RequestStatus Status { get; set; }

    public string? Description { get; set; }

    public DateTime Created { get; set; }

    public DateTime? Processed { get; set; }

    public UserId? ProcessedById { get; set; }

    public UserId? CreatedById { get; set; }

    public virtual ICollection<Change> Changes { get; set; } = new List<Change>();

    public virtual User? CreatedBy { get; set; }

    public virtual User? ProcessedBy { get; set; }

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<TradeOffer> TradeOffers { get; set; } = new List<TradeOffer>();
}
