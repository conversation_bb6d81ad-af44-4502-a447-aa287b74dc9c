﻿namespace AT.JobService;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AT.Core.Jobs.JobService;
using AT.Core.Jobs.JobService.ReloadLoop;
using AT.Core.Loops;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.DependencyInjection;
using AT.Utilities.Exceptions;
using AT.Utilities.Logging;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;

public class JobsWorker(
    ILogger<JobsWorker> _logger,
    IOptions<GeneralConfig> _configOptions,
    IFullOrgIdsQuery _fullOrgIdsQuery,
    IAppServiceProvider _appServiceProvider
) : BackgroundService
{
    private static readonly TimeSpan s_jobsReloadPeriod = TimeSpan.FromSeconds(10);

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            var organizationIdsToRun = await _fullOrgIdsQuery.GetOrgIdsAsync(stoppingToken);
            var config = _configOptions.Value;

            if (!config.IsMultitenant)
            {
                organizationIdsToRun = organizationIdsToRun.Where(x => x.DatabaseName == config.DbName).ToList();
                if (organizationIdsToRun.Count != 1)
                {
                    throw new InvalidConfigurationFileException(
                        $"Organization with the DatabaseName '{config.DbName}' not identified"
                            + $" in the master database (Count: {organizationIdsToRun.Count})."
                    );
                }
            }

            await RunProcessingForOrganizations(organizationIdsToRun, stoppingToken);

            _logger.Info("Cancellation requested. Terminating...");
        }
        catch (Exception e)
        {
            _logger.Error(e, $"{nameof(ExecuteAsync)} failed with an exception.");
        }
    }

    private async Task RunProcessingForOrganizations(IEnumerable<FullOrgId> organizationIds, CancellationToken ct)
    {
        List<Task> processingTasks = [];

        foreach (var organizationId in organizationIds.Select(x => x.Id))
        {
            var organizationResolver = _appServiceProvider.GetOrgServiceProvider(organizationId);

            var jobServiceBuilder = organizationResolver.Resolve<IJobServiceBuilder>();

            foreach (var specification in JobMapping.LegacyJobSpecifications.Concat(JobMapping.JobSpecifications))
            {
                jobServiceBuilder.AddJobSpecification(specification);
            }

            jobServiceBuilder.SetAristoTelosWebUrl(_configOptions.Value.AristoTelosWebUrl);

            var jobService = jobServiceBuilder.Create();

            var loopFactory = organizationResolver.Resolve<ILoopFactory>();
            var reloadLoop = loopFactory.CreateTimedLoop<JobReloadLoopIteration, JobReloadLoopParameters>(
                s_jobsReloadPeriod,
                new JobReloadLoopParameters(jobService)
            );

            var reloadTask = reloadLoop.StartAsync(ct);
            processingTasks.Add(reloadTask);

            var jobServiceTask = jobService.RunAsync(ct);
            processingTasks.Add(jobServiceTask);
        }

        await Task.WhenAll(processingTasks);
    }
}
