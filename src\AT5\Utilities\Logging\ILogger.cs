﻿namespace AT.Utilities.Logging;

using System.Runtime.CompilerServices;

/// Type for separation of normal and compiler generated arguments of logging.
public readonly struct ParamsStopType { }

/// <typeparam name="TCategoryName">The type whose name is used for the logger category name.</typeparam>
public interface ILogger<out TCategoryName>
{
    /// <summary>
    /// Logs that contain the most detailed messages. These messages may contain sensitive application data.
    /// These messages are disabled by default and should never be enabled in a production environment.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Trace(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that contain the most detailed messages. These messages may contain sensitive application data.
    /// These messages are disabled by default and should never be enabled in a production environment.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Trace("Processing request from {Address}", [address]);</example>
    void Trace(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that are used for interactive investigation during development. These logs should primarily contain
    /// information useful for debugging and have no long-term value.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Debug(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that are used for interactive investigation during development. These logs should primarily contain
    /// information useful for debugging and have no long-term value.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Debug("Processing request from {Address}", [address]);</example>
    void Debug(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that track the general flow of the application. These logs should have long-term value.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Info(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that track the general flow of the application. These logs should have long-term value.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Info("Processing request from {Address}", [address]);</example>
    void Info(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Warn(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Warn("Failed to process request from {Address}", [address]);</example>
    void Warn(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <param name="exception">The exception associated with the warning.</param>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Warn(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight an abnormal or unexpected event in the application flow, but do not otherwise cause the
    /// application execution to stop.
    /// </summary>
    /// <param name="exception">The exception associated with the warning.</param>
    /// <param name="message">An optional message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Warn(exception, "Failed to process request from {Address}", [address]);</example>
    void Warn(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Error(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Error("Failed to process request from {Address}", [address]);</example>
    void Error(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="message">An optional message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Error(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that highlight when the current flow of execution is stopped due to a failure. These should indicate a
    /// failure in the current activity, not an application-wide failure.
    /// </summary>
    /// <param name="exception">The exception to log, providing details about the failure.</param>
    /// <param name="message">An optional message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Error(exception, "Failed to process request from {Address}", [address]);</example>
    void Error(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Critical(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <param name="message">The message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Critical("Failed to process request from {Address}", [address]);</example>
    void Critical(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <param name="exception">The exception to log, providing details about the critical failure.</param>
    /// <param name="message">An optional message template to log.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    void Critical(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Logs that describe an unrecoverable application or system crash, or a catastrophic failure that requires
    /// immediate attention.
    /// </summary>
    /// <param name="exception">The exception to log, providing details about the critical failure.</param>
    /// <param name="message">An optional message template to log.</param>
    /// <param name="args">A span of arguments to format into the message template. This overload avoids allocations associated with params arrays.</param>
    /// <param name="stop">Parameter to delimit user-assigned and compiler-generated attributes. Never set this parameter.</param>
    /// <param name="memberName">The name of the method or property that invoked the logger. Automatically supplied by the compiler.</param>
    /// <param name="sourceFilePath">The full path of the source file that contains the caller. Automatically supplied by the compiler.</param>
    /// <param name="sourceLineNumber">The line number in the source file where the logging call was made. Automatically supplied by the compiler.</param>
    /// <example>Critical(exception, "Failed to process request from {Address}", [address]);</example>
    void Critical(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    );

    /// <summary>
    /// Creates a scope. Every call within the scope to other log methods (e.g., log.Info) automatically
    /// includes the <paramref name="properties"/> in the final log output.
    /// </summary>
    /// <param name="properties">An array of KVP value tuples for which the scope is created.</param>
    /// <returns>A disposable scope object.</returns>
    /// <example>
    /// using var _ = log.BeginScopeWithProperties(("Address", address));
    /// log.Info("Processing request...")
    /// </example>
    IDisposable? BeginScopeWithProperties(params (string Key, object Value)[] properties);
}
