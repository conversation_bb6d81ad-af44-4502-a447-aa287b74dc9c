﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class Roster
{
    public RosterId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public PlanningPeriodId? PlanningPeriodId { get; set; }

    public bool Deleted { get; set; }

    public DateTime Created { get; set; }

    public bool Locked { get; set; }

    public DateTime? LockTime { get; set; }

    public UserId CreatedById { get; set; }

    public UserId? LockedById { get; set; }

    public SiteId? SiteId { get; set; }

    public SiteRosterType SiteRosterType { get; set; }

    public virtual ICollection<Calculation> CalculationInputRosters { get; set; } = new List<Calculation>();

    public virtual ICollection<Calculation> CalculationOutputRosters { get; set; } = new List<Calculation>();

    public virtual User CreatedBy { get; set; } = null!;

    public virtual User? LockedBy { get; set; }

    public virtual PlanningPeriod? PlanningPeriod { get; set; }

    public virtual ICollection<PlanningPeriod> PlanningPeriodInputRosters { get; set; } = new List<PlanningPeriod>();

    public virtual ICollection<PlanningPeriod> PlanningPeriodPrepublicRosters { get; set; } =
        new List<PlanningPeriod>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual Site? Site { get; set; }
}
