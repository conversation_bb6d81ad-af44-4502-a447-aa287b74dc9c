﻿namespace AT.Core.Domain.Entities;

public class EmployeeBalance
{
    public EmployeeBalanceId Id { get; set; }

    public SiteId SiteId { get; set; }

    public UserId EmployeeId { get; set; }

    public DateInterval DateInterval { get; set; }

    public DateTime? FixedIntervalStart { get; set; }

    public UserId AuthorId { get; set; }

    public DateTime TimeStamp { get; set; }

    public int Balance { get; set; }

    public int Refund { get; set; }

    public int AdditionalTransferredBalanceCompensation { get; set; }

    public string FinalState { get; set; } = null!;

    public BalanceType BalanceType { get; set; }

    public int? PaidCompensatoryOvertime { get; set; }

    public int? InitialPaidOvertime { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
