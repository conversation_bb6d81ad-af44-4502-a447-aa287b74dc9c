﻿namespace AT.Shared.Formatting.Time.Formatters;

using System;
using AT.Translations.Formatting.Time;
using SmartFormat;

public sealed class TimeSpanFormatter
{
    public string Format(TimeSpan value, TimeSpanFormattingType type)
    {
        return type switch
        {
            TimeSpanFormattingType.Standard => FormatStandard(value),
            TimeSpanFormattingType.TotalHoursAndMinutes => FormatTotalHoursAndMinutes(value),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {type} is unknown"),
#else
            _ => FormatStandard(value)
#endif
        };
    }

    public string FormatStandard(TimeSpan value)
    {
        if (value.Seconds > 0 || value.Milliseconds > 0)
        {
            return value.ToString("c");
        }

        string dayPart = value.Days > 0 ? $"{value.Days}." : string.Empty;

        return $"{dayPart}{value.Hours:00}:{value.Minutes:00}";
    }

    public string FormatTotalHoursAndMinutes(TimeSpan value)
    {
        int hours = (int)value.TotalHours;
        int minutes = value.Minutes;

        return Smart.Format("{hours:00}:{minutes:00}", hours, minutes);
    }
}
