﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ output extension=".cs" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.Globalization" #>
#region Generated code from Language.tt template. In order to regenerate it, open, change and save the template file.
namespace AT.Primitives.Enums
{
	public enum Language : int
	{
		None = 0,
<#
		var i = 1;
		var nameSet = new HashSet<string>();
		foreach (var info in CultureInfo.GetCultures(CultureTypes.NeutralCultures))
		{
			var abbr = info.TwoLetterISOLanguageName.ToUpper();
			if (nameSet.Add(abbr))
			{
#>
		<#= abbr #> = <#= i #>,
<#
			}
			++i;
		}
#>
	}
}
#endregion