﻿namespace AT.Core.Domain.Entities;

public class Event
{
    public EventId Id { get; set; }

    public DateTimeInterval Period { get; set; }

    public EventType Type { get; set; }

    public double Value { get; set; }

    public bool ApplyToRealBins { get; set; }

    public string? Note { get; set; }

    public virtual ICollection<Prediction> Predictions { get; set; } = new List<Prediction>();

    public virtual ICollection<Queue> Queues { get; set; } = new List<Queue>();
}
