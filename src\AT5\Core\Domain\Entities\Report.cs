﻿namespace AT.Core.Domain.Entities;

using Base;

public class Report
{
    public ReportId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Type { get; set; } = null!;

    public string? MenuName { get; set; }

    public int MenuRank { get; set; }

    public string Title { get; set; } = null!;

    public string? Parameters { get; set; }

    public SqlReportDefinitionId? SqlReportDefinitionId { get; set; }

    public ReportCategoryId ReportCategoryId { get; set; }

    public string? Description { get; set; }

    public virtual ReportCategory ReportCategory { get; set; } = null!;

    public virtual ICollection<ReportColumn> ReportColumns { get; set; } = new List<ReportColumn>();

    public virtual ICollection<ReportDimension> ReportDimensions { get; set; } = new List<ReportDimension>();

    public virtual SqlReportDefinition? SqlReportDefinition { get; set; }

    public virtual ICollection<Role> Roles { get; set; } = new List<Role>();
}
