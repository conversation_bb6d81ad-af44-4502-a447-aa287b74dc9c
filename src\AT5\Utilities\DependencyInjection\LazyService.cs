﻿namespace AT.Utilities.DependencyInjection;

using Microsoft.Extensions.DependencyInjection;

/// <summary>
/// Using this service, we can inject Lazy<T> objects in the constructors without using AutoFac.
/// This is particularly useful when two classes inject each other in their constructors.
/// </summary>
public class LazyService<T>(IServiceProvider sp) : Lazy<T>(sp.GetRequiredService<T>)
    where T : notnull { }

public static class LazyServiceDependencyInjectionExtension
{
    public static IServiceCollection AddLazyServiceAsSingleton(this IServiceCollection services)
    {
        return services.AddSingleton(typeof(Lazy<>), typeof(LazyService<>));
    }
}
