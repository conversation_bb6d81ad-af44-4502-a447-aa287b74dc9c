﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class Property
{
    public PropertyId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Code { get; set; } = null!;

    public string? Description { get; set; }

    public string? DefaultValue { get; set; }

    public bool AllowEmployee { get; set; }

    public bool AllowSite { get; set; }

    public PropertyType Type { get; set; }

    public PropertyDataType DataType { get; set; }

    public string? Parameters { get; set; }

    public bool AllowRosterItem { get; set; }

    public bool IsGlobal { get; set; }

    public bool ImplicitAssignment { get; set; }

    public PropertyCategoryId? CategoryId { get; set; }

    public bool AllowRequest { get; set; }

    public virtual PropertyCategory? Category { get; set; }

    public virtual ICollection<EmployeeProperty> EmployeeProperties { get; set; } = new List<EmployeeProperty>();

    public virtual ICollection<PropertyFilter> PropertyFilters { get; set; } = new List<PropertyFilter>();

    public virtual ICollection<RequestProperty> RequestProperties { get; set; } = new List<RequestProperty>();

    public virtual ICollection<RosterItemProperty> RosterItemProperties { get; set; } = new List<RosterItemProperty>();

    public virtual ICollection<SiteProperty> SiteProperties { get; set; } = new List<SiteProperty>();

    public virtual ICollection<RequestType> RequestTypes { get; set; } = new List<RequestType>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
