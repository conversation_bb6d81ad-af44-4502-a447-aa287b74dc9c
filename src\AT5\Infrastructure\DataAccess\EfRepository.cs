﻿namespace AT.Infrastructure.DataAccess;

using Ardalis.Specification;
using Ardalis.Specification.EntityFrameworkCore;
using AT.Core.DataAccess;
using AT.Core.Domain.Base;
using AT.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Implementation mostly taken from: https://github.com/ardalis/Specification/blob/main/Specification.EntityFrameworkCore/src/Ardalis.Specification.EntityFrameworkCore/RepositoryBaseOfT.cs
/// Adjustments:
///     * The original implementation uses SaveChanges after every write operation.
/// </summary>
public class EfRepository<T>(OrganizationDbContext _dbContext, ISpecificationEvaluator _specificationEvaluator)
    : IRepository<T>,
        IRepositoryWithFactoryMethod<IRepository<T>>
    where T : class, IEntity, IAggregateRoot
{
    protected OrganizationDbContext DbContext => _dbContext;

    protected ISpecificationEvaluator SpecificationEvaluator => _specificationEvaluator;

    private bool _disposedValue;

    public EfRepository(OrganizationDbContext dbContext)
        : this(dbContext, Ardalis.Specification.EntityFrameworkCore.SpecificationEvaluator.Default) { }

    public static IRepository<T> Create(OrganizationDbContext dbContext)
    {
        return new EfRepository<T>(dbContext);
    }

    public virtual async Task<T?> GetByIdAsync<TId>(TId id, CancellationToken cancellationToken = default)
        where TId : notnull
    {
        return await DbContext.Set<T>().FindAsync([id], cancellationToken: cancellationToken);
    }

    public virtual async Task<T?> FirstOrDefaultAsync(
        ISpecification<T> specification,
        CancellationToken cancellationToken = default
    )
    {
        return await ApplySpecification(specification).FirstOrDefaultAsync(cancellationToken);
    }

    public virtual async Task<TResult?> FirstOrDefaultAsync<TResult>(
        ISpecification<T, TResult> specification,
        CancellationToken cancellationToken = default
    )
    {
        return await ApplySpecification(specification).FirstOrDefaultAsync(cancellationToken);
    }

    public virtual async Task<T?> SingleOrDefaultAsync(
        ISingleResultSpecification<T> specification,
        CancellationToken cancellationToken = default
    )
    {
        return await ApplySpecification(specification).SingleOrDefaultAsync(cancellationToken);
    }

    public virtual async Task<TResult?> SingleOrDefaultAsync<TResult>(
        ISingleResultSpecification<T, TResult> specification,
        CancellationToken cancellationToken = default
    )
    {
        return await ApplySpecification(specification).SingleOrDefaultAsync(cancellationToken);
    }

    public virtual async Task<List<T>> ListAsync(CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<T>().ToListAsync(cancellationToken);
    }

    public virtual async Task<List<T>> ListAsync(
        ISpecification<T> specification,
        CancellationToken cancellationToken = default
    )
    {
        var queryResult = await ApplySpecification(specification).ToListAsync(cancellationToken);

        return specification.PostProcessingAction == null
            ? queryResult
            : specification.PostProcessingAction(queryResult).ToList();
    }

    public virtual async Task<List<TResult>> ListAsync<TResult>(
        ISpecification<T, TResult> specification,
        CancellationToken cancellationToken = default
    )
    {
        var queryResult = await ApplySpecification(specification).ToListAsync(cancellationToken);

        return specification.PostProcessingAction == null
            ? queryResult
            : specification.PostProcessingAction(queryResult).ToList();
    }

    public virtual async Task<int> CountAsync(
        ISpecification<T> specification,
        CancellationToken cancellationToken = default
    )
    {
        return await ApplySpecification(specification, true).CountAsync(cancellationToken);
    }

    public virtual async Task<int> CountAsync(CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<T>().CountAsync(cancellationToken);
    }

    public virtual async Task<bool> AnyAsync(
        ISpecification<T> specification,
        CancellationToken cancellationToken = default
    )
    {
        return await ApplySpecification(specification, true).AnyAsync(cancellationToken);
    }

    public virtual async Task<bool> AnyAsync(CancellationToken cancellationToken = default)
    {
        return await DbContext.Set<T>().AnyAsync(cancellationToken);
    }

    public virtual IAsyncEnumerable<T> AsAsyncEnumerable(ISpecification<T> specification)
    {
        return ApplySpecification(specification).AsAsyncEnumerable();
    }

    public virtual void AddForInsert(T entity)
    {
        DbContext.Set<T>().Add(entity);
    }

    public virtual void AddRangeForInsert(IEnumerable<T> entities)
    {
        DbContext.Set<T>().AddRange(entities);
    }

    public virtual void AddForUpdate(T entity)
    {
        DbContext.Set<T>().Update(entity);
    }

    public virtual void AddRangeForUpdate(IEnumerable<T> entities)
    {
        DbContext.Set<T>().UpdateRange(entities);
    }

    public virtual void AddForDelete(T entity)
    {
        DbContext.Set<T>().Remove(entity);
    }

    public virtual void AddRangeForDelete(IEnumerable<T> entities)
    {
        DbContext.Set<T>().RemoveRange(entities);
    }

    public virtual void Commit()
    {
        DbContext.SaveChanges();
    }

    public virtual Task CommitAsync(CancellationToken cancellationToken = default(CancellationToken))
    {
        return DbContext.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Filters the entities  of <typeparamref name="T"/>, to those that match the encapsulated query logic of the
    /// <paramref name="specification"/>.
    /// </summary>
    /// <param name="specification">The encapsulated query logic.</param>
    /// <returns>The filtered entities as an <see cref="IQueryable{T}"/>.</returns>
    protected virtual IQueryable<T> ApplySpecification(
        ISpecification<T> specification,
        bool evaluateCriteriaOnly = false
    )
    {
        return SpecificationEvaluator.GetQuery(DbContext.Set<T>().AsQueryable(), specification, evaluateCriteriaOnly);
    }

    /// <summary>
    /// Filters all entities of <typeparamref name="T" />, that matches the encapsulated query logic of the
    /// <paramref name="specification"/>, from the database.
    /// <para>
    /// Projects each entity into a new form, being <typeparamref name="TResult" />.
    /// </para>
    /// </summary>
    /// <typeparam name="TResult">The type of the value returned by the projection.</typeparam>
    /// <param name="specification">The encapsulated query logic.</param>
    /// <returns>The filtered projected entities as an <see cref="IQueryable{T}"/>.</returns>
    protected virtual IQueryable<TResult> ApplySpecification<TResult>(ISpecification<T, TResult> specification)
    {
        return SpecificationEvaluator.GetQuery(DbContext.Set<T>().AsQueryable(), specification);
    }

    public void Dispose()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                DbContext.Dispose();
            }

            _disposedValue = true;
        }
    }
}
