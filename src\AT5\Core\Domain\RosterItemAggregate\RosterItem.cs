﻿namespace AT.Core.Domain.RosterItemAggregate;

using AT.Core.Domain.SickNotesAggregate;
using AT.Core.Domain.UserAggregate;

public class RosterItem : IAggregateRoot
{
    public RosterItemId Id { get; set; }

    public RosterId RosterId { get; set; }

    public UserId EmployeeId { get; set; }

    public ShiftTemplateId? ShiftId { get; set; }

    public bool Deleted { get; set; }

    public DateTime Created { get; set; }

    public UserId CreatedById { get; set; }

    public LocationId? LocationId { get; set; }

    public SiteId? SiteId { get; set; }

    public RosterItemType Type { get; set; }

    public DateTimeInterval TotalInterval { get; set; }

    public ChangeRequestId? LastChangeRequestId { get; set; }

    public ChangeLogId? LastChangeLogId { get; set; }

    public SickNoteId? SickNoteId { get; set; }

    public virtual ICollection<Change> Changes { get; set; } = new List<Change>();

    public virtual User CreatedBy { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    public virtual ChangeLog? LastChangeLog { get; set; }

    public virtual ChangeRequest? LastChangeRequest { get; set; }

    public virtual Location? Location { get; set; }

    public virtual ICollection<RosterItemNote> RosterItemNotes { get; set; } = new List<RosterItemNote>();

    public virtual ICollection<ParallelPart> ParallelParts { get; set; } = new List<ParallelPart>();

    public virtual Roster Roster { get; set; } = null!;

    public virtual ICollection<RosterItemPart> RosterItemParts { get; set; } = new List<RosterItemPart>();

    public virtual ICollection<RosterItemProperty> RosterItemProperties { get; set; } = new List<RosterItemProperty>();

    public virtual ICollection<RosterItemSyncQueueEntry> RosterItemSyncQueueEntries { get; set; } =
        new List<RosterItemSyncQueueEntry>();

    public virtual ICollection<RosterItemSyncState> RosterItemSyncStates { get; set; } =
        new List<RosterItemSyncState>();

    public virtual ShiftTemplate? Shift { get; set; }

    public virtual Site? Site { get; set; }

    public virtual ICollection<TradeAnswer> TradeAnswers { get; set; } = new List<TradeAnswer>();

    public virtual ICollection<TradeOffer> TradeOfferRosterItems { get; set; } = new List<TradeOffer>();

    public virtual ICollection<TradeOffer> TradeOfferRosterItems2 { get; set; } = new List<TradeOffer>();

    public virtual ICollection<Tag> Tags { get; set; } = new List<Tag>();

    public virtual SickNote? SickNote { get; set; }
}
