﻿namespace AT.Infrastructure.Events.Events;

using AT.Primitives.Enums;

public class RosterItemTagsDataChangeEvent : DataChangeEvent
{
    public IReadOnlyCollection<int>? ChangedItemsIds { get; set; }

    public RosterItemTagsDataChangeEvent() { }

    public RosterItemTagsDataChangeEvent(IReadOnlyCollection<int>? changedItemsIds)
    {
        Type = DataChangeType.RosterItemTags;
        ChangedItemsIds = changedItemsIds;
    }
}
