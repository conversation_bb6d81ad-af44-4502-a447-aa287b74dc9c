﻿namespace AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using Primitives.Enums;

public class SentNotificationRecipient : IEntity
{
    public long Id { get; set; }

    public long SentNotificationId { get; set; }

    public SentNotificationIdentifierType IdentifierType { get; set; }

    public string Identifier { get; set; } = null!;

    public virtual SentNotification SentNotification { get; set; } = null!;

    public static SentNotificationRecipient CreateFromEmailMessageRecipient(EmailMessageRecipient emailMessageRecipient)
    {
        return new SentNotificationRecipient()
        {
            IdentifierType = SentNotificationIdentifierType.EmailAddress,
            Identifier = emailMessageRecipient.Address,
        };
    }
}
