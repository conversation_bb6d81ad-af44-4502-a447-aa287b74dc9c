﻿namespace AT.Core.Domain.Entities;

using Base;

public class PreparationWay
{
    public PreparationWayId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public string? Parameters { get; set; }

    public SiteId SiteId { get; set; }

    public virtual ICollection<PreparationWayBonusCoefficient> PreparationWayBonusCoefficients { get; set; } =
        new List<PreparationWayBonusCoefficient>();

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<WorkOrderItem> WorkOrderItems { get; set; } = new List<WorkOrderItem>();
}
