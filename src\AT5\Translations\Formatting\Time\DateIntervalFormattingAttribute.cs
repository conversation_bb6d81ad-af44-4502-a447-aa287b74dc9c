﻿namespace AT.Translations.Formatting.Time;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class DateIntervalFormattingAttribute(DateIntervalFormattingType type) : Attribute
{
    public DateIntervalFormattingType Type { get; init; } = type;
}

// FUTURE: This shouldn't be in the Translations project.
/// <summary>
/// Locale-specific type of <see cref="DataStructures.Time.DateInterval"/> formatting.
/// </summary>
public enum DateIntervalFormattingType
{
    /// <summary>
    /// E.g. "5. 5. 2000 - 5. 5. 2000".
    /// </summary>
    Standard,

    /// <summary>
    /// E.g. "5. 5. 2000" when Start == End. Otherwise the same as <see cref="Standard"/>.
    /// </summary>
    Short,
}
