﻿namespace AT.Infrastructure.DataAccess;

using AT.Infrastructure.Database;

/// <summary>
/// Infrastructure-level interface, repository implementations implement this interface.
/// Repository implementing this interface then can be created using generic factory <see cref="EfRepositoryFactory{TRepo, TRepoImpl}"/>
/// Do not use outside the Infrastructure project.
/// </summary>
/// <typeparam name="TRepo">Type of repository that is created.</typeparam>
public interface IRepositoryWithFactoryMethod<out TRepo>
    where TRepo : class, IDisposable
{
    static abstract TRepo Create(OrganizationDbContext dbContext);
}
