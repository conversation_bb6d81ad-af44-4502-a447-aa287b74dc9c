﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.ConfigurationParameterAggregate;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using AT.Primitives.Enums;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to configuration in the database.
/// </summary>
internal static class ConfigurationGroupModelBuilder
{
    public static void BuildConfigurationGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<BalanceConfiguration>(entity =>
        {
            entity.ToTable("BalanceConfigurations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Code).HasMaxLength(10);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Parameters).HasMaxLength(4000);
        });

        modelBuilder.Entity<DbUpgrade>(entity =>
        {
            entity.ToTable("DbUpgrades");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(512);
            entity.Property(e => e.UpdateEnd).IsStoredAsDateTime();
            entity.Property(e => e.UpdateStart).IsStoredAsDateTime();
        });

        modelBuilder.Entity<DbUpgradeLog>(entity =>
        {
            entity.ToTable("DbUpgradeLogs");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();
        });

        modelBuilder.Entity<DefaultConfigParameter>(entity =>
        {
            entity.ToTable("DefaultConfigParameters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<Filter2>(entity =>
        {
            entity.ToTable("Filter2");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<Licence>(entity =>
        {
            entity.ToTable("Licences");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<LicencePrice>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("LicencePrices");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Price).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.LicenceId).HasColumnOrder(iota);
            entity.HasIndex(e => e.LicenceId, "IX_FK_LicencePriceLicence");

            entity
                .HasOne(d => d.Licence)
                .WithMany(p => p.LicencePrices)
                .HasForeignKey(d => d.LicenceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_LicencePriceLicence");
        });

        modelBuilder.Entity<LicenceRule>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("LicenceRules");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.Property(e => e.LicenceId).HasColumnOrder(iota);
            entity.HasIndex(e => e.LicenceId, "IX_FK_LicenceRuleLicence");

            entity.Property(e => e.FilterId).HasColumnOrder(iota);
            entity.HasIndex(e => e.FilterId, "IX_FK_LicenceRuleFilter2");

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.Priority).HasColumnOrder(iota);
            entity.Property(e => e.ValidContractOnly).HasColumnOrder(iota);
            entity.Property(e => e.IsGlobal).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.LicenceRules)
                .HasForeignKey(d => d.FilterId)
                .HasConstraintName("FK_LicenceRuleFilter2");

            entity
                .HasOne(d => d.Licence)
                .WithMany(p => p.LicenceRules)
                .HasForeignKey(d => d.LicenceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_LicenceRuleLicence");

            entity
                .HasMany(d => d.Roles)
                .WithMany(p => p.LicenceRules)
                .UsingEntity<Dictionary<string, object>>(
                    "LicenceRuleRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("RolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_LicenceRuleRole_Role"),
                    l =>
                        l.HasOne<LicenceRule>()
                            .WithMany()
                            .HasForeignKey("LicenceRulesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_LicenceRuleRole_LicenceRule"),
                    j =>
                    {
                        j.HasKey("LicenceRulesId", "RolesId");
                        j.ToTable("LicenceRuleRole");
                        j.HasIndex(["RolesId"], "IX_FK_LicenceRuleRole_Role");
                        j.IndexerProperty<LicenceRuleId>("LicenceRulesId").HasColumnName("LicenceRules_Id");
                        j.IndexerProperty<RoleId>("RolesId").HasColumnName("Roles_Id");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.LicenceRules)
                .UsingEntity<Dictionary<string, object>>(
                    "LicenceRuleSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_LicenceRuleSite_Site"),
                    l =>
                        l.HasOne<LicenceRule>()
                            .WithMany()
                            .HasForeignKey("LicenceRulesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_LicenceRuleSite_LicenceRule"),
                    j =>
                    {
                        j.HasKey("LicenceRulesId", "SitesId");
                        j.ToTable("LicenceRuleSite");
                        j.HasIndex(["SitesId"], "IX_FK_LicenceRuleSite_Site");
                        j.IndexerProperty<LicenceRuleId>("LicenceRulesId").HasColumnName("LicenceRules_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.ToTable("Permissions");
            entity.HasKey(e => e.Type);

            entity.Property(e => e.Type).ValueGeneratedNever();
            entity.Property(e => e.Description).HasMaxLength(1024);
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<PermissionsGroup>(entity =>
        {
            entity.ToTable("PermissionsGroups");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RoleId, "IX_FK_RolePermissionsGroup");

            entity
                .HasOne(d => d.Role)
                .WithMany(p => p.PermissionsGroups)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RolePermissionsGroup");

            entity
                .HasMany(d => d.ExcludedRoles)
                .WithMany(p => p.ExcludedInPermissionGroups)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleExcludedRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("ExcludedRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleExcludedRoles_Role"),
                    l =>
                        l.HasOne<PermissionsGroup>()
                            .WithMany()
                            .HasForeignKey("ExcludedInPermissionGroupsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleExcludedRoles_PermissionsGroup"),
                    j =>
                    {
                        j.HasKey("ExcludedInPermissionGroupsId", "ExcludedRolesId");
                        j.ToTable("RoleExcludedRoles");
                        j.HasIndex(["ExcludedRolesId"], "IX_FK_RoleExcludedRoles_Role");
                        j.IndexerProperty<PermissionsGroupId>("ExcludedInPermissionGroupsId")
                            .HasColumnName("ExcludedInPermissionGroups_Id");
                        j.IndexerProperty<RoleId>("ExcludedRolesId").HasColumnName("ExcludedRoles_Id");
                    }
                );

            entity
                .HasMany(d => d.PermissionsTypes)
                .WithMany(p => p.PermissionsGroupPermissionPermissions)
                .UsingEntity<Dictionary<string, object>>(
                    "PermissionsGroupPermission",
                    r =>
                        r.HasOne<Permission>()
                            .WithMany()
                            .HasForeignKey("PermissionsType")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PermissionsGroupPermission_Permission"),
                    l =>
                        l.HasOne<PermissionsGroup>()
                            .WithMany()
                            .HasForeignKey("PermissionsGroupPermissionPermissionId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PermissionsGroupPermission_PermissionsGroup"),
                    j =>
                    {
                        j.HasKey("PermissionsGroupPermissionPermissionId", "PermissionsType");
                        j.ToTable("PermissionsGroupPermission");
                        j.HasIndex(["PermissionsType"], "IX_FK_PermissionsGroupPermission_Permission");
                        j.IndexerProperty<PermissionsGroupId>("PermissionsGroupPermissionPermissionId")
                            .HasColumnName("PermissionsGroupPermission_Permission_Id");
                        j.IndexerProperty<PermissionsEnum>("PermissionsType").HasColumnName("Permissions_Type");
                    }
                );

            entity
                .HasMany(d => d.SubordinateRoles)
                .WithMany(p => p.ManagerRoles)
                .UsingEntity<Dictionary<string, object>>(
                    "SubordinateRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("SubordinateRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SubordinateRoles_Role"),
                    l =>
                        l.HasOne<PermissionsGroup>()
                            .WithMany()
                            .HasForeignKey("ManagerRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SubordinateRoles_PermissionsGroup"),
                    j =>
                    {
                        j.HasKey("ManagerRolesId", "SubordinateRolesId");
                        j.ToTable("SubordinateRoles");
                        j.HasIndex(["SubordinateRolesId"], "IX_FK_SubordinateRoles_Role");
                        j.IndexerProperty<PermissionsGroupId>("ManagerRolesId").HasColumnName("ManagerRoles_Id");
                        j.IndexerProperty<RoleId>("SubordinateRolesId").HasColumnName("SubordinateRoles_Id");
                    }
                );
        });

        modelBuilder.Entity<PublicHoliday>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("PublicHolidays");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Date).HasColumnOrder(iota).IsStoredAsDateTime();
            entity.Property(e => e.IsGlobal).HasColumnOrder(iota);

            entity.HasDateTimeInterval(e => e.PaymentInterval, iota);

            entity.Property(e => e.Working).HasColumnOrder(iota);

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.PublicHolidays)
                .UsingEntity<Dictionary<string, object>>(
                    "PublicHolidaySite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PublicHolidaySite_Site"),
                    l =>
                        l.HasOne<PublicHoliday>()
                            .WithMany()
                            .HasForeignKey("PublicHolidaysId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PublicHolidaySite_PublicHoliday"),
                    j =>
                    {
                        j.HasKey("PublicHolidaysId", "SitesId");
                        j.ToTable("PublicHolidaySite");
                        j.HasIndex(["SitesId"], "IX_FK_PublicHolidaySite_Site");
                        j.IndexerProperty<PublicHolidayId>("PublicHolidaysId").HasColumnName("PublicHolidays_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("Roles");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.SingleUserId, "IX_FK_SingleUserRoles");

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Code).HasMaxLength(50);

            entity
                .HasOne(d => d.SingleUser)
                .WithMany(p => p.Roles)
                .HasForeignKey(d => d.SingleUserId)
                .HasConstraintName("FK_SingleUserRoles");

            entity
                .HasMany(d => d.ManagedRoles)
                .WithMany(p => p.ManagingRoles)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleRoleManagement",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("ManagedRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleRoleManagement_Role"),
                    l =>
                        l.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("ManagingRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleRoleManagement_Role1"),
                    j =>
                    {
                        j.HasKey("ManagedRolesId", "ManagingRolesId");
                        j.ToTable("RoleRoleManagement");
                        j.HasIndex(["ManagingRolesId"], "IX_FK_RoleRoleManagement_Role1");
                        j.IndexerProperty<RoleId>("ManagedRolesId").HasColumnName("ManagedRoles_Id");
                        j.IndexerProperty<RoleId>("ManagingRolesId").HasColumnName("ManagingRoles_Id");
                    }
                );

            entity
                .HasMany(d => d.ManagingRoles)
                .WithMany(p => p.ManagedRoles)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleRoleManagement",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("ManagingRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleRoleManagement_Role1"),
                    l =>
                        l.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("ManagedRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleRoleManagement_Role"),
                    j =>
                    {
                        j.HasKey("ManagedRolesId", "ManagingRolesId");
                        j.ToTable("RoleRoleManagement");
                        j.HasIndex(["ManagingRolesId"], "IX_FK_RoleRoleManagement_Role1");
                        j.IndexerProperty<RoleId>("ManagedRolesId").HasColumnName("ManagedRoles_Id");
                        j.IndexerProperty<RoleId>("ManagingRolesId").HasColumnName("ManagingRoles_Id");
                    }
                );
        });

        modelBuilder.Entity<RoleDelegation>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RoleDelegations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.Created).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Changed).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.ChangedByUserId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ChangedByUserId, "IX_FK_RoleDelegationChangedByUser");

            entity.Property(e => e.DelegatingEmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.DelegatingEmployeeId, "IX_FK_DelegatingRoleDelegation");

            entity.Property(e => e.RoleDelegationTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RoleDelegationTypeId, "IX_FK_RoleDelegationRoleDelegationType");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_RoleDelegationDelegatingEmployeeSite");

            entity.Property(e => e.IncludeSubsites).HasColumnOrder(iota);

            entity
                .HasOne(d => d.ChangedByUser)
                .WithMany()
                .HasForeignKey(d => d.ChangedByUserId)
                .HasConstraintName("FK_RoleDelegationChangedByUser");

            entity
                .HasOne(d => d.DelegatingEmployee)
                .WithMany(p => p.RoleDelegations)
                .HasForeignKey(d => d.DelegatingEmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_DelegatingRoleDelegation");

            entity
                .HasOne(d => d.RoleDelegationType)
                .WithMany(p => p.RoleDelegations)
                .HasForeignKey(d => d.RoleDelegationTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleDelegationRoleDelegationType");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.RoleDelegations)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleDelegationDelegatingEmployeeSite");

            entity
                .HasMany(d => d.DelegatedEmployees)
                .WithMany(p => p.DelegatedRoleDelegations)
                .UsingEntity<Dictionary<string, object>>(
                    "DelegatedRoleDelegationEmployee",
                    r =>
                        r.HasOne<Employee>()
                            .WithMany()
                            .HasForeignKey("DelegatedEmployeesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_DelegatedRoleDelegationEmployee_Employee"),
                    l =>
                        l.HasOne<RoleDelegation>()
                            .WithMany()
                            .HasForeignKey("DelegatedRoleDelegationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_DelegatedRoleDelegationEmployee_RoleDelegation"),
                    j =>
                    {
                        j.HasKey("DelegatedRoleDelegationsId", "DelegatedEmployeesId");
                        j.ToTable("DelegatedRoleDelegationEmployee");
                        j.HasIndex(["DelegatedEmployeesId"], "IX_FK_DelegatedRoleDelegationEmployee_Employee");
                        j.IndexerProperty<RoleDelegationId>("DelegatedRoleDelegationsId")
                            .HasColumnName("DelegatedRoleDelegations_Id");
                        j.IndexerProperty<UserId>("DelegatedEmployeesId").HasColumnName("DelegatedEmployees_Id");
                    }
                );

            entity
                .HasMany(d => d.Employees)
                .WithMany(p => p.ExplicitlyGovernedByRoleDelegations)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationEmployee",
                    r =>
                        r.HasOne<Employee>()
                            .WithMany()
                            .HasForeignKey("EmployeesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationEmployee_Employee"),
                    l =>
                        l.HasOne<RoleDelegation>()
                            .WithMany()
                            .HasForeignKey("ExplicitlyGovernedByRoleDelegationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationEmployee_RoleDelegation"),
                    j =>
                    {
                        j.HasKey("ExplicitlyGovernedByRoleDelegationsId", "EmployeesId");
                        j.ToTable("RoleDelegationEmployee");
                        j.HasIndex(["EmployeesId"], "IX_FK_RoleDelegationEmployee_Employee");
                        j.IndexerProperty<RoleDelegationId>("ExplicitlyGovernedByRoleDelegationsId")
                            .HasColumnName("ExplicitlyGovernedByRoleDelegations_Id");
                        j.IndexerProperty<UserId>("EmployeesId").HasColumnName("Employees_Id");
                    }
                );

            entity
                .HasMany(d => d.PermissionsTypes)
                .WithMany(p => p.RoleDelegations)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationPermission",
                    r =>
                        r.HasOne<Permission>()
                            .WithMany()
                            .HasForeignKey("PermissionsType")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationPermission_Permission"),
                    l =>
                        l.HasOne<RoleDelegation>()
                            .WithMany()
                            .HasForeignKey("RoleDelegationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationPermission_RoleDelegation"),
                    j =>
                    {
                        j.HasKey("RoleDelegationsId", "PermissionsType");
                        j.ToTable("RoleDelegationPermission");
                        j.HasIndex(["PermissionsType"], "IX_FK_RoleDelegationPermission_Permission");
                        j.IndexerProperty<RoleDelegationId>("RoleDelegationsId").HasColumnName("RoleDelegations_Id");
                        j.IndexerProperty<PermissionsEnum>("PermissionsType").HasColumnName("Permissions_Type");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.ExplicitlyGovernedByRoleDelegations)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationSite_Site"),
                    l =>
                        l.HasOne<RoleDelegation>()
                            .WithMany()
                            .HasForeignKey("ExplicitlyGovernedByRoleDelegationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationSite_RoleDelegation"),
                    j =>
                    {
                        j.HasKey("ExplicitlyGovernedByRoleDelegationsId", "SitesId");
                        j.ToTable("RoleDelegationSite");
                        j.HasIndex(["SitesId"], "IX_FK_RoleDelegationSite_Site");
                        j.IndexerProperty<RoleDelegationId>("ExplicitlyGovernedByRoleDelegationsId")
                            .HasColumnName("ExplicitlyGovernedByRoleDelegations_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );

            entity
                .HasMany(d => d.Teams)
                .WithMany(p => p.ExplicitlyGovernedByRoleDelegations)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationTeam",
                    r =>
                        r.HasOne<Team>()
                            .WithMany()
                            .HasForeignKey("TeamsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTeam_Team"),
                    l =>
                        l.HasOne<RoleDelegation>()
                            .WithMany()
                            .HasForeignKey("ExplicitlyGovernedByRoleDelegationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTeam_RoleDelegation"),
                    j =>
                    {
                        j.HasKey("ExplicitlyGovernedByRoleDelegationsId", "TeamsId");
                        j.ToTable("RoleDelegationTeam");
                        j.HasIndex(["TeamsId"], "IX_FK_RoleDelegationTeam_Team");
                        j.IndexerProperty<RoleDelegationId>("ExplicitlyGovernedByRoleDelegationsId")
                            .HasColumnName("ExplicitlyGovernedByRoleDelegations_Id");
                        j.IndexerProperty<TeamId>("TeamsId").HasColumnName("Teams_Id");
                    }
                );
        });

        modelBuilder.Entity<RoleDelegationType>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RoleDelegationTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.DelegateOrganizationUnits).HasColumnOrder(iota);
            entity.Property(e => e.OrganizationStructureLevelsDown).HasColumnOrder(iota);
            entity.Property(e => e.OrganizationStructureLevelsUp).HasColumnOrder(iota);
            entity.Property(e => e.MaxDays).HasColumnOrder(iota);
            entity.Property(e => e.MaxDaysBeforeStart).HasColumnOrder(iota);

            entity
                .HasMany(d => d.DelegateRoles)
                .WithMany(p => p.DelegateRoleDelegationTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationTypeDelegateRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("DelegateRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypeDelegateRole_Role"),
                    l =>
                        l.HasOne<RoleDelegationType>()
                            .WithMany()
                            .HasForeignKey("DelegateRoleDelegationTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypeDelegateRole_RoleDelegationType"),
                    j =>
                    {
                        j.HasKey("DelegateRoleDelegationTypesId", "DelegateRolesId");
                        j.ToTable("RoleDelegationTypeDelegateRole");
                        j.HasIndex(["DelegateRolesId"], "IX_FK_RoleDelegationTypeDelegateRole_Role");
                        j.IndexerProperty<RoleDelegationTypeId>("DelegateRoleDelegationTypesId")
                            .HasColumnName("DelegateRoleDelegationTypes_Id");
                        j.IndexerProperty<RoleId>("DelegateRolesId").HasColumnName("DelegateRoles_Id");
                    }
                );

            entity
                .HasMany(d => d.ExcludedSubordinateRoles)
                .WithMany(p => p.ExcludedInRoleDelegationTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationTypeExcludedSubordinateRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("ExcludedSubordinateRolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypeExcludedSubordinateRoles_Role"),
                    l =>
                        l.HasOne<RoleDelegationType>()
                            .WithMany()
                            .HasForeignKey("ExcludedInRoleDelegationTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypeExcludedSubordinateRoles_RoleDelegationType"),
                    j =>
                    {
                        j.HasKey("ExcludedInRoleDelegationTypesId", "ExcludedSubordinateRolesId");
                        j.ToTable("RoleDelegationTypeExcludedSubordinateRoles");
                        j.HasIndex(
                            ["ExcludedSubordinateRolesId"],
                            "IX_FK_RoleDelegationTypeExcludedSubordinateRoles_Role"
                        );
                        j.IndexerProperty<RoleDelegationTypeId>("ExcludedInRoleDelegationTypesId")
                            .HasColumnName("ExcludedInRoleDelegationTypes_Id");
                        j.IndexerProperty<RoleId>("ExcludedSubordinateRolesId")
                            .HasColumnName("ExcludedSubordinateRoles_Id");
                    }
                );

            entity
                .HasMany(d => d.PermissionsTypes)
                .WithMany(p => p.RoleDelegationTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationTypePermission",
                    r =>
                        r.HasOne<Permission>()
                            .WithMany()
                            .HasForeignKey("PermissionsType")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypePermission_Permission"),
                    l =>
                        l.HasOne<RoleDelegationType>()
                            .WithMany()
                            .HasForeignKey("RoleDelegationTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypePermission_RoleDelegationType"),
                    j =>
                    {
                        j.HasKey("RoleDelegationTypesId", "PermissionsType");
                        j.ToTable("RoleDelegationTypePermission");
                        j.HasIndex(["PermissionsType"], "IX_FK_RoleDelegationTypePermission_Permission");
                        j.IndexerProperty<RoleDelegationTypeId>("RoleDelegationTypesId")
                            .HasColumnName("RoleDelegationTypes_Id");
                        j.IndexerProperty<PermissionsEnum>("PermissionsType").HasColumnName("Permissions_Type");
                    }
                );

            entity
                .HasMany(d => d.Roles)
                .WithMany(p => p.RoleDelegationTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RoleDelegationTypeRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("RolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypeRole_Role"),
                    l =>
                        l.HasOne<RoleDelegationType>()
                            .WithMany()
                            .HasForeignKey("RoleDelegationTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoleDelegationTypeRole_RoleDelegationType"),
                    j =>
                    {
                        j.HasKey("RoleDelegationTypesId", "RolesId");
                        j.ToTable("RoleDelegationTypeRole");
                        j.HasIndex(["RolesId"], "IX_FK_RoleDelegationTypeRole_Role");
                        j.IndexerProperty<RoleDelegationTypeId>("RoleDelegationTypesId")
                            .HasColumnName("RoleDelegationTypes_Id");
                        j.IndexerProperty<RoleId>("RolesId").HasColumnName("Roles_Id");
                    }
                );
        });

        modelBuilder.Entity<SitesConfigParameter>(entity =>
        {
            entity.ToTable("SitesConfigParameters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.DefaultConfigParameterId, "IX_FK_DefaultConfigParameterSiteConfigParameter");

            entity.HasIndex(e => e.SiteId, "IX_FK_SiteConfigParameters");

            entity
                .HasOne(d => d.DefaultConfigParameter)
                .WithMany(p => p.SitesConfigParameters)
                .HasForeignKey(d => d.DefaultConfigParameterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_DefaultConfigParameterSiteConfigParameter");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.SitesConfigParameters)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteConfigParameters");
        });

        modelBuilder.Entity<Translation>(entity =>
        {
            entity.ToTable("Translations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.TranslationId).HasMaxLength(1024);
        });

        modelBuilder.Entity<TranslationText>(entity =>
        {
            entity.ToTable("TranslationTexts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.TranslationId, "IX_FK_TranslationTranslationText");

            entity.Property(e => e.Text).HasMaxLength(1024);

            entity
                .HasOne(d => d.Translation)
                .WithMany(p => p.TranslationTexts)
                .HasForeignKey(d => d.TranslationId)
                .HasConstraintName("FK_TranslationTranslationText");
        });
    }
}
