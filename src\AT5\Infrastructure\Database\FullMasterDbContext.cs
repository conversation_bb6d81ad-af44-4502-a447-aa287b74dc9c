﻿namespace AT.Infrastructure.Database;

using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// This is a variant of <see cref="MasterDbContext"/> that is used in tests.
/// Eventually it could contain things that we don't want on normal db context but need for full db creation.
/// </summary>
public class FullMasterDbContext(DbContextOptions<MasterDbContext> _options)
    : MasterDbContext(_options),
        IFullDbContext<MasterDbContext>
{
    public IEnumerable<FormattableString> GetAdditionalIndexes()
    {
        return [];
    }
}
