namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class TimeOffType
{
    public TimeOffShiftInteraction ShiftInteraction { get; set; }

    public RosterItemPartTypeId Id { get; set; }

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;

    public virtual ICollection<RequestTypeTimeOff> TimeOffs { get; set; } = new List<RequestTypeTimeOff>();
}
