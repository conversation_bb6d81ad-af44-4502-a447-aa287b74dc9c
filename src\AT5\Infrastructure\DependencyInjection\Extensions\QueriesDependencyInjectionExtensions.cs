﻿namespace AT.Infrastructure.DependencyInjection.Extensions;

using AT.Core.DataAccess;
using AT.Core.Domain.ConfigurationParameterAggregate.Queries;
using AT.Core.Domain.JobsAggregate.Queries;
using AT.Core.Domain.PushNotificationTokenAggregate.Queries;
using AT.Core.Domain.UserAggregate.Queries;
using AT.Infrastructure.DataAccess;
using AT.Infrastructure.Queries;
using Autofac;

public static class QueriesDependencyInjectionExtensions
{
    public static ContainerBuilder AddQueries(this ContainerBuilder containerBuilder)
    {
        containerBuilder.RegisterGeneric(typeof(EfDataSource<>)).As(typeof(IDataSource<>)).InstancePerLifetimeScope(); // Shall only be used from infrastructure.

        // Queries.
        containerBuilder
            .RegisterType<GlobalConfigParamQuery>()
            .As<IGlobalConfigParamQuery>()
            .InstancePerLifetimeScope();
        containerBuilder.RegisterType<BasicUserInfoQuery>().As<IBasicUserInfoQuery>().InstancePerLifetimeScope();
        containerBuilder
            .RegisterType<PushNotificationTokenForUsersQuery>()
            .As<IPushNotificationTokenForUsersQuery>()
            .InstancePerLifetimeScope();

        containerBuilder.RegisterType<JobQuery>().As<IJobQuery>().InstancePerLifetimeScope();

        // General queries
        containerBuilder
            .RegisterGeneric(typeof(EfGeneralQuery<>))
            .As(typeof(IGeneralQuery<>))
            .InstancePerLifetimeScope(); // Only for scripting purposes - testing, command line utilities.

        return containerBuilder;
    }
}
