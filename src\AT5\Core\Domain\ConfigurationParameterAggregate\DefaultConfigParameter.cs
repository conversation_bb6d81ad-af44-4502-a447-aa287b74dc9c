﻿namespace AT.Core.Domain.ConfigurationParameterAggregate;

using Base;

public class DefaultConfigParameter : IEntity
{
    public DefaultConfigParameterId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? DefaultValue { get; set; }

    public virtual ICollection<SitesConfigParameter> SitesConfigParameters { get; set; } =
        new List<SitesConfigParameter>();
}
