﻿namespace AT.Core.Domain.ApprovalsAggregate;

using AT.Core.Domain.UserAggregate;

public class ApprovalHistory : IEntity
{
    public ApprovalHistoryId Id { get; set; }

    public ApprovalId ApprovalId { get; set; }

    public RequestStatus Status { get; set; }

    public string? Note { get; set; } = null;

    public DateTime Changed { get; set; }

    public UserId? ChangedById { get; set; }

    public UserId EmployeeId { get; set; }

    public virtual Approval Approval { get; set; } = null!;

    public virtual User? ChangedBy { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();
}
