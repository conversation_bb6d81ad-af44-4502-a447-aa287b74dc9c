﻿namespace AT.Translations.Formatting.Time;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class DateTimeFormattingAttribute(DateTimeFormattingType type) : Attribute
{
    public DateTimeFormattingType Type { get; init; } = type;
}

// FUTURE: This shouldn't be in the Translations project.
/// <summary>
/// Locale-specific type of <see cref="DateTime"/> formatting.
/// </summary>
public enum DateTimeFormattingType
{
    /// <summary>
    /// E.g. "25. 7. 2025 14:25:30" or "7/25/2025 02:25:30 PM".
    /// </summary>
    Standard,

    /// <summary>
    /// E.g. "25. 7. 2025 14:25" or "7/25/2025 2:25 PM".
    /// </summary>
    Short,
}
