﻿namespace AT.Utilities.Collections;

using System.Diagnostics.CodeAnalysis;

public static class CollectionUtils
{
    /// <returns>
    /// First element of the enumerable (or default) and information whther there are more items.
    /// </returns>
    public static (T? firstElement, bool more) FirstOrDefaultAndMore<T>(this IEnumerable<T> items)
    {
        using var enumerator = items.GetEnumerator();

        if (!enumerator.MoveNext())
        {
            return (default(T), false);
        }

        var firstValue = enumerator.Current;
        bool moreValues = enumerator.MoveNext();

        return (firstValue, moreValues);
    }

    public static IEnumerable<T> Yield<T>(this T? item)
    {
        return item is null ? [] : Enumerable.Repeat(item, 1);
    }

    public static IEnumerable<(T Item, int Index)> WithIndex<T>(this IEnumerable<T> items)
    {
        return items.Select((item, index) => (item, index));
    }

    public static void ForEach<T>(this IEnumerable<T> items, Action<T> action)
    {
        foreach (var item in items)
        {
            action(item);
        }
    }

    public static void ForEach<T>(this IEnumerable<T> items, Action<T, int> action)
    {
        foreach (var (item, index) in items.WithIndex())
        {
            action(item, index);
        }
    }

    public static string ToJoinedString<T>(this IEnumerable<T> items, string separator = ", ")
    {
        return string.Join(separator, items);
    }

    [return: NotNullIfNotNull(nameof(defaultValue))]
    public static T? GetValueOr<TKey, T>(
        this IReadOnlyDictionary<TKey, T> dictionary,
        TKey? key,
        T? defaultValue = default(T)
    )
    {
        if (key is not null && dictionary.TryGetValue(key, out var value))
        {
            return value;
        }

        return defaultValue;
    }

    /// <summary>
    /// Semantic shortcut for <code>!enumerable.Any(pred)</code>.
    /// </summary>
    public static bool None<T>(this IEnumerable<T> items, Func<T, bool> predicate)
    {
        return !items.Any(predicate);
    }

    /// <summary>
    /// Semantic shortcut for <code>!enumerable.Any()</code>.
    /// </summary>
    public static bool IsEmpty<T>(this IEnumerable<T> items)
    {
        return !items.Any();
    }

    public static bool IsNullOrEmpty<T>([NotNullWhen(false)] this IEnumerable<T>? items)
    {
        if (items == null)
        {
            return true;
        }

        return !items.Any();
    }

    /// <summary>
    /// E.g., [1, 2, 2, 3, 4, 5] with listSize 2 would yield [ [1, 2], [3, 4], [5] ].
    /// </summary>
    public static List<List<T>> SplitToListsOfUniqueElements<T>(this IEnumerable<T> entities, int listSize)
    {
        var processed = new HashSet<T>();
        var result = new List<List<T>>();
        var currentList = new List<T>();

        foreach (var entity in entities)
        {
            // If entity has already been added, do nothing
            if (!processed.Add(entity))
            {
                continue;
            }

            if (currentList.Count == listSize)
            {
                result.Add(currentList);
                currentList = [];
            }

            currentList.Add(entity);
        }

        // Add remaining elements
        if (currentList.Count > 0)
        {
            result.Add(currentList);
        }

        return result;
    }

    public static CollectionSplitResult<T> SplitInTwo<T>(this IEnumerable<T> sequence, Func<T, bool> featurePredicate)
    {
        var hasFeature = new List<T>();
        var doesntHaveFeature = new List<T>();

        foreach (var item in sequence)
        {
            if (featurePredicate(item))
            {
                hasFeature.Add(item);
            }
            else
            {
                doesntHaveFeature.Add(item);
            }
        }

        return new CollectionSplitResult<T>(hasFeature, doesntHaveFeature);
    }

    public static CollectionDiffResult<TElement> CompareCollections<TElement, TKey>(
        IEnumerable<TElement> originalCollection,
        IEnumerable<TElement> updatedCollection,
        Func<TElement, TKey> uniqueKeySelector,
        Func<TElement, TElement, bool> comparer
    )
        where TKey : notnull
    {
        var removed = new List<TElement>();
        var added = new List<TElement>();
        var changed = new List<(TElement From, TElement To)>();

        var originalById = originalCollection.ToDictionary(x => uniqueKeySelector(x));
        var updatedById = updatedCollection.ToDictionary(x => uniqueKeySelector(x));

        foreach (var originalElement in originalCollection.Where(x => !updatedById.ContainsKey(uniqueKeySelector(x))))
        {
            removed.Add(originalElement);
        }

        foreach (var updatedElement in updatedCollection)
        {
            if (!originalById.TryGetValue(uniqueKeySelector(updatedElement), out var originalElement))
            {
                added.Add(updatedElement);
                continue;
            }

            if (!comparer(originalElement, updatedElement))
            {
                changed.Add((originalElement, updatedElement));
            }
        }

        return new CollectionDiffResult<TElement>(removed, added, changed);
    }
}
