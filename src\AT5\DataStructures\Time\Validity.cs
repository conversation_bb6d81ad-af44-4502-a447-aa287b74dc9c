﻿namespace AT.DataStructures.Time;

using System.Runtime.Serialization;

/// <summary>
/// Represents a validity-aware wrapper for an <see cref="OpenDateInterval"/>.
/// This struct stores an interval and tracks whether it is considered valid.
/// When marked as invalid, the original interval is preserved for user interaction purposes,
/// allowing it to be remembered after being set to invalid.
///
/// <para>
/// This type should only be used in contexts involving user interaction where preserving
/// the stored interval is necessary. In service layers or business logic, it should be
/// converted to <see cref="OpenDateInterval"/> using the implicit conversion or the
/// <see cref="ToOpenDateInterval"/> method.
/// </para>
/// </summary>
[DataContract]
public readonly record struct Validity
{
    /// <summary>
    /// Gets the stored interval represented by this instance.
    /// </summary>
    [DataMember]
    public OpenDateInterval Interval { get; }

    /// <summary>
    /// Gets a value indicating whether the interval is marked as invalid.
    /// </summary>
    [DataMember]
    public bool IsInvalid { get; }

    /// <summary>
    /// Gets a value indicating whether the interval is considered valid.
    /// </summary>
    public bool IsValid => !IsInvalid;

    /// <summary>
    /// Gets the start date of the interval.
    /// </summary>
    public DateOnly? Start => Interval.Start;

    /// <summary>
    /// Gets the end date of the interval.
    /// </summary>
    public DateOnly? End => Interval.End;

    /// <summary>
    /// Initializes a new valid instance of the <see cref="Validity"/> struct with an empty interval.
    /// </summary>
    public Validity()
        : this(default(DateOnly?), default(DateOnly?)) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Validity"/> struct with the specified start
    /// and end dates and an optional validity state.
    /// </summary>
    /// <param name="start">The start date of the interval.</param>
    /// <param name="end">The end date of the interval.</param>
    /// <param name="isInvalid">A value indicating whether the interval should be marked as invalid. Defaults to <c>false</c>.</param>
    public Validity(DateOnly? start, DateOnly? end, bool isInvalid = false)
        : this(new OpenDateInterval(start, end), isInvalid: isInvalid) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Validity"/> struct with the specified interval
    /// and an optional validity state.
    /// </summary>
    /// <param name="interval">The interval to wrap in the <see cref="Validity"/> struct.</param>
    /// <param name="isInvalid">A value indicating whether the interval should be marked as invalid. Defaults to <c>false</c>.</param>
    public Validity(OpenDateInterval interval, bool isInvalid = false)
    {
        Interval = interval;
        IsInvalid = isInvalid;
    }

    /// <summary>
    /// Implicitly converts a <see cref="Validity"/> instance to an <see cref="OpenDateInterval"/> using <see cref="ToOpenDateInterval"/>.
    /// If the interval is invalid, an empty <see cref="OpenDateInterval"/> is returned.
    /// </summary>
    /// <param name="validity">The <see cref="Validity"/> instance to convert.</param>
    public static implicit operator OpenDateInterval(Validity validity)
    {
        return validity.ToOpenDateInterval();
    }

    /// <summary>
    /// Converts the current instance to an <see cref="OpenDateInterval"/>.
    /// If the interval is invalid, an empty <see cref="OpenDateInterval"/> is returned.
    /// </summary>
    /// <returns>The stored interval if valid; otherwise, an empty <see cref="OpenDateInterval"/>.</returns>
    public OpenDateInterval ToOpenDateInterval()
    {
        if (IsInvalid)
        {
            return new OpenDateInterval();
        }

        return Interval;
    }
}
