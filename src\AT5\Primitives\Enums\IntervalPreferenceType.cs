﻿namespace AT.Primitives.Enums;

/// <summary>
/// For an interval that defines some preference (i.e. employee wants to
/// have his shifts between 9am and 4pm), this defines when the
/// preference is met for some other given interval.
/// </summary>
public enum IntervalPreferenceType
{
    /// <summary>
    /// The whole given interval must be inside the preferred interval.
    /// </summary>
    SubInterval = 0,

    /// <summary>
    /// Start of the given interval must be inside the preferred interval.
    /// </summary>
    StartOnly = 1,

    /// <summary>
    /// The shift length is in the selected interval
    /// </summary>
    ShiftLength = 2,

    /// <summary>
    /// Employee can/cannot work in the selected interval
    /// </summary>
    CanCannot = 3,
}
