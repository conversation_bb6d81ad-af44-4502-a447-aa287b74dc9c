﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System.Linq.Expressions;

public interface ITypedAdditionalIndexBuilder<T> : IAdditionalIndexBuilder
    where T : class
{
    ITypedAdditionalIndexBuilder<T> IncludeProperties(Expression<Func<T, object?>> includeExpression);

    ITypedAdditionalIndexBuilder<T> IncludeProperties(IEnumerable<string> columnNames);

    ITypedAdditionalIndexBuilder<T> WithSettings(object settings);
}
