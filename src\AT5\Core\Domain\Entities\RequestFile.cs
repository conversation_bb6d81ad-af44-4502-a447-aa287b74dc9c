﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class RequestFile
{
    public RequestFileId Id { get; set; }

    public RequestPropertyId? RequestPropertyId { get; set; }

    public string Name { get; set; } = null!;

    public FileType Type { get; set; }

    public byte[] Content { get; set; } = null!;

    public UserId AuthorId { get; set; }

    public DateTime Created { get; set; }

    public virtual User Author { get; set; } = null!;

    public virtual RequestProperty? RequestProperty { get; set; }
}
