﻿namespace AT.Core.Domain.Entities;

public class Adherence
{
    public AdherenceId Id { get; set; }

    public RosterItemPartTypeId? RosterItemPartTypeId { get; set; }

    public StatusTypeId? StatusTypeId { get; set; }

    public AdherenceStatusId AdherenceStatusId { get; set; }

    public TimeInterval LowerLimit { get; set; }

    public TimeInterval UpperLimit { get; set; }

    public virtual AdherenceStatus AdherenceStatus { get; set; } = null!;

    public virtual RosterItemPartType? RosterItemPartType { get; set; }

    public virtual StatusType? StatusType { get; set; }
}
