﻿namespace AT.UseCases.Jobs;

using System.Threading.Tasks;
using AT.Core.Jobs.JobService.JobWrapper;
using AT.Primitives.Enums;

public abstract class ATJobBase<TParams> : IATJob<TParams>
{
    public abstract Task<IATJobResult> Execute(IATJobContext context, TParams parameters);

    public Task<IATJobResult> Execute(IATJobContext context, object parameters)
    {
        return Execute(context, (TParams)parameters);
    }

    protected static IATJobResult MakeResult(JobResultType resultType)
    {
        return new ATJobResult(resultType);
    }
}
