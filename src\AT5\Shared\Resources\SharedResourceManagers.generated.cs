namespace AT.Shared.Resources;

using System.CodeDom.Compiler;
using System.Resources;
using AT.Translations;

[GeneratedCode("AT.Translations.Generator.ResxTranslationsGenerator.cs", "1.0.0.0")]
public static class SharedResourceManagers
{
    public static ResourceManagerWrapper[] ResourceManagers = new ResourceManagerWrapper[]
    {
        new ResourceManagerWrapper(new ResourceManager("AT.Shared.Resources.TimeFormat.TimeFormat", typeof(SharedAssembly).Assembly), new ResxLocation(AssemblyName: "Shared", ResxRelativeNamespace: "TimeFormat.TimeFormat")),
        new ResourceManagerWrapper(new ResourceManager("AT.Shared.Resources.NumberFormat.NumberFormat", typeof(SharedAssembly).Assembly), new ResxLocation(AssemblyName: "Shared", ResxRelativeNamespace: "NumberFormat.NumberFormat"))
    };
}