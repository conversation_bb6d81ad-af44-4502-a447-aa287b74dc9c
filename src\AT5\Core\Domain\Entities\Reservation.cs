﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class Reservation
{
    public ReservationId Id { get; set; }

    public DateOnly Date { get; set; }

    public RequirementId RequirementId { get; set; }

    public UserId EmployeeId { get; set; }

    public RequestStatus Status { get; set; }

    public DateTime? Processed { get; set; }

    public UserId? ProcessedById { get; set; }

    public DateTime Created { get; set; }

    public UserId CreatedById { get; set; }

    public virtual User CreatedBy { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    public virtual ICollection<ReservationPart> ReservationParts { get; set; } = new List<ReservationPart>();

    public virtual User? ProcessedBy { get; set; }

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual Requirement Requirement { get; set; } = null!;
}
