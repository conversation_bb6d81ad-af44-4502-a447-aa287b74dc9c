﻿namespace AT.Infrastructure.Utilities.AzureMonitor;

public class AzureMonitorOptions
{
    public const string AzureMonitor = nameof(AzureMonitor);

    public string ConnectionString { get; set; } = string.Empty;

    public string ServiceName { get; set; } = string.Empty;

    public string ServiceInstanceId { get; set; } = string.Empty;

    public float SamplingRatio { get; set; } = 0.05F;

    public int HealthChecksInterval { get; set; } = 30;

    public bool DoNotSampleRequestsWithErrors { get; set; } = true;
}
