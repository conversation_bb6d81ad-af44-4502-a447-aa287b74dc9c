﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class AdditionalIndexBuilder<T>(string _indexName, EntityTypeBuilder<T> _entityTypeBuilder)
    : ITypedAdditionalIndexBuilder<T>
    where T : class
{
    // The columns are stored as factories because they have to be lazily evaluated only when Build is called.
    // This allows to configure the entities in any order.
    private readonly List<IColumnNamesFactory<T>> _keyColumnsFactories = [];
    private readonly List<IColumnNamesFactory<T>> _nonkeyColumnsFactories = [];
    private readonly List<string> _settings = [];

    public string Build()
    {
        var tableName = _entityTypeBuilder.Metadata.GetTableName();
        if (tableName is null)
        {
            throw new InvalidOperationException(
                $"Index {_indexName} cannot be built. Table name is not specified in the metadata."
            );
        }

        var keyColumns = _keyColumnsFactories.SelectMany(x => x.MakeColumnNames(_entityTypeBuilder)).ToArray();
        var keyColumnsMerged = string.Join(", ", keyColumns.Select(x => $"[{x}]"));
        var index = $"CREATE NONCLUSTERED INDEX [{_indexName}] ON [dbo].[{tableName}] ({keyColumnsMerged})";

        var nonkeyColumns = _nonkeyColumnsFactories.SelectMany(x => x.MakeColumnNames(_entityTypeBuilder)).ToArray();
        if (nonkeyColumns.Length > 0)
        {
            var nonkeyColumnsMerged = string.Join(", ", nonkeyColumns.Select(x => $"[{x}]"));
            var includeClause = $"INCLUDE ({nonkeyColumnsMerged})";
            index = $"{index} {includeClause}";
        }

        if (_settings.Count > 0)
        {
            var settingsMerged = string.Join(", ", _settings);
            var settingsClause = $"WITH ({settingsMerged})";
            index = $"{index} {settingsClause}";
        }

        return index;
    }

    public ITypedAdditionalIndexBuilder<T> HasKeyColumns(Expression<Func<T, object?>> keyColumnsExpression)
    {
        _keyColumnsFactories.Add(new ExpressionColumnNamesFactory<T>(keyColumnsExpression));
        return this;
    }

    public ITypedAdditionalIndexBuilder<T> HasKeyColumns(IEnumerable<string> columnNames)
    {
        _keyColumnsFactories.Add(new SimpleColumnNamesFactory<T>(columnNames));
        return this;
    }

    public ITypedAdditionalIndexBuilder<T> IncludeProperties(Expression<Func<T, object?>> includeExpression)
    {
        _nonkeyColumnsFactories.Add(new ExpressionColumnNamesFactory<T>(includeExpression));
        return this;
    }

    public ITypedAdditionalIndexBuilder<T> IncludeProperties(IEnumerable<string> columnNames)
    {
        _nonkeyColumnsFactories.Add(new SimpleColumnNamesFactory<T>(columnNames));
        return this;
    }

    public ITypedAdditionalIndexBuilder<T> WithSettings(object settings)
    {
        var newSettings = new List<string>();

        var settingsProperties = settings.GetType().GetProperties();
        foreach (var property in settingsProperties)
        {
            var name = property.Name;
            var value = property.GetValue(settings);
            var newSetting = $"{name} = {value}";
            newSettings.Add(newSetting);
        }

        _settings.AddRange(newSettings);
        return this;
    }
}
