namespace AT.SharedResources.Resources;

using System.CodeDom.Compiler;
using System.Resources;
using AT.Translations;

[GeneratedCode("AT.Translations.Generator.ResxTranslationsGenerator.cs", "1.0.0.0")]
public static class SharedResourcesResourceManagers
{
    public static ResourceManagerWrapper[] ResourceManagers = new ResourceManagerWrapper[]
    {
        new ResourceManagerWrapper(new ResourceManager("AT.SharedResources.Resources.Entities.Entities", typeof(SharedResourcesAssembly).Assembly), new ResxLocation(AssemblyName: "SharedResources", ResxRelativeNamespace: "Entities.Entities")),
        new ResourceManagerWrapper(new ResourceManager("AT.SharedResources.Resources.Global.Global", typeof(SharedResourcesAssembly).Assembly), new ResxLocation(AssemblyName: "SharedResources", ResxRelativeNamespace: "Global.Global"))
    };
}