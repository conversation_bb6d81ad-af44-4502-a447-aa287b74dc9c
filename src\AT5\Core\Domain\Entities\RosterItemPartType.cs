﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;

public class RosterItemPartType
{
    public RosterItemPartTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Abbreviation { get; set; } = null!;

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public bool WorkingTime { get; set; }

    public int ShowPriority { get; set; }

    public int AppearancePriority { get; set; }

    public string? ExportCode { get; set; }

    public bool Disabled { get; set; }

    public bool IsGlobal { get; set; }

    public bool ShowInLegend { get; set; }

    public int Rank { get; set; }

    public bool ImplicitAssignment { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public virtual ICollection<Adherence> Adherences { get; set; } = new List<Adherence>();

    public virtual ICollection<ParallelPart> ParallelParts { get; set; } = new List<ParallelPart>();

    public virtual ICollection<Part> Parts { get; set; } = new List<Part>();

    public virtual ICollection<Requirement> Requirements { get; set; } = new List<Requirement>();

    public virtual ICollection<RosterItemPartRule> RosterItemPartRules { get; set; } = new List<RosterItemPartRule>();

    public virtual ICollection<RosterItemPartTypeFilter> RosterItemPartTypeFilters { get; set; } =
        new List<RosterItemPartTypeFilter>();

    public virtual ICollection<RosterItemPart> RosterItemParts { get; set; } = new List<RosterItemPart>();

    public virtual ICollection<Requirement> RequirementSatisfactoryRosterItemPartTypes { get; set; } =
        new List<Requirement>();

    public virtual ICollection<RosterItemPartRule> RosterItemPartRuleTypes { get; set; } =
        new List<RosterItemPartRule>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
