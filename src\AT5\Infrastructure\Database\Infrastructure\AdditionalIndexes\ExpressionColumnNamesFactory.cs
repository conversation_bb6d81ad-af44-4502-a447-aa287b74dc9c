﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

/// <summary>
/// Creates column names from selector expression.
/// </summary>
/// <param name="_columnsExpression">Expression selecting the columns.</param>
internal sealed class ExpressionColumnNamesFactory<T>(Expression<Func<T, object?>> _columnsExpression)
    : IColumnNamesFactory<T>
    where T : class
{
    public IEnumerable<string> MakeColumnNames(EntityTypeBuilder<T> entityTypeBuilder)
    {
        return ExtractColumnNamesFromExpression(entityTypeBuilder, _columnsExpression);
    }

    private static IEnumerable<string> ExtractColumnNamesFromExpression(
        EntityTypeBuilder<T> entityTypeBuilder,
        Expression<Func<T, object?>> expression
    )
    {
        // We expect column names as either x => x.Foo or x => new { x.Foo, x.Bar }.

        if (expression.Body is UnaryExpression unaryExpression)
        {
            return ExtractColumnNamesFromExpression(entityTypeBuilder, unaryExpression);
        }

        if (expression.Body is NewExpression newExpression)
        {
            return ExtractColumnNamesFromExpression(entityTypeBuilder, newExpression);
        }

        throw new ArgumentException(
            $"Expression with body of type {expression.Body.GetType().Name} is not supported.",
            nameof(expression)
        );
    }

    private static IEnumerable<string> ExtractColumnNamesFromExpression(
        EntityTypeBuilder<T> entityTypeBuilder,
        UnaryExpression expression
    )
    {
        // We expect column name as x => x.Foo.

        if (expression.Operand is MemberExpression memberExpression)
        {
            yield return ExtractColumnNameFromExpression(entityTypeBuilder, memberExpression);
            yield break;
        }

        throw new ArgumentException(
            $"Expression with operand of type {expression.Operand.GetType().Name} is not supported.",
            nameof(expression)
        );
    }

    private static IEnumerable<string> ExtractColumnNamesFromExpression(
        EntityTypeBuilder<T> entityTypeBuilder,
        NewExpression expression
    )
    {
        // We expect column names as x => new { x.Foo, x.Bar }.

        foreach (var argument in expression.Arguments)
        {
            if (argument is MemberExpression memberExpression)
            {
                yield return ExtractColumnNameFromExpression(entityTypeBuilder, memberExpression);
                continue;
            }

            throw new ArgumentException(
                $"NewExpression with argument of type {argument.GetType().Name} is not supported.",
                nameof(expression)
            );
        }
    }

    private static string ExtractColumnNameFromExpression(
        EntityTypeBuilder<T> entityTypeBuilder,
        MemberExpression expression
    )
    {
        // When we get here the expression is something like x.Foo.Bar.Baz.

        Expression? tmp = expression;
        var path = new List<string>();
        while (tmp is MemberExpression memberExpression)
        {
            path.Add(memberExpression.Member.Name);
            tmp = memberExpression.Expression;
        }

        // By walking through the expression we get a path to the required element, the path will look like this:
        // [ "Baz", "Bar", "Foo" ]

        if (path.Count == 1)
        {
            // If there was only one step in the path, the original expression was something like x.Foo.
            // This means we only need the property of the entity called Foo.

            var property = entityTypeBuilder.Metadata.FindProperty(path[0]);
            if (property is null)
            {
                throw new ArgumentException(
                    $"Path {path[0]} specified by the given expression doesn't point to a property with a column.",
                    nameof(expression)
                );
            }

            return property.GetColumnName();
        }

        // If there were more steps in the path than one, the original expression was something like x.Foo.Bar.Baz.
        // This means that we have to walk through a serie of complex types and only the final element corresponds to a column.

        // The path was generated in a reverse order because the expression was basically a stack, but we need to walk it in natural order.
        path.Reverse();

        // We expact that first N - 1 elements of the path were all complex types, we need to get the final one.
        var complexType = FindComplexType(entityTypeBuilder, path.Take(path.Count - 1));
        // We use the final complex type to retrieve the selected property.
        var finalProperty = complexType.FindProperty(path[^1]);
        if (finalProperty is null)
        {
            throw new ArgumentException(
                $"Path {string.Join('.', path)} specified by the given expression doesn't point to a property with a column.",
                nameof(expression)
            );
        }

        return finalProperty.GetColumnName();
    }

    private static IMutableComplexType FindComplexType(EntityTypeBuilder<T> entityTypeBuilder, IEnumerable<string> path)
    {
        var firstPathStep = path.First();
        var complexProperty = entityTypeBuilder.Metadata.FindComplexProperty(firstPathStep);
        if (complexProperty is null)
        {
            throw new ArgumentException(
                $"Path {string.Join('.', path)} doesn't point to a complex property.",
                nameof(path)
            );
        }

        var complexType = complexProperty.ComplexType;
        foreach (var step in path.Skip(1))
        {
            complexProperty = complexType.FindComplexProperty(step);
            if (complexProperty is null)
            {
                throw new ArgumentException(
                    $"Path {string.Join('.', path)} doesn't point to a complex property.",
                    nameof(path)
                );
            }

            complexType = complexProperty.ComplexType;
        }

        return complexType;
    }
}
