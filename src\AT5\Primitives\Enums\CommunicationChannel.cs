﻿namespace AT.Primitives.Enums;

public enum CommunicationChannel
{
    None = 0,
    IncomingCall = 1,
    OutgoingCall = 2,
    Email = 3,
    Sms = 4,
    Fax = 5,
    InstantMessage = 6,
    Visit = 7,
    Chat = 8,
    OutgoingEmail = 9,
    Item = 10,
    Task = 11,
    ManHours = 12,
    ManHoursDeferred = 13,
    ManHoursPreparation = 14,
    Quantity = 15,
    WorkOrder = 16,
    WorkOrderNoSimulation = 17, // Data are the same as for WorkOrders, but no predictions are viewed/simulated and no people are assigned.
    WorkMission = 18
}
