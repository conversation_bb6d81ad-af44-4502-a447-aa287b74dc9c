﻿#pragma warning disable // IDE0005 - Using directive is unnecessary.

// Global usings from implicit usings, except we don't want Microsoft.Extensions.Logging because of conflicting ILogger<T>
global using System;
global using System.Collections.Generic;
global using System.IO;
global using System.Linq;
global using System.Net.Http;
global using System.Threading;
global using System.Threading.Tasks;
// Custom global usings
global using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
global using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
global using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
global using AT.Core.Domain.PushNotificationTokenAggregate;
global using AT.PrimitivesAT5.Ids;
global using AT.Utilities.Collections;
global using AT.Utilities.Logging;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
