﻿namespace AT.Core.Domain.Entities;

public class JobRun
{
    public JobRunId Id { get; set; }

    public JobId JobId { get; set; }

    public DateTime Triggered { get; set; }

    public DateTime? Started { get; set; }

    public DateTime? Finished { get; set; }

    public JobResultType Result { get; set; }

    public UserId? AuthorId { get; set; }

    public string? JobMethod { get; set; }

    public string? MethodParameters { get; set; }

    public virtual Job Job { get; set; } = null!;
}
