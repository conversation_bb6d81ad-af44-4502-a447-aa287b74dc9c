﻿namespace AT.Core.Domain.Entities;

public class ShiftPopularity
{
    public ShiftPopularityId Id { get; set; }

    public IntervalPreferenceType Type { get; set; }

    public string? Description { get; set; }

    public TimeInterval TimeInterval { get; set; }

    public double Priority { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public SiteId SiteId { get; set; }

    public virtual Site Site { get; set; } = null!;
}
