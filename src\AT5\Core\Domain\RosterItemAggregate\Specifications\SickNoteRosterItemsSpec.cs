﻿namespace AT.Core.Domain.RosterItemAggregate.Specifications;

using System.Linq;
using Ardalis.Specification;
using AT.Core.Domain.RosterItemAggregate;

public class SickNoteRosterItemsSpec : Specification<RosterItem>
{
    public SickNoteRosterItemsSpec(IReadOnlyCollection<SickNoteId> sickNotesIds)
    {
        Query
            .Where(ri => ri.SickNoteId != null)
            .Where(ri => sickNotesIds.Contains(ri.SickNoteId!.Value))
            .Include(ri => ri.RosterItemParts);
    }
}
