﻿namespace AT.Core.Domain.Entities;

using Base;

public class CalculationTypePhase
{
    public CalculationTypePhaseId Id { get; set; }

    public string Parameters { get; set; } = null!;

    public int Rank { get; set; }

    public CalculationTypeId CalculationTypeId { get; set; }

    public CalculationPhaseId CalculationPhaseId { get; set; }

    public virtual CalculationPhase CalculationPhase { get; set; } = null!;

    public virtual CalculationType CalculationType { get; set; } = null!;
}
