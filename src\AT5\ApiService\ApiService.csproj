﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
		<Platforms>x64</Platforms>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\ServiceDefaults\ServiceDefaults.csproj" />

		<ProjectReference Include="..\Core\Core.csproj" />
		<ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
		<ProjectReference Include="..\UseCases\UseCases.csproj" />
		<ProjectReference Include="..\Shared\Shared.csproj" />
		<ProjectReference Include="..\SharedResources\SharedResources.csproj" />

		<ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
		<ProjectReference Include="..\Primitives\Primitives.csproj" />
		<ProjectReference Include="..\DataStructures\DataStructures.csproj" />
		<ProjectReference Include="..\Utilities\Utilities.csproj" />
	</ItemGroup>

	<ItemGroup>
		<!-- Referenced packages. Package versions are managed centrally in Directory.Packages.props file -->
		<PackageReference Include="Aspire.StackExchange.Redis" />
		<PackageReference Include="Autofac" />
		<PackageReference Include="Autofac.AspNetCore.Multitenant" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" />
		<PackageReference Include="Autofac.Multitenant" />
		<PackageReference Include="FastEndpoints" />
		<PackageReference Include="FastEndpoints.Swagger" />
		<PackageReference Include="FastEndpoints.ClientGen" />
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" />
		<PackageReference Include="NSwag.AspNetCore" />
		<PackageReference Include="Scalar.AspNetCore" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.local.json" Condition="'$(Configuration)'=='Debug'">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
