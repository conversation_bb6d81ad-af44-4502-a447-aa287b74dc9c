﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;

public class WorkOrderPrediction
{
    public WorkOrderPredictionId Id { get; set; }

    public string Name { get; set; } = null!;

    public DateTime PlannedStart { get; set; }

    public DateTime Deadline { get; set; }

    public double Volume { get; set; }

    public DoubleValueChange ProductivityChange { get; set; } = null!;

    public string? Color { get; set; }

    public bool Ignore { get; set; }

    public QueueId QueueId { get; set; }

    public PredictionId PredictionId { get; set; }

    public SiteId SiteId { get; set; }

    public virtual Prediction Prediction { get; set; } = null!;

    public virtual Queue Queue { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
