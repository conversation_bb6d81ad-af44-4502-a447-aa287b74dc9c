namespace AT.Infrastructure.Database.Infrastructure;

using AT.PrimitivesAT5.Ids;
using Core.Domain.EntitySubtypes;
using Vogen;

/// <summary>
/// Here we need to list all value objects used in Entities so that Vogen could generate EF converts for them.
/// After adding new attributes in here, the generated method RegisterAllInVogenEfCoreConverters is updated.
/// </summary>
[EfCoreConverter<SkillLevel>]
[EfCoreConverter<ApprovalHistoryId>]
[EfCoreConverter<ApprovalId>]
[EfCoreConverter<ApprovalTypeFilterId>]
[EfCoreConverter<ApprovalTypeId>]
[EfCoreConverter<ActivitySkillId>]
[EfCoreConverter<AdherenceId>]
[EfCoreConverter<AdherenceStatusId>]
[EfCoreConverter<AgencyId>]
[EfCoreConverter<AuditEntityId>]
[EfCoreConverter<AuditInsertPropertyId>]
[EfCoreConverter<AuditRelationshipId>]
[EfCoreConverter<AuditUpdatePropertyId>]
[EfCoreConverter<AuthenticationTokenId>]
[EfCoreConverter<BalanceConfigurationId>]
[EfCoreConverter<BreakTemplateId>]
[EfCoreConverter<BudgetActivityId>]
[EfCoreConverter<BudgetHoursAllowanceId>]
[EfCoreConverter<CalculationId>]
[EfCoreConverter<CalculationErrorId>]
[EfCoreConverter<CalculationLogId>]
[EfCoreConverter<CalculationLogEntryId>]
[EfCoreConverter<CalculationPhaseId>]
[EfCoreConverter<CalculationTypeId>]
[EfCoreConverter<CalculationTypePhaseId>]
[EfCoreConverter<ChangeId>]
[EfCoreConverter<ChangeLogId>]
[EfCoreConverter<ChangeRequestId>]
[EfCoreConverter<ContractId>]
[EfCoreConverter<DbUpgradeId>]
[EfCoreConverter<DbUpgradeLogId>]
[EfCoreConverter<DefaultConfigParameterId>]
[EfCoreConverter<EmployeeActivityId>]
[EfCoreConverter<EmployeeBalanceId>]
[EfCoreConverter<EmployeeContractId>]
[EfCoreConverter<EmployeeDefaultWorkingTimeModelId>]
[EfCoreConverter<EmployeeLocationId>]
[EfCoreConverter<EmployeeOvertimeSuggestionId>]
[EfCoreConverter<EmployeePositionId>]
[EfCoreConverter<EmployeePropertyId>]
[EfCoreConverter<EmployeePublicRosterId>]
[EfCoreConverter<EmployeeQueuePropertyId>]
[EfCoreConverter<EmployeeRequestTypeId>]
[EfCoreConverter<EmployeeRosterFinishId>]
[EfCoreConverter<EmployeeShiftTemplateId>]
[EfCoreConverter<EmployeeSkillId>]
[EfCoreConverter<EmployeeTimePreferenceId>]
[EfCoreConverter<EmployeeWorkingTimeModelId>]
[EfCoreConverter<EventId>]
[EfCoreConverter<ExternalStatusId>]
[EfCoreConverter<Filter2Id>]
[EfCoreConverter<JobId>]
[EfCoreConverter<JobLogEntryId>]
[EfCoreConverter<JobRunId>]
[EfCoreConverter<JobTriggerId>]
[EfCoreConverter<LicenceId>]
[EfCoreConverter<LicencePriceId>]
[EfCoreConverter<LicenceRuleId>]
[EfCoreConverter<LocationId>]
[EfCoreConverter<MeetingId>]
[EfCoreConverter<NoteId>]
[EfCoreConverter<OrganizationId>]
[EfCoreConverter<ParallelPartId>]
[EfCoreConverter<PartId>]
[EfCoreConverter<PermissionsGroupId>]
[EfCoreConverter<PlanningPeriodId>]
[EfCoreConverter<PositionId>]
[EfCoreConverter<PredictionId>]
[EfCoreConverter<PreparationWayId>]
[EfCoreConverter<PreparationWayBonusCoefficientId>]
[EfCoreConverter<PropertyId>]
[EfCoreConverter<PropertyCategoryId>]
[EfCoreConverter<PropertyFilterId>]
[EfCoreConverter<PublicHolidayId>]
[EfCoreConverter<PushNotificationTokenId>]
[EfCoreConverter<QueueId>]
[EfCoreConverter<QueuePropertyId>]
[EfCoreConverter<RemovedNotificationId>]
[EfCoreConverter<ReportId>]
[EfCoreConverter<ReportCategoryId>]
[EfCoreConverter<ReportColumnId>]
[EfCoreConverter<ReportColumnFieldId>]
[EfCoreConverter<ReportDimensionId>]
[EfCoreConverter<ReportSubtotalId>]
[EfCoreConverter<RequestId>]
[EfCoreConverter<RequestFileId>]
[EfCoreConverter<RequestLimitId>]
[EfCoreConverter<RequestPropertyId>]
[EfCoreConverter<RequestSyncQueueEntryId>]
[EfCoreConverter<RequestSyncStateId>]
[EfCoreConverter<RequestTypeId>]
[EfCoreConverter<RequestTypeFilterId>]
[EfCoreConverter<RequirementId>]
[EfCoreConverter<ReservationId>]
[EfCoreConverter<RoleId>]
[EfCoreConverter<RoleDelegationId>]
[EfCoreConverter<RoleDelegationTypeId>]
[EfCoreConverter<RosterId>]
[EfCoreConverter<RosterItemId>]
[EfCoreConverter<RosterItemPartId>]
[EfCoreConverter<RosterItemPartRuleId>]
[EfCoreConverter<RosterItemPartTypeId>]
[EfCoreConverter<RosterItemPartTypeFilterId>]
[EfCoreConverter<RosterItemPropertyId>]
[EfCoreConverter<RosterItemSyncQueueEntryId>]
[EfCoreConverter<RosterItemSyncStateId>]
[EfCoreConverter<RosteringRuleId>]
[EfCoreConverter<RosteringRuleFilters2Id>]
[EfCoreConverter<RoutingId>]
[EfCoreConverter<ServiceId>]
[EfCoreConverter<ShiftPopularityId>]
[EfCoreConverter<ShiftSystemId>]
[EfCoreConverter<ShiftSystemDayId>]
[EfCoreConverter<ShiftTemplateId>]
[EfCoreConverter<ShiftTemplateFilterId>]
[EfCoreConverter<SickNoteId>]
[EfCoreConverter<SiteId>]
[EfCoreConverter<SitePropertyId>]
[EfCoreConverter<SiteRelationId>]
[EfCoreConverter<SiteSubtypeId>]
[EfCoreConverter<SiteTypeId>]
[EfCoreConverter<SitesConfigParameterId>]
[EfCoreConverter<SkillId>]
[EfCoreConverter<SqlReportDefinitionId>]
[EfCoreConverter<StatusPartId>]
[EfCoreConverter<StatusTypeId>]
[EfCoreConverter<TagId>]
[EfCoreConverter<TaskTodoId>]
[EfCoreConverter<TeamId>]
[EfCoreConverter<TradeAnswerId>]
[EfCoreConverter<TradeOfferId>]
[EfCoreConverter<TranslationId>]
[EfCoreConverter<TranslationTextId>]
[EfCoreConverter<UserId>]
[EfCoreConverter<UserFilterId>]
[EfCoreConverter<UserRoleId>]
[EfCoreConverter<WorkMissionId>]
[EfCoreConverter<WorkMissionTypeId>]
[EfCoreConverter<WorkOrderId>]
[EfCoreConverter<WorkOrderPredictionId>]
[EfCoreConverter<WorkOrderProductivityId>]
[EfCoreConverter<WorkingTimeId>]
[EfCoreConverter<WorkingTimeModelId>]
[EfCoreConverter<WorkingTimeModelShiftSystemId>]
public partial class VogenEfCoreConverters;
