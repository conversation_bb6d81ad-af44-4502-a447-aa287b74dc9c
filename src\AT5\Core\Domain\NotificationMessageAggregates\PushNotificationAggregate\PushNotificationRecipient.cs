﻿namespace AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;

using AT.Core.Domain.NotificationMessageAggregates;
using AT.Core.Domain.UserAggregate;

public class PushNotificationRecipient : IEntity, INotificationMessageRecipient
{
    public long Id { get; set; }

    public long PushNotificationId { get; set; }

    public UserId UserId { get; set; }

    public virtual PushNotification PushNotification { get; set; } = null!;

    public long MessageId => PushNotificationId;

    public INotificationMessage Message => PushNotification;

    UserId? INotificationMessageRecipient.UserId => UserId;
}
