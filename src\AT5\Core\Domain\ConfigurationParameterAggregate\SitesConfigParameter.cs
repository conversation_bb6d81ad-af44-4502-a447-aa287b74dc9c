﻿namespace AT.Core.Domain.ConfigurationParameterAggregate;

using AT.Core.Domain.Entities;
using Base;

public class SitesConfigParameter
{
    public SitesConfigParameterId Id { get; set; }

    public string? Value { get; set; }

    public SiteId SiteId { get; set; }

    public DefaultConfigParameterId DefaultConfigParameterId { get; set; }

    public virtual DefaultConfigParameter DefaultConfigParameter { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
