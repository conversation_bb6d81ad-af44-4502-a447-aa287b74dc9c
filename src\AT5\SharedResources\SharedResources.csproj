﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <WarningsAsErrors>Nullable</WarningsAsErrors>
        <Platforms>x64</Platforms>
        <RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <!--This must be in here because Directory.packages.props generates AssemblyInfo from Primitives for each project.-->
        <ProjectReference Include="..\Primitives\Primitives.csproj" />

        <ProjectReference Include="..\Translations\Translations.csproj" />
		<ProjectReference Include="..\DataStructures\DataStructures.csproj" />
    </ItemGroup>

</Project>
