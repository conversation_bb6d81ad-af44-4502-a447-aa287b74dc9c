﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class CalculationError
{
    public CalculationErrorId Id { get; set; }

    public CalculationId CalculationId { get; set; }

    public string Name { get; set; } = null!;

    public string Parameters { get; set; } = null!;

    public ErrorLevel Level { get; set; }

    public virtual Calculation Calculation { get; set; } = null!;
}
