﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class Request
{
    public RequestId Id { get; set; }

    public string? Description { get; set; }

    public DateTimeInterval Interval { get; set; }

    public int Priority { get; set; }

    public RequestIntervalType IntervalType { get; set; }

    public RequestStatus Status { get; set; }

    public UserId EmployeeId { get; set; }

    public RequestTypeId RequestTypeId { get; set; }

    public DateTime Changed { get; set; }

    public UserId ChangedById { get; set; }

    public DateTime Created { get; set; }

    public UserId CreatedById { get; set; }

    public RequestAction Action { get; set; }

    public SiteId SiteId { get; set; }

    public virtual User ChangedBy { get; set; } = null!;

    public virtual User CreatedBy { get; set; } = null!;

    public virtual Employee Employee { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual ICollection<RequestProperty> RequestProperties { get; set; } = new List<RequestProperty>();

    public virtual ICollection<RequestSyncQueueEntry> RequestSyncQueueEntries { get; set; } =
        new List<RequestSyncQueueEntry>();

    public virtual ICollection<RequestSyncState> RequestSyncStates { get; set; } = new List<RequestSyncState>();

    public virtual RequestType RequestType { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<Request> ReferencedBies { get; set; } = new List<Request>();

    public virtual ICollection<Request> RequestReferences { get; set; } = new List<Request>();

    public virtual ICollection<ShiftTemplate> ShiftTemplates { get; set; } = new List<ShiftTemplate>();
}
