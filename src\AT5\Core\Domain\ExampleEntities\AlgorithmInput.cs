﻿namespace AT.Core.Domain.ExampleEntities;

using Vogen;

[ValueObject<int>]
public readonly partial struct Divident;

[ValueObject<int>]
public readonly partial struct Divisor
{
    public static Validation Validate(int value)
    {
        return value != 0 ? Validation.Ok : Validation.Invalid("Divisor cannot be 0.");
    }
};

public record AlgorithmInput(Divident Number1, Divisor Number2)
{
    public override string ToString()
    {
        return $"{Number1}/{Number2}";
    }
}
