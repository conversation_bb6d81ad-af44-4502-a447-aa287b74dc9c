﻿namespace AT.NotificationsService.PushNotifications;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate.Specifications;
using AT.NotificationsService.General.Interfaces;

internal class PushNotificationDatabaseProvider(IPushNotificationRepository _pushNotificationRepository)
    : IMessageProvider<PushNotification>
{
    public async Task<List<PushNotification>> GetInstantMessagesAsync(int? limit)
    {
        return await _pushNotificationRepository.ListAsync(new InstantPushNotificationsBatchSpec(limit));
    }

    public async Task<List<PushNotification>> GetScheduledMessagesAsync(DateTime untilExclusive, int? limit = null)
    {
        return await _pushNotificationRepository.ListAsync(
            new ScheduledPushNotificationsSpec(untilExclusive, batchSize: limit)
        );
    }

    public async Task MarkAsProcessedAsync(IEnumerable<PushNotification> messages)
    {
        foreach (var pushNotification in messages)
        {
            _pushNotificationRepository.AddForDelete(pushNotification);
        }

        await _pushNotificationRepository.CommitAsync();
    }
}
