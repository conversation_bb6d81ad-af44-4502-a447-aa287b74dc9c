﻿namespace AT.ApiService.Middleware;

using System.Threading.Tasks;
using AT.Core.MasterDomain;

/// <summary>
/// Ensures that every logged message contains the current tenant's (organization) name.
/// </summary>
public class OrganizationLoggingScopeMiddleware(
    ILogger<OrganizationLoggingScopeMiddleware> _logger,
    FullOrgId _fullOrgId
) : IMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        using var _ = _logger.BeginScopeWithProperties(("Organization", _fullOrgId.DatabaseName));
        await next.Invoke(context);
    }
}
