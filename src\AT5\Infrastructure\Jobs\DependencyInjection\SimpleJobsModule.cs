﻿namespace AT.Infrastructure.Jobs.DependencyInjection;

using AT.Core.SimpleJobs;
using AT.Infrastructure.Jobs.SimpleJobs;
using Autofac;
using Autofac.Multitenant;

public static class SimpleJobsModule
{
    public static ContainerBuilder AddSimpleJobScheduling(this ContainerBuilder containerBuilder)
    {
        // Requires AddQuartzCustom to be invoked on ServiceCollection.
        containerBuilder.RegisterType<ScopedJobScheduler>().As<ISimpleJobScheduler>().InstancePerTenant();

        return containerBuilder;
    }
}
