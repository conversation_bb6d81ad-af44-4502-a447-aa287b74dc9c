﻿namespace AT.Core.Jobs.JobService.EmailsService;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Utilities.Logging;

public class JobEmailsServiceFactory(
    ILogger<JobEmailsService> _jobEmailsServiceLogger,
    IEmailMessageRepository _emailMessageRepository
) : IJobEmailsServiceFactory
{
    public IJobEmailsService Create(string jobName, string[] successEmails, string[] errorEmails)
    {
        return new JobEmailsService(
            _jobEmailsServiceLogger,
            _emailMessageRepository,
            jobName,
            successEmails,
            errorEmails
        );
    }
}
