﻿namespace AT.Core.Domain.Entities;

using Base;

public class ShiftSystem
{
    public ShiftSystemId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string? Parameters { get; set; }

    public bool IsGlobal { get; set; }

    public virtual ICollection<ShiftSystemDay> ShiftSystemDays { get; set; } = new List<ShiftSystemDay>();

    public virtual ICollection<WorkingTimeModelShiftSystem> WorkingTimeModelShiftSystems { get; set; } =
        new List<WorkingTimeModelShiftSystem>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
