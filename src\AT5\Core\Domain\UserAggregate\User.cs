namespace AT.Core.Domain.UserAggregate;

using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.Entities;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.RosterItemAggregate;
using Primitives.Enums;

public class User : IAggregateRoot
{
    public UserId Id { get; set; }

    public string Username { get; set; } = null!;

    public string? Password { get; set; }

    public string? Email { get; set; }

    public string FirstName { get; set; } = null!;

    public string? MiddleName { get; set; }

    public string LastName { get; set; } = null!;

    public bool IsApproved { get; set; }

    public bool IsLockedOut { get; set; }

    public DateTime LastLogin { get; set; }

    public DateTime LastPasswordChange { get; set; }

    public DateTime LastLockout { get; set; }

    public DateTime FailedPasswordStart { get; set; }

    public int FailedPasswordCount { get; set; }

    public DateTime Created { get; set; }

    public int CreatedBy { get; set; }

    public DateTime Changed { get; set; }

    public int ChangedBy { get; set; }

    public bool ForceChangePassword { get; set; }

    public string? Phone { get; set; }

    public string? Email2 { get; set; }

    public Language? Language { get; set; }

    public bool IsClientApplication { get; set; }

    public virtual ICollection<AuthenticationToken> AuthenticationTokens { get; set; } =
        new List<AuthenticationToken>();

    public virtual ICollection<Calculation> Calculations { get; set; } = new List<Calculation>();

    public virtual ICollection<ChangeLog> ChangeLogs { get; set; } = new List<ChangeLog>();

    public virtual ICollection<ChangeRequest> ChangeRequestCreatedBies { get; set; } = new List<ChangeRequest>();

    public virtual ICollection<ChangeRequest> ChangeRequestProcessedBies { get; set; } = new List<ChangeRequest>();

    public virtual ICollection<EmployeeProperty> EmployeePropertyStateChangedBies { get; set; } =
        new List<EmployeeProperty>();

    public virtual ICollection<EmployeeProperty> EmployeePropertyValueChangedBies { get; set; } =
        new List<EmployeeProperty>();

    public virtual ICollection<EmployeePublicRoster> EmployeePublicRosterAuthors { get; set; } =
        new List<EmployeePublicRoster>();

    public virtual ICollection<Note> Notes { get; set; } = new List<Note>();

    public virtual ICollection<PlanningPeriod> PlanningPeriods { get; set; } = new List<PlanningPeriod>();

    public virtual ICollection<PushNotificationToken> PushNotificationTokens { get; set; } =
        new List<PushNotificationToken>();

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual ICollection<Request> RequestChangedBies { get; set; } = new List<Request>();

    public virtual ICollection<Request> RequestCreatedBies { get; set; } = new List<Request>();

    public virtual ICollection<RequestFile> RequestFiles { get; set; } = new List<RequestFile>();

    public virtual ICollection<RequestProperty> RequestPropertyStateChangedBies { get; set; } =
        new List<RequestProperty>();

    public virtual ICollection<RequestProperty> RequestPropertyValueChangedBies { get; set; } =
        new List<RequestProperty>();

    public virtual ICollection<Reservation> ReservationCreatedBies { get; set; } = new List<Reservation>();

    public virtual ICollection<Reservation> ReservationProcessedBies { get; set; } = new List<Reservation>();

    public virtual ICollection<Role> Roles { get; set; } = new List<Role>();

    public virtual ICollection<Roster> RosterCreatedBies { get; set; } = new List<Roster>();

    public virtual ICollection<RosterItem> CreatedRosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<Roster> RosterLockedBies { get; set; } = new List<Roster>();

    public virtual ICollection<TaskTodo> TaskChangedBies { get; set; } = new List<TaskTodo>();

    public virtual ICollection<TaskTodo> TaskCreatedBies { get; set; } = new List<TaskTodo>();

    public virtual ICollection<UserFilter> UserFilters { get; set; } = new List<UserFilter>();

    public virtual ICollection<UserRole> UserRoleSubordinateUsers { get; set; } = new List<UserRole>();

    public virtual ICollection<UserRole> UserRoleUsers { get; set; } = new List<UserRole>();
}
