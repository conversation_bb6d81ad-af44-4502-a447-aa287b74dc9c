﻿namespace AT.Shared.Formatting.Time;

using AT.Shared.Formatting.Time.Formatters;

public sealed class TimeFormatters
{
    public required DateFormatter DateFormatter { get; init; }

    public required DateIntervalFormatter DateIntervalFormatter { get; init; }

    public required DateTimeFormatter DateTimeFormatter { get; init; }

    public required DateTimeIntervalFormatter DateTimeIntervalFormatter { get; init; }

    public required TimeFormatter TimeFormatter { get; init; }

    public required TimeValidityFormatter TimeValidityFormatter { get; init; }

    public required ValidityFormatter ValidityFormatter { get; init; }

    public required TimeSpanFormatter TimeSpanFormatter { get; init; }
}
