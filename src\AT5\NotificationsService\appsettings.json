﻿{
  "GeneralConfig": {
    "DbName": "at_csob",
    "IsSingleOrg": true
  },
  "ConnectionStrings": {
    "MasterDb": "Data Source=.\\;Initial Catalog=AristoTelos;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;",
    "OrgDbTemplate": "Data Source=.\\;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;"
  },
  "PushNotificationConfig": {
    "FirebaseConfiguration": {
      "GoogleServices": "JSON_PARSED_GOOGLE_SERVICES_FROM_FIREBASE_REPLACED_BY_PIPELINE_OR_LOCAL_APPSETTINGS"
    },
    "CsvLogsDirectory": "Notifications\\PushNotifications"
  },
  "EmailConfig": {
    "CsvLogsDirectory": "Notifications\\Emails",
    "AttachmentContentsDirectory": "Notifications\\Emails\\Attachments",
    "EmailSettings": {
      "FromName": "AristoTelos",
      "FromAddress": "<EMAIL>",
      "Host": "SMTP_HOST_REPLACED_BY_PIPELINE_OR_LOCAL_APPSETTINGS",
      "Port": "SMTP_PORT_REPLACED_BY_PIPELINE_OR_LOCAL_APPSETTINGS",
      "Username": "SMTP_USERNAME_REPLACED_BY_PIPELINE_OR_LOCAL_APPSETTINGS",
      "Password": "SMTP_PASSWORD_REPLACED_BY_PIPELINE_OR_LOCAL_APPSETTINGS"
    }
  },
  "Logging": {
    "OpenTelemetry": {
      "LogLevel": {
        "Default": "Error",
        "Microsoft": "Error"
      }
    }
  },
  "AzureMonitor": {
    "ConnectionString": "",
    "ServiceName": "",
    "ServiceInstanceId": "",
    "SamplingRatio": 0.05,
    "HealthChecksInterval": 30,
    "DoNotSampleRequestsWithErrors": true
  },
  // Some settings that are not explicitly stated in here are added to the serilog in SerilogLoggerCreator.cs.
  "Serilog": {
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/NotificationsService_.log",
          "fileSizeLimitBytes": 31457280
        }
      }
    ]
  }
}