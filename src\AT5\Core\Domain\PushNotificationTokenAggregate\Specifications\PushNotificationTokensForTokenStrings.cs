﻿namespace AT.Core.Domain.PushNotificationTokenAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.PushNotificationTokenAggregate;

public class PushNotificationTokensForTokenStrings : Specification<PushNotificationToken>
{
    public PushNotificationTokensForTokenStrings(IReadOnlyCollection<string> tokenStrings)
    {
        Query.Where(pnt => tokenStrings.Contains(pnt.Token));
    }
}
