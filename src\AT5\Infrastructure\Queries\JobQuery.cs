﻿namespace AT.Infrastructure.Queries;

using AT.Core.Domain.Entities;
using AT.Core.Domain.JobsAggregate;
using AT.Core.Domain.JobsAggregate.Queries;
using AT.Infrastructure.DataAccess;
using AT.Utilities.Collections;
using AT.Utilities.Cron;
using AT.Utilities.Logging;
using AT.Utilities.Parsing;
using Microsoft.EntityFrameworkCore;

public sealed class JobQuery(ILogger<JobQuery> _logger, IDataSource<Job> _jobDataSource, IJsonParser jsonParser)
    : IJobQuery
{
    public async Task<List<JobSeriesInfo>> GetAllJobSeries(CancellationToken ct = default)
    {
        var jobs = await _jobDataSource
            .Data.Where(j => !j.Disabled)
            .Include(j => j.JobTriggers)
            .Select(j => new
            {
                Id = j.Id,
                Name = j.Name,
                Type = j.Type,
                SuccessEmails = j.SuccessEmails,
                ErrorEmails = j.ErrorEmails,
                Parameters = j.Parameters,
                Triggers = j
                    .JobTriggers.Where(t => !t.Disabled)
                    .Select(t => new
                    {
                        Id = t.Id,
                        FirstJobId = t.JobId,
                        Name = t.Name,
                        Type = t.Type,
                        Cron = new CronExpression(t.Parameters),
                        RunParameters = t.RunParameters,
                    })
                    .ToArray(),
            })
            .ToArrayAsync(ct);

        var jobsMap = jobs.ToDictionary(x => x.Id);
        var allTriggers = jobs.SelectMany(x => x.Triggers);

        var jobSeriesBuilders = new Dictionary<CollectionKey<JobId>, JobSeriesBuilder>();
        foreach (var trigger in allTriggers)
        {
            JobId[] nextJobs = [];
            if (
                !string.IsNullOrEmpty(trigger.RunParameters)
                && jsonParser.TryDeserialize(trigger.RunParameters, out RunParametersInfo? runParametersParsed)
                && runParametersParsed is not null
                && runParametersParsed.NextJobs is not null
            )
            {
                nextJobs = runParametersParsed.NextJobs.ToArray();
            }

            var jobSeriesJobIds = nextJobs.Prepend(trigger.FirstJobId).ToArray();
            var jobSeriesKey = new CollectionKey<JobId>(jobSeriesJobIds);

            var triggerInfo = new JobTriggerInfo(trigger.Id, jobSeriesKey, trigger.Name, trigger.Type, trigger.Cron);

            if (jobSeriesBuilders.TryGetValue(jobSeriesKey, out var existingJobSeriesBuilder))
            {
                existingJobSeriesBuilder.AddTrigger(triggerInfo);
                continue;
            }

            bool isValidSeries = true;
            var newJobSeriesBuilder = new JobSeriesBuilder(jobSeriesJobIds).AddTrigger(triggerInfo);
            foreach (var jobId in jobSeriesJobIds)
            {
                if (!jobsMap.TryGetValue(jobId, out var job))
                {
                    _logger.Error(
                        "Trigger {TriggerName} ({TriggerId}) defines series with invalid job id {TriggeredJobId}.",
                        trigger.Name,
                        trigger.Id,
                        jobId
                    );
                    isValidSeries = false;
                    break;
                }

                newJobSeriesBuilder.AddJob(
                    new JobInfo(
                        job.Id,
                        job.Name,
                        job.Type,
                        ParseEmails(job.SuccessEmails),
                        ParseEmails(job.ErrorEmails),
                        job.Parameters
                    )
                );
            }

            if (isValidSeries)
            {
                jobSeriesBuilders.Add(newJobSeriesBuilder.Key, newJobSeriesBuilder);
            }
        }

        return jobSeriesBuilders.Select(x => x.Value.Build()).ToList();
    }

    private static string[] ParseEmails(string? emails)
    {
        return emails?.Split(';').Select(x => x.Trim()).Where(x => x != string.Empty).ToArray() ?? [];
    }

    private sealed class JobSeriesBuilder(JobId[] _jobIds)
    {
        private readonly List<JobInfo> _jobs = [];
        private readonly List<JobTriggerInfo> _triggers = [];

        public CollectionKey<JobId> Key { get; } = new(_jobIds);

        public JobSeriesBuilder AddJob(JobInfo job)
        {
            _jobs.Add(job);
            return this;
        }

        public JobSeriesBuilder AddTrigger(JobTriggerInfo trigger)
        {
            _triggers.Add(trigger);
            return this;
        }

        public JobSeriesInfo Build()
        {
            return new JobSeriesInfo(Key, _jobs, _triggers);
        }
    }
}
