namespace AT.Infrastructure.Utilities.Parsing;

using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using AT.Utilities.Parsing;

/// <summary>
/// Provides methods for serializing and deserializing JSON data using System.Text.Json.
/// </summary>
public class SystemTextJsonParser : IJsonParser
{
    // Cache JsonSerializerOptions instance to avoid creating a new one for every serialization operation.
    private static readonly JsonSerializerOptions s_cachedDefaultParserOptions =
        new() { PropertyNameCaseInsensitive = true, TypeInfoResolver = SmartJsonTypeInfoResolver.Default };
    private static readonly JsonSerializerOptions s_cachedDefaultSerializerOptions =
        new() { TypeInfoResolver = SmartJsonTypeInfoResolver.Default };
    private static readonly JsonSerializerOptions s_cachedPrettySerializerOptions =
        new() { WriteIndented = true, TypeInfoResolver = SmartJsonTypeInfoResolver.Default };

    public bool TryDeserialize<T>([StringSyntax(StringSyntaxAttribute.Json)] string jsonData, out T? result)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(jsonData))
            {
                result = default;
                return false;
            }

            result = JsonSerializer.Deserialize<T>(jsonData, s_cachedDefaultParserOptions);

            return true;
        }
        catch (JsonException)
        {
            result = default;
            return false;
        }
    }

    public bool TryDeserialize(
        [StringSyntax(StringSyntaxAttribute.Json)] string jsonData,
        Type resultType,
        out object? result
    )
    {
        try
        {
            if (string.IsNullOrWhiteSpace(jsonData))
            {
                result = default;
                return false;
            }

            result = JsonSerializer.Deserialize(jsonData, resultType, s_cachedDefaultParserOptions);

            return true;
        }
        catch (JsonException)
        {
            result = default;
            return false;
        }
    }

    public string Serialize<T>(T value, bool prettyPrint = true)
    {
        return prettyPrint
            ? JsonSerializer.Serialize(value, s_cachedPrettySerializerOptions)
            : JsonSerializer.Serialize(value, s_cachedDefaultSerializerOptions);
    }
}
