﻿namespace AT.Infrastructure.Utilities.Parsing;

using System;
using System.Reflection;
using System.Runtime.Serialization;
using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using DataContractResolver = System.Text.Json.Serialization.Metadata.DataContractResolver;

/// <summary>
/// Uses <see cref="DataContractResolver"/> for objects with <see cref="DataContractAttribute"/>; otherwise uses <see cref="DefaultJsonTypeInfoResolver"/>.
/// </summary>
public class SmartJsonTypeInfoResolver : DefaultJsonTypeInfoResolver
{
    private static readonly Lazy<SmartJsonTypeInfoResolver> s_defaultInstance =
        new(() => new SmartJsonTypeInfoResolver());

    public static SmartJsonTypeInfoResolver Default => s_defaultInstance.Value;

    private readonly DataContractResolver _dataContractResolver = DataContractResolver.Default;

    public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)
    {
        if (type.GetCustomAttribute<DataContractAttribute>() is not null)
        {
            return _dataContractResolver.GetTypeInfo(type, options);
        }

        return base.GetTypeInfo(type, options);
    }
}
