﻿namespace AT.NotificationsService.Emails;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate.Specifications;
using AT.NotificationsService.General.Interfaces;

internal class EmailMessageDatabaseProvider(IEmailMessageRepository _emailMessageRepository)
    : IMessageProvider<EmailMessage>
{
    public async Task<List<EmailMessage>> GetInstantMessagesAsync(int? limit)
    {
        return await _emailMessageRepository.ListAsync(new InstantEmailMessagesBatchSpec(limit));
    }

    public async Task<List<EmailMessage>> GetScheduledMessagesAsync(DateTime untilExclusive, int? limit = null)
    {
        return await _emailMessageRepository.ListAsync(
            new ScheduledEmailMessagesSpec(untilExclusive, batchSize: limit)
        );
    }

    public async Task MarkAsProcessedAsync(IEnumerable<EmailMessage> messages)
    {
        foreach (var emailMessage in messages)
        {
            _emailMessageRepository.AddForDelete(emailMessage);
        }

        await _emailMessageRepository.CommitAsync();
    }
}
