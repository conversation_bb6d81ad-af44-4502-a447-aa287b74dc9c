﻿namespace AT.Primitives.Enums;

[Flags]
public enum RequestStatuses
{
    None = 0,
    Created = 1,
    Pending = 2,
    Approved = 4,
    Rejected = 8,
    Auto = 16,
    Cancelled = 32,
    PreApproved = 64,
    PreRejected = 128,
    PreAuto = 256,
    All = 511
}

public static class RequestStatusesExtensions
{
    public static bool AllowsPreProcessOrPending(this RequestStatuses requestStatuses)
    {
        return requestStatuses.HasFlag(RequestStatuses.PreApproved)
            || requestStatuses.HasFlag(RequestStatuses.PreRejected)
            || requestStatuses.HasFlag(RequestStatuses.PreAuto)
            || requestStatuses.HasFlag(RequestStatuses.Pending);
    }

    public static bool AllowsProcessOrPending(this RequestStatuses requestStatuses)
    {
        return requestStatuses.HasFlag(RequestStatuses.Approved)
            || requestStatuses.HasFlag(RequestStatuses.Rejected)
            || requestStatuses.HasFlag(RequestStatuses.Auto)
            || requestStatuses.HasFlag(RequestStatuses.Pending);
    }

    public static bool ContainsRequestStatus(this RequestStatuses requestStatuses, RequestStatus requestStatus)
    {
        switch (requestStatus)
        {
            case RequestStatus.None:
                return requestStatuses == RequestStatuses.None;
            case RequestStatus.Pending:
                return requestStatuses.HasFlag(RequestStatuses.Pending);
            case RequestStatus.Approved:
                return requestStatuses.HasFlag(RequestStatuses.Approved);
            case RequestStatus.Rejected:
                return requestStatuses.HasFlag(RequestStatuses.Rejected);
            case RequestStatus.Auto:
                return requestStatuses.HasFlag(RequestStatuses.Auto);
            case RequestStatus.Cancelled:
                return requestStatuses.HasFlag(RequestStatuses.Cancelled);
            case RequestStatus.PreApproved:
                return requestStatuses.HasFlag(RequestStatuses.PreApproved);
            case RequestStatus.PreRejected:
                return requestStatuses.HasFlag(RequestStatuses.PreRejected);
            case RequestStatus.PreAuto:
                return requestStatuses.HasFlag(RequestStatuses.PreAuto);
        }

        return false;
    }
}
