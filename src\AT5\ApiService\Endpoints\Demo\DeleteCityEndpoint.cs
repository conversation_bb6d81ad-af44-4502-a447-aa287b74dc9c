﻿namespace AT.ApiService.Endpoints.Demo;

using System.Threading;
using System.Threading.Tasks;
using AT.Shared.Models.Demo;
using FastEndpoints;

public class DeleteCityEndpoint(CitiesRepository citiesRepository) : Ep.Req<DeleteCityRequest>.NoRes
{
    public override void Configure()
    {
        Delete("/demo/cities/{CityId}");
    }

    public override async Task HandleAsync(DeleteCityRequest req, CancellationToken ct)
    {
        citiesRepository.DeleteCity(req.CityId);
        await SendNoContentAsync(ct);
    }
}
