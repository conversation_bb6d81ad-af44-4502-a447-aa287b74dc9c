﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class RosterItemProperty
{
    public RosterItemPropertyId Id { get; set; }

    public string Value { get; set; } = null!;

    public PropertyState State { get; set; }

    public bool Locked { get; set; }

    public string? Note { get; set; }

    public DateTime StateChanged { get; set; }

    public UserId StateChangedById { get; set; }

    public DateTime ValueChanged { get; set; }

    public UserId ValueChangedById { get; set; }

    public PropertyId PropertyId { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public byte Source { get; set; }

    public virtual Property Property { get; set; } = null!;

    public virtual RosterItem RosterItem { get; set; } = null!;
}
