﻿namespace AT.Core.Domain.Entities;

using Base;

public class SiteSubtype
{
    public SiteSubtypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public SiteTypeId? SiteTypeId { get; set; }

    public string? Parameters { get; set; }

    public RosterItemPartTypeId? PrimaryActivityId { get; set; }

    public virtual SiteType? SiteType { get; set; }

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();

    public virtual ActivityType? PrimaryActivity { get; set; }

    public virtual ICollection<ActivityType> OtherActivities { get; set; } = new List<ActivityType>();
}
