﻿namespace AT.Translations.Formatting.Numbers;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class DoubleFormattingAttribute(MaxDecimalPlaces maxDecimalPlaces = MaxDecimalPlaces.Two) : Attribute
{
    public MaxDecimalPlaces MaxDecimalPlaces { get; init; } = maxDecimalPlaces;
}

// FUTURE: This shouldn't be in the Translations project.
// NOTE: The enum value should always be the same as the number.
public enum MaxDecimalPlaces
{
    Zero = 0,

    One = 1,

    Two = 2,

    Three = 3,

    Six = 6,
}
