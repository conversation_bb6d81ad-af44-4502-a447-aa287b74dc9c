﻿namespace AT.NotificationsService.PushNotifications.Tokens;

public class PushNotificationTokensCleanupConfig
{
    public int MaxTokensToValidateInOneIteration { get; set; } = 50;

    public TimeSpan PauseAfterTakeFromQueue { get; set; } = TimeSpan.FromMilliseconds(500);

    public TimeSpan PauseAfterCleanupIteration { get; set; } = TimeSpan.FromSeconds(10);

    public TimeSpan PauseAfterException { get; set; } = TimeSpan.FromSeconds(10);
}
