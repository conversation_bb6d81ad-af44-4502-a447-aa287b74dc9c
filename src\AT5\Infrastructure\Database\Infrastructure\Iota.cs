﻿namespace AT.Infrastructure.Database.Infrastructure;

/// <summary>
/// Provides a simple incremental counter for use in ordering operations,
/// such as determining column order in Entity Framework Core model building.
/// </summary>
internal class Iota
{
    private int _current = 0;

    /// <summary>
    /// Returns the current value of the counter and increments it.
    /// </summary>
    /// <returns>
    /// The current value of the counter before incrementing.
    /// </returns>
    /// <example>
    /// <code>
    /// var iota = new Iota();
    /// int first = iota.Next(); // first = 0
    /// int second = iota.Next(); // second = 1
    /// </code>
    /// </example>
    public int Next()
    {
        return _current++;
    }
}
