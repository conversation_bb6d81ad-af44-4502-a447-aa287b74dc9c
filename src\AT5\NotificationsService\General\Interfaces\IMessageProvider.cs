﻿namespace AT.NotificationsService.General.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates;

public interface IMessageProvider<TMessageType>
    where TMessageType : INotificationMessage
{
    Task<List<TMessageType>> GetInstantMessagesAsync(int? limit);

    Task<List<TMessageType>> GetScheduledMessagesAsync(DateTime untilExclusive, int? limit = null);

    Task MarkAsProcessedAsync(IEnumerable<TMessageType> messages);
}
