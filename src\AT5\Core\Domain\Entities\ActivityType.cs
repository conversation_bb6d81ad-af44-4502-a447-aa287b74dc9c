﻿namespace AT.Core.Domain.Entities;

using Base;

public class ActivityType
{
    public string? CcId { get; set; }

    public RequestTypeId RequestTypeId { get; set; }

    public bool Productive { get; set; }

    public RosterItemPartTypeId Id { get; set; }

    public virtual ICollection<ActivitySkill> ActivitySkills { get; set; } = new List<ActivitySkill>();

    public virtual ICollection<BudgetActivity> BudgetActivities { get; set; } = new List<BudgetActivity>();

    public virtual ICollection<EmployeeActivity> EmployeeActivities { get; set; } = new List<EmployeeActivity>();

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;

    public virtual ICollection<RequestTypeActivity> ActivityRequestTypes { get; set; } =
        new List<RequestTypeActivity>();

    public virtual ICollection<RequestTypeNewShift> NewShiftRequestTypes { get; set; } =
        new List<RequestTypeNewShift>();

    public virtual ICollection<RequestTypeNewShift> NewParallelShiftRequestTypes { get; set; } =
        new List<RequestTypeNewShift>();

    public virtual ICollection<RequestTypeNewShift> NewLabelShiftRequestTypes { get; set; } =
        new List<RequestTypeNewShift>();

    public virtual ICollection<RequestTypeNewShift> NewOvertimeShiftRequestTypes { get; set; } =
        new List<RequestTypeNewShift>();
}
