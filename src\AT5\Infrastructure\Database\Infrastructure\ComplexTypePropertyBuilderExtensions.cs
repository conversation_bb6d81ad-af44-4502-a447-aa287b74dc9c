﻿namespace AT.Infrastructure.Database.Infrastructure;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal static class ComplexTypePropertyBuilderExtensions
{
    /// <summary>
    /// Configures the column order for a property in a complex type using an <see cref="Iota"/> instance.
    /// <para>
    /// If <paramref name="iota"/> is <c>null</c>, this method does nothing and returns the original builder.
    /// Otherwise, it retrieves the next column order value from the <paramref name="iota"/> instance and applies it to the property.
    /// </para>
    /// </summary>
    /// <typeparam name="T">The type of the complex type property.</typeparam>
    /// <param name="complexTypePropertyBuilder">The <see cref="ComplexTypePropertyBuilder{T}"/> instance to configure.</param>
    /// <param name="iota">An <see cref="Iota"/> instance used to determine the column order, or <c>null</c> to skip configuration.</param>
    /// <returns>The configured <see cref="ComplexTypePropertyBuilder{T}"/> instance.</returns>
    public static ComplexTypePropertyBuilder<T> HasColumnOrder<T>(
        this ComplexTypePropertyBuilder<T> complexTypePropertyBuilder,
        Iota? iota
    )
    {
        if (iota is null)
        {
            return complexTypePropertyBuilder;
        }

        int columnOrder = iota.Next();
        complexTypePropertyBuilder.HasColumnOrder(columnOrder);
        return complexTypePropertyBuilder;
    }
}
