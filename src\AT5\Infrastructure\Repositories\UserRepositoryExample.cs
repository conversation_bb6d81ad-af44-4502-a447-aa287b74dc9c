namespace AT.Infrastructure.Repositories;

using AT.Core.Domain.UserAggregate;
using Core.Domain.Entities;
using Core.Domain.HelperObjects;
using Core.Repositories;
using Database;
using Microsoft.EntityFrameworkCore;

public class UserRepositoryExample(OrganizationDbContext _dbContext)
    : IUserWithNotesReader,
        IUserWriter,
        IUserNoteEditor
{
    public Task<List<UserNoteData>> GetUserNoteDataAsync(IReadOnlyCollection<UserId> userIds)
    {
        return _dbContext
            .Set<User>()
            .Where(user => userIds.Count == 0 || userIds.Contains(user.Id))
            .Select(user => new UserNoteData(user.Id, user.Username, user.Notes.Count))
            .ToListAsync();
    }

    public async Task AddUserAsync(User user)
    {
        await _dbContext.Set<User>().AddAsync(user);
        await _dbContext.SaveChangesAsync();
    }

    public async Task AddNoteAsync(UserId userId, string noteText)
    {
        var user = await _dbContext.Set<User>().FindAsync(userId);
        if (user is null)
        {
            throw new InvalidOperationException($"User with id {userId} not found.");
        }

        user.Notes.Add(new Note { Text = noteText, Created = DateTime.Now });

        await _dbContext.SaveChangesAsync();
    }
}
