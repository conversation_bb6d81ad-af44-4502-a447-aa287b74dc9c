﻿namespace AT.Infrastructure.Repositories;

using Core.Domain.UserAggregate;
using DataAccess;
using Database;

public sealed class UserRepository(OrganizationDbContext _dbContext)
    : EfRepository<User>(_dbContext),
        IUserRepository,
        IRepositoryWithFactoryMethod<IUserRepository>
{
    public static new IUserRepository Create(OrganizationDbContext dbContext)
    {
        return new UserRepository(dbContext);
    }
}
