namespace AT.Infrastructure.Repositories;

using System.Collections.Immutable;
using Core.Domain.ExampleEntities;
using Core.Repositories.Example;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

public class CachingAlgorithmLogRepositoryDecorator(
    IOptions<CachingSettings> _settings,
    IAlgorithmLogRepository _repository,
    IMemoryCache _cache
) : IAlgorithmLogRepository
{
    private const string cacheKey = nameof(CachingAlgorithmLogRepositoryDecorator);

    public async Task<ImmutableList<AlgorithmLog>> GetLogsAsync(CancellationToken token = default)
    {
        if (_cache.TryGetValue(cacheKey, out var cachedLogs))
        {
            return (ImmutableList<AlgorithmLog>)cachedLogs!;
        }

        var logs = await _repository.GetLogsAsync(token);
        _cache.Set(cacheKey, logs, TimeSpan.FromMinutes(_settings.Value.RetentionInMinutes));
        return logs;
    }

    public async Task AddLogAsync(AlgorithmLog log, CancellationToken token = default)
    {
        await _repository.AddLogAsync(log, token);
        _cache.Remove(cacheKey);
    }

    public async Task ClearAsync(CancellationToken token = default)
    {
        await _repository.ClearAsync(token);
        _cache.Remove(cacheKey);
    }
}
