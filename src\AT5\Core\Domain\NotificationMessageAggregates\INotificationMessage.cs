﻿namespace AT.Core.Domain.NotificationMessageAggregates;

public interface INotificationMessage
{
    long Id { get; }

    /// <summary>
    /// The notification message might be related to some entity.
    /// E.g., EmailMessage with EmailType set to CalculationEnd stores in here the ID of the calculation that ended.
    /// </summary>
    int? EntityId { get; }

    string? EventParameters { get; }

    DateTime Generated { get; }

    /// <summary>
    /// <see langword="null"/> means send the message immediately.
    /// </summary>
    DateTime? SendTime { get; }

    IEnumerable<INotificationMessageRecipient> Recipients { get; }
}
