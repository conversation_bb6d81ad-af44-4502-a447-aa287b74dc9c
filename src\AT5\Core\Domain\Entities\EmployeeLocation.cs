﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeeLocation
{
    public EmployeeLocationId Id { get; set; }

    public int? Min { get; set; }

    public int? Max { get; set; }

    public double? Priority { get; set; }

    public LocationId LocationId { get; set; }

    public UserId EmployeeId { get; set; }

    public SiteId SiteId { get; set; }

    public Validity Validity { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Location Location { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
