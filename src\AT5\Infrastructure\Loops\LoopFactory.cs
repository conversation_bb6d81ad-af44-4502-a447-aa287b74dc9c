﻿namespace AT.Infrastructure.Loops;

using System;
using AT.Core.Loops;
using AT.Infrastructure.DependencyInjection;

public class LoopFactory(IOrgServiceProvider _orgServiceProvider) : ILoopFactory
{
    public ILoop CreateTimedLoop<TLoopIteration, TLoopContext>(TimeSpan iterationDelay, TLoopContext loopContext)
        where TLoopIteration : class, ILoopIteration<TLoopContext>
    {
        return new Loop<TLoopIteration, TLoopContext>(_orgServiceProvider, iterationDelay, loopContext);
    }
}
