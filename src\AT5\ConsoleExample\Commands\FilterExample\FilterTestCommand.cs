﻿namespace AT.ConsoleExample.Commands.FilterExample;

using System;
using System.Diagnostics;
using System.Threading.Tasks;
using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;
using AT.Infrastructure.DataAccess;
using AT.Infrastructure.Database;
using AT.PrimitivesAT5.Ids;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Microsoft.EntityFrameworkCore;

[Command("filter-test", Description = "Tests different version of filtering by ids.")]
public class FilterTestCommand(OrganizationDbContextReadOnly _dbContext) : ICommand
{
    [CommandOption("skipFactor", Description = "Skip N user ids after every selected user id.")]
    public int SkipFactor { get; set; } = 0;

    [CommandOption("testRepeatCount", Description = "Specifies the number of test runs.")]
    public int TestRepeatCount { get; set; } = 10;

    public async ValueTask ExecuteAsync(IConsole console)
    {
        var userIds = await _dbContext.Set<User>().Select(x => x.Id).ToArrayAsync();
        userIds = SkipEvery(userIds, SkipFactor).ToArray();
        await console.Output.WriteLineAsync($"Selecting roster items for {userIds.Length} user ids.");

        var testedMethods = new Func<UserId[], Task<List<RosterItemDto>>>[]
        {
            LoadByWhereAsync,
            LoadByJoinAsync,
            LoadByFastAsync,
            LoadByFastAsNoTrackingAsync,
        };

        var calculator = new TotalDurationCalculator();

        for (int i = 0; i < TestRepeatCount; ++i)
        {
            foreach (var testedMethod in testedMethods)
            {
                await TestAndMeasureMethod(calculator, console, testedMethod.Method.Name, testedMethod, userIds);
            }
        }

        await calculator.DisplayTotalDurations(console);
    }

    private static async Task TestAndMeasureMethod(
        TotalDurationCalculator calculator,
        IConsole console,
        string methodName,
        Func<UserId[], Task<List<RosterItemDto>>> testedMethod,
        UserId[] userIds
    )
    {
        try
        {
            var started = Stopwatch.GetTimestamp();
            var items = await testedMethod(userIds);
            var elapsed = Stopwatch.GetElapsedTime(started);

            await console.Output.WriteLineAsync($"{methodName} loaded {items.Count} roster items.");
            await console.Output.WriteLineAsync($"{methodName} took {elapsed}.");

            calculator.AddDuration(methodName, elapsed);
        }
        catch
        {
            await console.Output.WriteLineAsync($"{methodName} threw an exception.");
            calculator.SetAsIncorrect(methodName);
        }
    }

    private async Task<List<RosterItemDto>> LoadByWhereAsync(UserId[] userIds)
    {
        var items = await _dbContext
            .Set<RosterItem>()
            .Where(x => userIds.Contains(x.EmployeeId))
            .Select(x => new RosterItemDto
            {
                Id = x.Id,
                Start = x.TotalInterval.Start,
                End = x.TotalInterval.End
            })
            .ToListAsync();
        return items;
    }

    private async Task<List<RosterItemDto>> LoadByJoinAsync(UserId[] userIds)
    {
        var itemsQuery = await _dbContext.SetByJoinAsync<RosterItem, UserId>(ri => ri.EmployeeId, userIds);
        var items = await itemsQuery
            .Select(x => new RosterItemDto
            {
                Id = x.Id,
                Start = x.TotalInterval.Start,
                End = x.TotalInterval.End
            })
            .ToListAsync();
        return items;
    }

    private async Task<List<RosterItemDto>> LoadByFastAsync(UserId[] userIds)
    {
        var itemsQuery = await _dbContext.SetFastAsync<RosterItem, UserId>(ri => ri.EmployeeId, userIds);
        var items = await itemsQuery
            .Select(x => new RosterItemDto
            {
                Id = x.Id,
                Start = x.TotalInterval.Start,
                End = x.TotalInterval.End
            })
            .ToListAsync();
        return items;
    }

    private async Task<List<RosterItemDto>> LoadByFastAsNoTrackingAsync(UserId[] userIds)
    {
        var itemsQuery = await _dbContext.SetFastAsync<RosterItem, UserId>(ri => ri.EmployeeId, userIds);
        var items = await itemsQuery
            .AsNoTracking()
            .Select(x => new RosterItemDto
            {
                Id = x.Id,
                Start = x.TotalInterval.Start,
                End = x.TotalInterval.End
            })
            .ToListAsync();
        return items;
    }

    private static IEnumerable<T> SkipEvery<T>(IEnumerable<T> source, int skipCount)
    {
        if (source is null)
        {
            throw new ArgumentNullException(nameof(source));
        }

        if (skipCount < 0)
        {
            throw new ArgumentOutOfRangeException(nameof(skipCount));
        }

        using var enumerator = source.GetEnumerator();

        while (enumerator.MoveNext())
        {
            yield return enumerator.Current;

            for (int i = 0; i < skipCount && enumerator.MoveNext(); i++)
            {
                // This is intentionally empty. Everything is happening in the for statement.
            }
        }
    }

    private sealed class TotalDurationCalculator
    {
        private readonly Dictionary<string, TimeSpan> _methodDurations = new();
        private readonly HashSet<string> _incorrectMethods = new();

        public void SetAsIncorrect(string name)
        {
            _incorrectMethods.Add(name);
        }

        public void AddDuration(string name, TimeSpan duration)
        {
            if (_methodDurations.TryAdd(name, duration))
            {
                return;
            }

            _methodDurations[name] += duration;
        }

        public async Task DisplayTotalDurations(IConsole console)
        {
            foreach (var (method, duration) in _methodDurations.OrderBy(x => x.Value))
            {
                bool isIncorrect = _incorrectMethods.Contains(method);
                await console.Output.WriteLineAsync(
                    $"Method: {method} - Total duration: {duration} {(isIncorrect ? "(INCORRECT)" : string.Empty)}"
                );
            }
        }
    }

    private sealed class RosterItemDto
    {
        public RosterItemId Id { get; set; }

        public DateTime Start { get; set; }

        public DateTime End { get; set; }
    }
}
