namespace AT.ConsoleExample.Commands.Translations;

using AT.SharedResources;
using AT.Translations;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

[Command("translations showcase", Description = "A brief example of using translations")]
public class TranslationsShowcaseCommand(ITranslator _translator) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        // Parameterless translation.
        await Yes(console);

        // Parametrized translation without formatters.
        await OperationSuccessful(console);

        // Parametrized translation 'ValueIsTooExtreme' with a formatter and the raw value included in the params.
        await ValueIsTooLow(console);
        await ValueIsTooHigh(console);
    }

    private async ValueTask Yes(IConsole console)
    {
        var translatedYesCurrentLocale = _translator.Translate<GlobalTranslations.Yes>();
        await console.Output.WriteLineAsync(translatedYesCurrentLocale.Value);

        var translatedYesCzechLocale = _translator.Translate<GlobalTranslations.Yes>(Language.Czech);
        await console.Output.WriteLineAsync(translatedYesCzechLocale.Value);
    }

    private async ValueTask OperationSuccessful(IConsole console)
    {
        var operationSuccessfulTranslation = new GlobalTranslations.OperationSuccessful(OperationName: "Test");

        var translatedOperationSuccessfulCurrentLocale = _translator.Translate(operationSuccessfulTranslation);
        await console.Output.WriteLineAsync(translatedOperationSuccessfulCurrentLocale.Value);

        var translatedOperationSuccessfulCzechLocale = _translator.Translate(
            operationSuccessfulTranslation,
            Language.Czech
        );
        await console.Output.WriteLineAsync(translatedOperationSuccessfulCzechLocale.Value);
    }

    private async ValueTask ValueIsTooLow(IConsole console)
    {
        var valueIsTooLowTranslation = new GlobalTranslations.ValueIsTooExtreme(Value: 2.71828);

        var translatedValueIsTooLowCurrentLocale = _translator.Translate(valueIsTooLowTranslation);
        await console.Output.WriteLineAsync(translatedValueIsTooLowCurrentLocale.Value);

        var translatedValueIsTooLowCzechLocale = _translator.Translate(valueIsTooLowTranslation, Language.Czech);
        await console.Output.WriteLineAsync(translatedValueIsTooLowCzechLocale.Value);
    }

    private async ValueTask ValueIsTooHigh(IConsole console)
    {
        var valueIsTooHighTranslation = new GlobalTranslations.ValueIsTooExtreme(Value: 314159.265358979);

        var translatedValueIsTooHighCurrentLocale = _translator.Translate(valueIsTooHighTranslation);
        await console.Output.WriteLineAsync(translatedValueIsTooHighCurrentLocale.Value);

        var translatedValueIsTooHighCzechLocale = _translator.Translate(valueIsTooHighTranslation, Language.Czech);
        await console.Output.WriteLineAsync(translatedValueIsTooHighCzechLocale.Value);
    }
}
