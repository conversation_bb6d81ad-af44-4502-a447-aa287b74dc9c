﻿namespace AT.NotificationsService.Emails;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.NotificationsService.Emails.Interfaces;
using AT.NotificationsService.General.Interfaces;
using AT.NotificationsService.NotificationsLimiting.Interfaces;
using AT.Utilities.Collections;
using AT.Utilities.Logging;
using AT.Utilities.Task;
using Microsoft.Extensions.Options;

public class EmailMessageService(
    IOrganizationMessageLimitingService _limitingService,
    IEmailMessageSender _emailMessageSender,
    IOptionsMonitor<EmailConfig> _configMonitor,
    IEmailLogger _emailLogger,
    ILogger<EmailMessageService> _logger
) : IMessageService<EmailMessage>
{
    public async Task<SendMessageResult<EmailMessage>> SendMessagesAsync(
        IReadOnlyCollection<EmailMessage> emailMessages,
        CancellationToken cancellationToken = default
    )
    {
        var rateLimitingConfig = await _limitingService.LoadRateLimitingConfigAsync(cancellationToken);

        var emailsAfterFiltering = rateLimitingConfig is not null
            ? await _limitingService
                .ApplyLimitingAsync(rateLimitingConfig, emailMessages, cancellationToken)
                .ToListAsync()
            : emailMessages;

        // Logs only email messages (and/or their recipients) if filtering occurred
        LogEmailsFilteredByRateLimiting(emailMessages, emailsAfterFiltering);

        // Everything was filtered, there is nothing to send
        if (emailsAfterFiltering.Count == 0)
        {
            return new SendMessageResult<EmailMessage>(FailedMessages: [], PartiallyFailedMessages: []);
        }

        Dictionary<EmailMessage, IReadOnlyCollection<EmailMessageRecipient>> successfulRecipientsPerEmailMessage = [];

        foreach (var emailMessage in emailsAfterFiltering)
        {
            int totalRecipientsCount = emailMessage.EmailMessageRecipients.Count;
            List<EmailMessageRecipient> successfulRecipients = new(totalRecipientsCount);

            foreach (var recipientList in FragmentAddressesPerEmail(emailMessage.EmailMessageRecipients))
            {
                _logger.Info(
                    "Sending email {TrackingId} to {RecipientsCount}/{TotalRecipientsCount} recipients...",
                    emailMessage.TrackingId,
                    recipientList.Count,
                    totalRecipientsCount
                );

                var result = await _emailMessageSender.SendEmailMessageAsync(
                    emailMessage,
                    recipientList,
                    cancellationToken
                );
                if (!result.Success)
                {
                    _logger.Error(
                        result.Exception,
                        "Sending email {TrackingId} failed with error message: {ErrorMessage}.",
                        emailMessage.TrackingId,
                        result.ErrorMessage
                    );

                    _emailLogger.LogMessageSendFailedForRecipients(
                        emailMessage,
                        recipientList,
                        eventReason: result.Exception?.Message
                    );

                    continue;
                }

                successfulRecipients.AddRange(recipientList);

                _logger.Info(
                    "Successfully sent email {TrackingId} to {RecipientsCount}/{TotalRecipientsCount} recipients...",
                    emailMessage.TrackingId,
                    recipientList.Count,
                    totalRecipientsCount
                );

                _emailLogger.LogMessageSendSuccessfulForRecipients(emailMessage, recipientList);
            }

            successfulRecipientsPerEmailMessage.Add(emailMessage, successfulRecipients);

            if (successfulRecipients.Count != totalRecipientsCount)
            {
                _logger.Error(
                    "Email {TrackingId} delivered to {ActualRecipientsCount}/{TotalRecipientsCount} recipients.",
                    emailMessage.TrackingId,
                    successfulRecipients.Count,
                    totalRecipientsCount
                );

                var eventReason =
                    $"Only {successfulRecipients.Count}/{totalRecipientsCount} recipients received the email";
                _emailLogger.LogMessageSendFailed(emailMessage, eventReason: eventReason);

                continue;
            }

            _logger.Info("Email {TrackingId} successfully sent to everyone.", emailMessage.TrackingId);

            _emailLogger.LogMessageSendSuccessful(emailMessage);
        }

        if (rateLimitingConfig is not null)
        {
            await _limitingService.StoreAsSentNotificationsAsync(
                rateLimitingConfig,
                successfulRecipientsPerEmailMessage,
                cancellationToken
            );
        }

        var failedMessages = emailsAfterFiltering
            .Where(e => successfulRecipientsPerEmailMessage[e].Count == 0)
            .ToList();

        var partiallyFailedMessages = emailsAfterFiltering
            .Where(e =>
            {
                int totalRecipients = e.EmailMessageRecipients.Count;
                int successfulRecipients = successfulRecipientsPerEmailMessage[e].Count;

                return 0 < successfulRecipients && successfulRecipients < totalRecipients;
            })
            .ToList();

        return new SendMessageResult<EmailMessage>(failedMessages, partiallyFailedMessages);
    }

    private void LogEmailsFilteredByRateLimiting(
        IReadOnlyCollection<EmailMessage> emails,
        IReadOnlyCollection<EmailMessage> filteredEmails
    )
    {
        var trackingIdToFilteredEmail = filteredEmails.ToDictionary(e => e.TrackingId);

        foreach (var email in emails)
        {
            // If email is not in filteredEmails, then it should not be sent to any recipient.
            if (!trackingIdToFilteredEmail.TryGetValue(email.TrackingId, out var filteredEmail))
            {
                _logger.Info("Email {TrackingId} was filtered by rate limiting.", email.TrackingId);

                _emailLogger.LogMessageFilteredByRateLimiting(email);

                continue;
            }

            // If email is in filtered emails, but the number of recipients is different, send it only to some recipients.
            if (filteredEmail.EmailMessageRecipients.Count != email.EmailMessageRecipients.Count)
            {
                _logger.Info("Email {TrackingId} recipients were filtered by rate limiting.", email.TrackingId);

                _emailLogger.LogMessageRecipientsFilteredByRateLimiting(
                    filteredEmail,
                    filteredEmail.EmailMessageRecipients
                );
            }
        }
    }

    private List<List<EmailMessageRecipient>> FragmentAddressesPerEmail(IEnumerable<EmailMessageRecipient> recipients)
    {
        return recipients.SplitToListsOfUniqueElements(_configMonitor.CurrentValue.MaxAddressesInSingleEmail);
    }
}
