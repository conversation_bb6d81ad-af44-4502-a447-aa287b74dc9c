﻿namespace AT.Core.Domain.Entities;

using Base;

public class Prediction
{
    public PredictionId Id { get; set; }

    public string Name { get; set; } = null!;

    public DateTime LogCreated { get; set; }

    public int LogCreatedBy { get; set; }

    public DateTime? LogChanged { get; set; }

    public int? LogChangedBy { get; set; }

    public string? Parameters { get; set; }

    public SiteId? SiteId { get; set; }

    public virtual ICollection<PredictionBin> PredictionBins { get; set; } = new List<PredictionBin>();

    public virtual ICollection<WorkOrderPrediction> WorkOrderPredictions { get; set; } =
        new List<WorkOrderPrediction>();

    public virtual ICollection<Event> Events { get; set; } = new List<Event>();

    public virtual Site Site { get; set; } = null!;
}
