namespace AT.Utilities.Validation;

using System.ComponentModel.DataAnnotations;
using System.Numerics;
using static Comparison;

/// <summary>
/// A custom validation attribute that compares a numeric value against a specified threshold using a given comparison operator.
/// </summary>
/// <typeparam name="TNumber">The numeric type of the value being validated.</typeparam>
/// <param name="_comparison">The comparison operator to use (e.g., greater than, less than).</param>
/// <param name="_threshold">The threshold value to compare against.</param>
public class ComparisonAttribute<TNumber>(Comparison _comparison, TNumber _threshold) : ValidationAttribute
    where TNumber : INumber<TNumber>
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not TNumber number)
        {
            return validationContext.CreateValidationResult($"The field must be of type {typeof(TNumber)}.");
        }

        return _comparison switch
        {
            GreaterThan
                => number > _threshold
                    ? ValidationResult.Success
                    : validationContext.CreateValidationResult($"The value must be greater than {_threshold}"),

            GreaterThanOrEqualTo
                => number >= _threshold
                    ? ValidationResult.Success
                    : validationContext.CreateValidationResult(
                        $"The value must be greater than or equal to {_threshold}"
                    ),

            LessThan
                => number < _threshold
                    ? ValidationResult.Success
                    : validationContext.CreateValidationResult($"The value must be less than {_threshold}"),

            LessThanOrEqualTo
                => number <= _threshold
                    ? ValidationResult.Success
                    : validationContext.CreateValidationResult($"The value must be less than or equal to {_threshold}"),

            _ => throw new NotImplementedException($"Comparison {_comparison} is not implemented.")
        };
    }
}
