﻿namespace AT.Utilities.Configuration;

using System;
using Microsoft.Extensions.Configuration;

public static class ConfigurationExtensions
{
    /// <summary>
    /// Retrieves a required configuration value of the specified type <typeparamref name="T"/>.
    /// </summary>
    /// <typeparam name="T">The type of the configuration value to retrieve.</typeparam>
    /// <param name="configuration">The <see cref="IConfiguration"/> instance to retrieve the value from.</param>
    /// <param name="key">The configuration key to retrieve the value for. If null, the name of the type
    /// <typeparamref name="T"/> is used as the key.</param>
    /// <returns>The configuration value of type <typeparamref name="T"/>.</returns>
    /// <exception cref="Exception">
    /// Thrown if the configuration value is null, indicating that the key was not found or the value was missing.
    /// </exception>
    public static T GetRequired<T>(this IConfiguration configuration, string? key = null)
    {
        var actualKey = key ?? typeof(T).Name;

        return configuration.GetSection(actualKey).Get<T>()
            ?? throw new Exception($"The configuration key '{actualKey}' is expected to have a non-null value.");
    }
}
