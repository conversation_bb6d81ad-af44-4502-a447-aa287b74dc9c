﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ output extension=".cs" #>
namespace AT.DataStructures.Time;

using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using AT.Utilities.Time.Utils;

<#
var intervalDefinitions = new[]
{
	new IntervalDefinition("DateInterval", false, BoundaryDataType.DateOnly),
	new IntervalDefinition("DateTimeInterval", false, BoundaryDataType.DateTime),
	new IntervalDefinition("OpenDateInterval", true, BoundaryDataType.DateOnly),
	new IntervalDefinition("OpenDateTimeInterval", true, BoundaryDataType.DateTime),
};

foreach (var intervalDefinition in intervalDefinitions)
{
#>
/// <summary>
/// Represents an interval with a <see cref="<#=intervalDefinition.GetFullDataType()#>"/> start and end.<#=intervalDefinition.IsOpen ? " Null represents infinity." : string.Empty#>
/// </summary>
[DataContract]
public readonly partial record struct <#=intervalDefinition.Name#>
{
	/// <summary>
    /// Gets the start of the interval.<#=intervalDefinition.IsOpen ? " Null represents infinity." : string.Empty#>
    /// </summary>
	[DataMember]
	public <#=intervalDefinition.GetFullDataType()#> Start { get; }

	/// <summary>
    /// Gets the end of the interval.<#=intervalDefinition.IsOpen ? " Null represents infinity." : string.Empty#>
    /// </summary>
	[DataMember]
	public <#=intervalDefinition.GetFullDataType()#> End { get; }

	/// <summary>
    /// Initializes a new instance of the <see cref="<#=intervalDefinition.Name#>"/> struct with the specified start and end.
    /// </summary>
    /// <param name="start">The start of the interval.</param>
    /// <param name="end">The end of the interval.</param>
	[JsonConstructor]
	public <#=intervalDefinition.Name#>(<#=intervalDefinition.GetFullDataType()#> start, <#=intervalDefinition.GetFullDataType()#> end)
	{
		Start = start;
		End = end;
	}

	/// <summary>
    /// Initializes a new instance of the <see cref="<#=intervalDefinition.Name#>"/> struct which represents a single day.
    /// </summary>
    /// <param name="day">The day of the interval.</param>
	public <#=intervalDefinition.Name#>(DateOnly day)
	{
		Start = day<#=intervalDefinition.BoundaryDataType == BoundaryDataType.DateTime ? ".ToDateTime(TimeOnly.MinValue)" : string.Empty#>;
		End = day<#=intervalDefinition.BoundaryDataType == BoundaryDataType.DateTime ? ".ToDateTime(TimeOnly.MaxValue).AddTicks(1)" : string.Empty#>;
	}

	/// <summary>
    /// Initializes a new instance of the <see cref="<#=intervalDefinition.Name#>"/> struct with an empty interval.
    /// </summary>
	public <#=intervalDefinition.Name#>()
	{
		Start = <#=intervalDefinition.GetDataType()#>.MaxValue;
		End = <#=intervalDefinition.GetDataType()#>.MinValue;
	}

	/// <summary>
    /// Creates a <see cref="<#=intervalDefinition.Name#>"/> representing the current day based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="<#=intervalDefinition.Name#>"/> representing today's date.</returns>
	/// <remarks>This should be moved to a service.</remarks>
	public static <#=intervalDefinition.Name#> CreateToday(TimeProvider timeProvider)
	{
		var today = timeProvider.GetLocalNow().DateTime.Date;
		return new <#=intervalDefinition.Name#>(DateOnly.FromDateTime(today));
	}

	/// <summary>
    /// Creates a <see cref="<#=intervalDefinition.Name#>"/> representing the current week (Monday to Sunday) based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="<#=intervalDefinition.Name#>"/> representing the current week.</returns>
	/// <remarks>This should be moved to a service.</remarks>
	public static <#=intervalDefinition.Name#> CreateCurrentWeek(TimeProvider timeProvider)
	{
		var today = timeProvider.GetLocalNow().DateTime.Date;
		var start = today.AddDays(-1 * (today.DayOfWeek - DayOfWeek.Monday));
		var end = start.AddDays(6);
		return new <#=intervalDefinition.Name#>(
			<#=intervalDefinition.BoundaryDataType == BoundaryDataType.DateOnly ? "DateOnly.FromDateTime(start)" : "start"#>,
			<#=intervalDefinition.BoundaryDataType == BoundaryDataType.DateOnly ? "DateOnly.FromDateTime(end)" : "end.AddDays(1)"#>);
	}

	/// <summary>
    /// Creates a <see cref="<#=intervalDefinition.Name#>"/> representing the current month based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="<#=intervalDefinition.Name#>"/> representing the current month.</returns>
	/// <remarks>This should be moved to a service.</remarks>
	public static <#=intervalDefinition.Name#> CreateCurrentMonth(TimeProvider timeProvider)
	{
		var now = timeProvider.GetLocalNow().DateTime;
		var start = new DateTime(now.Year, now.Month, 1);
		var end = start.AddMonths(1).AddDays(-1);
		return new <#=intervalDefinition.Name#>(
			<#=intervalDefinition.BoundaryDataType == BoundaryDataType.DateOnly ? "DateOnly.FromDateTime(start)" : "start"#>,
			<#=intervalDefinition.BoundaryDataType == BoundaryDataType.DateOnly ? "DateOnly.FromDateTime(end)" : "end.AddDays(1)"#>);
	}

	/// <summary>
    /// Gets a value indicating whether the interval is empty.
    /// </summary>
	public bool IsEmpty => <#=intervalDefinition.IsOpen ? "Start is not null && End is not null &&" : string.Empty#> Start <#=intervalDefinition.IsRightInclusive() ? ">" : ">="#> End;

	/// <summary>
    /// Gets a value indicating whether the interval is not empty.
    /// </summary>
	public bool IsNotEmpty => !IsEmpty;

	/// <summary>
    /// Determines whether the specified <see cref="DateTime"/> is within the interval.
    /// </summary>
    /// <param name="dateTime">The date and time to check.</param>
    /// <returns><c>true</c> if the interval contains the date and time; otherwise, <c>false</c>.</returns>
	public bool Contains(DateTime dateTime)
	{
<#
if (intervalDefinition.IsDateOnly)
{
#>
		var date = DateOnly.FromDateTime(dateTime);
		return Contains(date);
<#
}
else
{
#>
		return (<#=intervalDefinition.IsOpen ? "Start is null || " : string.Empty#>Start <= dateTime) && (<#=intervalDefinition.IsOpen ? "End is null || " : string.Empty#>End > dateTime);
<#
}
#>
	}

	/// <summary>
    /// Determines whether the day specified by <see cref="DateOnly"/> is within the interval.
    /// </summary>
    /// <param name="date">The date to check.</param>
    /// <returns><c>true</c> if the interval contains the date; otherwise, <c>false</c>.</returns>
	public bool Contains(DateOnly date)
	{
<#
if (intervalDefinition.IsDateOnly)
{
#>
		return (<#=intervalDefinition.IsOpen ? "Start is null || " : string.Empty#>Start <= date) && (<#=intervalDefinition.IsOpen ? "End is null || " : string.Empty#>End >= date);
<#
}
else
{
#>
		var dateInterval = new DateInterval(date);
		return Contains(dateInterval);
<#
}
#>
	}
<#
foreach (var otherIntervalDefinition in intervalDefinitions)
{
#>

	/// <summary>
    /// Determines whether the specified <see cref="<#=otherIntervalDefinition.Name#>"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
	public bool Contains(<#=otherIntervalDefinition.Name#> other)
	{
<#
	if (intervalDefinition.IsDateOnly)
	{
#>
		var otherStart = other.Start<#=otherIntervalDefinition.IsDateTime ? ".ToDateOnly()" : ""#>;
		var otherEnd = other.End<#=otherIntervalDefinition.IsDateTime ? ".ToDateOnly()" : ""#>;
		return (<#=intervalDefinition.IsOpen ? "Start is null || " : ""#>(<#=otherIntervalDefinition.IsOpen ? "otherStart is not null && " : ""#>Start <= otherStart)) && (<#=intervalDefinition.IsOpen ? "End is null || " : ""#>(<#=otherIntervalDefinition.IsOpen ? "otherEnd is not null && " : ""#>otherEnd <= End));
<#
	}
	else
	{
#>
		var otherStart = other.Start<#=otherIntervalDefinition.IsDateOnly ? ".ToDateTime(TimeOnly.MinValue)" : ""#>;
		var otherEnd = <#=$"other.End{(otherIntervalDefinition.IsDateOnly ? $".ToDateTime(TimeOnly.MinValue){(otherIntervalDefinition.IsOpen ? "?" : "")}.AddDays(1)" : "")};"#>
		return (<#=intervalDefinition.IsOpen ? "Start is null || " : ""#>(<#=otherIntervalDefinition.IsOpen ? "otherStart is not null && " : ""#>Start <= otherStart)) && (<#=intervalDefinition.IsOpen ? "End is null || " : ""#>(<#=otherIntervalDefinition.IsOpen ? "otherEnd is not null && " : ""#>otherEnd < End));
<#
	}
#>
	}
<#
}
foreach (var otherIntervalDefinition in intervalDefinitions)
{
	var combinedType = Tools.CombineTypes(intervalDefinition.Name, otherIntervalDefinition.Name);
#>

	/// <summary>
    /// Computes the intersection of this interval with another <see cref="<#=otherIntervalDefinition.Name#>"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="<#=combinedType#>"/> representing the intersection.</returns>
	public <#=combinedType#> Intersection(<#=otherIntervalDefinition.Name#> other)
	{
<#
	if (intervalDefinition.IsDateOnly && otherIntervalDefinition.IsDateOnly)
	{
#>
		return new <#=combinedType#>(DateOnlyUtils.Max(Start, other.Start), DateOnlyUtils.Min(End, other.End));
<#
	}
	else
	{
#>
		var thisStart = Start<#=intervalDefinition.IsDateOnly ? ".ToDateTime(TimeOnly.MinValue)" : ""#>;
		var thisEnd = <#=$"End{(intervalDefinition.IsDateOnly ? $".ToDateTime(TimeOnly.MinValue){(intervalDefinition.IsOpen ? "?" : "")}.AddDays(1)" : "")};"#>
		var otherStart = other.Start<#=otherIntervalDefinition.IsDateOnly ? ".ToDateTime(TimeOnly.MinValue)" : ""#>;
		var otherEnd = <#=$"other.End{(otherIntervalDefinition.IsDateOnly ? $".ToDateTime(TimeOnly.MinValue){(otherIntervalDefinition.IsOpen ? "?" : "")}.AddDays(1)" : "")};"#>
		return new <#=combinedType#>(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
<#
	}
#>
	}
<#
}
foreach (var otherIntervalDefinition in intervalDefinitions)
{
#>

	/// <summary>
    /// Determines whether this interval overlaps with another <see cref="<#=otherIntervalDefinition.Name#>"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
	public bool Overlaps(<#=otherIntervalDefinition.Name#> other)
	{
		return Intersection(other).IsNotEmpty;
	}
<#
}
#>
}

<#
}
#>

<#+
public class IntervalDefinition
{
	public string Name { get; }

	public bool IsOpen { get; }

	public BoundaryDataType BoundaryDataType { get; }

	public bool IsDateOnly
	{
		get
		{
			return BoundaryDataType == BoundaryDataType.DateOnly;
		}
	}

	public bool IsDateTime
	{
		get
		{
			return BoundaryDataType == BoundaryDataType.DateTime;
		}
	}

	public IntervalDefinition(string name, bool isOpen, BoundaryDataType boundaryDataType)
	{
		Name = name;
		IsOpen = isOpen;
		BoundaryDataType = boundaryDataType;
	}

	public string GetDataType()
	{
		return $"{BoundaryDataType}";
	}

	public string GetFullDataType()
	{
		return $"{BoundaryDataType}{(IsOpen ? "?" : string.Empty)}";
	}

	public bool IsRightInclusive()
	{
		return BoundaryDataType == BoundaryDataType.DateOnly;
	}
}

public enum BoundaryDataType
{
	DateTime,
	DateOnly,
}

public static class Tools
{
	public static string CombineTypes(string first, string second)
	{
		if (first == second)
		{
			return first;
		}

		var isOpen = first.Contains("Open") && second.Contains("Open");
		var dataType = first.Contains("DateTime") || second.Contains("DateTime") ? "DateTime" : "Date";
		return $"{(isOpen ? "Open" : string.Empty)}{dataType}Interval";
	}
}
#>
