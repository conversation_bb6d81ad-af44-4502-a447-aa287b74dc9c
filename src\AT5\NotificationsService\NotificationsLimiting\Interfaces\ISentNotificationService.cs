﻿namespace AT.NotificationsService.NotificationsLimiting.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.ConfigurationParameters;

public interface ISentNotificationService
{
    Task<IEnumerable<SentNotification>> GetSentEmailMessagesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates and stores <see cref="SentNotification"> entities from <paramref name="emailMessages"/>.
    /// </summary>
    Task StoreEmailMessagesAsSentNotificationsAsync(
        IReadOnlyDictionary<EmailMessage, IReadOnlyCollection<EmailMessageRecipient>> emailMessages,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// Delete all <see cref="SentNotification"> entities from the database until <paramref name="untilExclusive"/>.
    /// </summary>
    Task DeleteAsync(DateTime untilExclusive, CancellationToken cancellationToken = default);

    Task<NotificationsRateLimitingConfig?> GetRateLimitingConfigAsync(CancellationToken cancellationToken = default);
}
