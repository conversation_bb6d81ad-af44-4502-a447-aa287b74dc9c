﻿namespace AT.Primitives.Enums;

public enum RosterItemChangeSource
{
    Unknown = 0,

    // Whether by owner or not will be seen as the user who did it - if it is owner before and after -> then owner.
    Manually = 1,

    ApprovedChangeRequest = 2,

    ApprovedShiftTrade = 3,

    ApprovedRequest = 4,

    Suggestion = 5,

    Reservation = 6,

    Synchronization = 7,

    /// <summary>
    /// RosterItem altered during planning of breaks/lunches in an already existing roster.
    /// </summary>
    DailyPlanning = 8,

    /// <summary>
    /// Created during planning becuase of requirements.
    /// Currently if a shift is created during planning it has no ChangeLog.
    /// </summary>
    Planning = 9,

    /// <summary>
    /// Created during planning because of presence in input roster.
    /// Currently if a shift is created during planning it has no ChangeLog.
    /// </summary>
    PlanningCopiedFromInputRoster = 10,

    /// <summary>
    /// Shift was changed by configured automatic updates according to StatusParts.
    /// </summary>
    ShiftUpdateFromStatusParts = 11,

    AutomaticShiftCreation = 12,

    API = 13,

    RosterPublish = 14,

    Migration = 15,

    RosterPrePublish = 16,

    AutomaticJob = 16,
}
