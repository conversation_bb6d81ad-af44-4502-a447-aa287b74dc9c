﻿namespace AT.Shared.Formatting.Number.Formatters;

using System.Globalization;
using AT.Shared.Formatting.Number;
using AT.Translations;
using AT.Translations.Formatting.Numbers;

public sealed class DoubleFormatter(ITranslator _translator)
{
    public string Format(
        double value,
        MaxDecimalPlaces maxDecimalPlaces = MaxDecimalPlaces.Two,
        Language? language = null
    )
    {
        var format = maxDecimalPlaces switch
        {
            MaxDecimalPlaces.Zero => _translator.Translate<NumberFormatTranslations.DoubleZeroDecimalPlaces>(language),
            MaxDecimalPlaces.One => _translator.Translate<NumberFormatTranslations.DoubleOneDecimalPlace>(language),
            MaxDecimalPlaces.Two => _translator.Translate<NumberFormatTranslations.DoubleTwoDecimalPlaces>(language),
            MaxDecimalPlaces.Three
                => _translator.Translate<NumberFormatTranslations.DoubleThreeDecimalPlaces>(language),
            MaxDecimalPlaces.Six => _translator.Translate<NumberFormatTranslations.DoubleSixDecimalPlaces>(language),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {maxDecimalPlaces} is unknown"),
#else
            _ => _translator.Translate<NumberFormatTranslations.DoubleTwoDecimalPlaces>(language)
#endif
        };

        // We need to use CultureInfo in here because it is not possible to specify custom character
        // for the decimal separator in the format string.
        return value.ToString(format.Value, language?.CultureInfo ?? CultureInfo.CurrentUICulture);
    }
}
