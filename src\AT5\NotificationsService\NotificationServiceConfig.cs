﻿namespace AT.NotificationsService;

using AT.NotificationsService.Emails;
using AT.NotificationsService.PushNotifications.Firebase;
using AT.NotificationsService.PushNotifications.Tokens;

public class GeneralConfig
{
    public string? DbName { get; set; }

    public bool IsSingleOrg { get; set; }

    public bool IsMultitenant => string.IsNullOrEmpty(DbName) || !IsSingleOrg;
}

/// <summary>
/// Used by dependency injection. Therefore, despite of 0 references, this is not unused!
/// </summary>
public class ConnectionStrings
{
    public required string MasterDb { get; set; }

    public required string OrgDbTemplate { get; set; }
}

public class RateLimitingConfig
{
    public string CleanupJobTriggerCronExpression { get; set; } = "0 0 2 * * ?"; // Every day at 2 AM

    /// <summary>
    /// Do not delete records from SentNotifications with SendTime that is less than this many days back.
    /// E.g., 1 means keep today's and yesterday's records. Zero would mean only today's records.
    /// </summary>
    public int CleanupJobMaxDaysBack { get; set; } = 1;
}

public class PushNotificationConfig : IHasInstantSendingConfig, IHasScheduledSendingConfig
{
    public required FirebaseConfiguration FirebaseConfiguration { get; set; }

    public required string CsvLogsDirectory { get; set; }

    public InstantSendingConfig InstantSending { get; set; } = new();

    public ScheduledSendingConfig ScheduledSending { get; set; } = new();

    public PushNotificationTokensCleanupConfig TokensCleanupConfig { get; set; } = new();
}

public class EmailConfig : IHasInstantSendingConfig, IHasScheduledSendingConfig
{
    public required EmailSettings EmailSettings { get; set; }

    public required string CsvLogsDirectory { get; set; }

    /// <summary>
    /// Email attachmnets will be stored in this directory.
    /// </summary>
    public required string AttachmentContentsDirectory { get; set; }

    public InstantSendingConfig InstantSending { get; set; } = new();

    public ScheduledSendingConfig ScheduledSending { get; set; } = new();

    /// <summary>
    /// If there are more than this many email addresses, create multiple email messages, so that none of them exceeds this number of recipient email addresses.
    /// </summary>
    public int MaxAddressesInSingleEmail { get; set; } = 40;
}

public class InstantSendingConfig
{
    public int MaxMessagesToLoadAtOnce { get; set; } = 100;

    /// <summary>
    /// If there are no instant messages (SendTime == NULL), how long should we wait before asking the message provider (eg the database) again.
    /// </summary>
    public TimeSpan PauseBetweenMessageLoads { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// If an exception occurs in the loop, how much time should the execution wait before continuing.
    /// </summary>
    public TimeSpan PauseAfterException { get; set; } = TimeSpan.FromSeconds(5);
}

public class ScheduledSendingConfig
{
    /// <summary>
    /// If a message's SendTime is in the future but no more than the value specified in this parameter, we send it anyway.
    /// </summary>
    public TimeSpan SendInAdvanceTolerance { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// If a message's SendTime is older than the value in this parameter, we do not send it, we just delete it.
    /// </summary>
    public TimeSpan DeleteOnlyThreshold { get; set; } = TimeSpan.FromMinutes(5);
}

public interface IHasInstantSendingConfig
{
    InstantSendingConfig InstantSending { get; }
}

public interface IHasScheduledSendingConfig
{
    ScheduledSendingConfig ScheduledSending { get; }
}
