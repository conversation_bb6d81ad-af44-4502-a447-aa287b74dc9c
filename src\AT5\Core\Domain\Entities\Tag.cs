﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using Base;

public class Tag
{
    public TagId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool Disabled { get; set; }

    public bool IsGlobal { get; set; }

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
