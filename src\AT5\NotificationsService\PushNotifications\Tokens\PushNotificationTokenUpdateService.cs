﻿namespace AT.NotificationsService.PushNotifications.Tokens;

using AT.Core.DataAccess;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate.Specifications;
using AT.NotificationsService.PushNotifications.Interfaces;

// We have to use IRepositoryFactory<IPushNotificationTokenRepository>, becuase PushNotificationTokenUpdateService
// is used by PushNotificationTokensCleanupService and that is a singleton service.
internal class PushNotificationTokenUpdateService(
    IRepositoryFactory<IPushNotificationTokenRepository> _pushNotificationTokensRepoFactory
) : IPushNotificationTokenUpdateService
{
    public async Task<bool> DeleteTokens(IEnumerable<string> tokens)
    {
        try
        {
            using var pushNotificationTokensRepo = _pushNotificationTokensRepoFactory.Create();

            var pushNotificationTokens = await pushNotificationTokensRepo.ListAsync(
                new PushNotificationTokensForTokenStrings(tokens.ToList())
            );

            if (pushNotificationTokens.Count > 0)
            {
                pushNotificationTokensRepo.AddRangeForDelete(pushNotificationTokens);

                await pushNotificationTokensRepo.CommitAsync();
            }

            return true;
        }
        catch
        {
            return false;
        }
    }
}
