﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;

public class StatusType
{
    public StatusTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Abbreviation { get; set; } = null!;

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public int ShowPriority { get; set; }

    public int AppearancePriority { get; set; }

    public string? ExportCode { get; set; }

    public bool Disabled { get; set; }

    public virtual ICollection<Adherence> Adherences { get; set; } = new List<Adherence>();

    public virtual ICollection<ExternalStatus> ExternalStatuses { get; set; } = new List<ExternalStatus>();

    public virtual ICollection<StatusPart> StatusParts { get; set; } = new List<StatusPart>();

    public virtual ICollection<ExternalStatus> ClosingStatuses { get; set; } = new List<ExternalStatus>();
}
