﻿namespace AT.Translations;

public interface ITranslator
{
    /// <param name="language">If null, uses current thread's culture info.</param>
    TranslatedText Translate<T>(Language? language = null)
        where T : ITranslationNoParameters;

    /// <param name="language">If null, uses current thread's culture info.</param>
    TranslatedText Translate(ITranslationWithParameters translation, Language? language = null);
}
