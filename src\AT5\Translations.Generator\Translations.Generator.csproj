﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
		<Platforms>x64</Platforms>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\Translations\Translations.csproj" />

		<ProjectReference Include="..\DataStructures\DataStructures.csproj" />
		<ProjectReference Include="..\Utilities\Utilities.csproj" />
		<ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
		<ProjectReference Include="..\Primitives\Primitives.csproj" />

		<!--Generate translations for these projects.-->
		<ProjectReference Include="..\Shared\Shared.csproj" />
		<ProjectReference Include="..\SharedResources\SharedResources.csproj" />
		<ProjectReference Include="..\Core\Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="ResXResourceReader.NetStandard" />
		<PackageReference Include="Vogen" />
		<PackageReference Include="CliFx" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.local.json" Condition="'$(Configuration)'=='Debug'">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
</Project>
