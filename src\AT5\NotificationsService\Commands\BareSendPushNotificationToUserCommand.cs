namespace AT.NotificationsService.Commands;

using AT.Core.DataAccess;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate.Specifications;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.UserAggregate;
using AT.Core.Domain.UserAggregate.Specifications;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Primitives.Enums;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

/// <example>
/// BareSendPushNotificationToUser --ToId 2 --title Test1 --body Test2 --sendtomultiple false
/// </example>
[Command("BareSendPushNotificationToUser", Description = "Send a push notification via Firebase to user")]
public class BareSendPushNotificationToUserCommand(
    IGeneralQuery<User> _generalUsersQuery,
    IGeneralQuery<PushNotificationToken> _generalPnTokenQuery,
    IPushNotificationSender _pushNotificationSender
) : ICommand
{
    [CommandOption(nameof(ToId), Description = "toId or toUser has to be specified.")]
    public UserId? ToId { get; set; }

    [CommandOption(nameof(ToUsername), Description = "toId or toUser has to be specified.")]
    public string? ToUsername { get; init; }

    [CommandOption(nameof(Title), Description = "Title of the push notification.")]
    public required string Title { get; init; }

    [CommandOption(nameof(Body), Description = "Text of the push notification.")]
    public required string Body { get; init; }

    [CommandOption(
        nameof(SendToMultiple),
        Description = "Whether to use SendToMultiple or SendToSingle API method. Default is true -> SendToMultiple."
    )]
    public bool SendToMultiple { get; init; } = true;

    public async ValueTask ExecuteAsync(IConsole console)
    {
        if (ToId is null)
        {
            var recipientUserId = await _generalUsersQuery.FirstOrDefaultAsync(
                new UserIdByUsernameSpec(username: ToUsername!)
            );

            if (!recipientUserId.IsInitialized())
            {
                await console.Error.WriteLineAsync($"User with username {ToUsername} not found or there are multiple.");
                return;
            }

            ToId = recipientUserId;
        }

        PushNotification notification =
            new()
            {
                Title = Title,
                Body = Body,
                Type = PushNotificationType.BreakEnd,
                Generated = DateTime.Now,
            };

        var pnTokens = await _generalPnTokenQuery.ListAsync(
            new PushNotificationTokenStringsForUserSpec(userId: ToId.Value)
        );

        if (pnTokens.Count == 0)
        {
            await console.Output.WriteLineAsync($"No push notification tokens found for user with Id {ToId}");
            return;
        }

        if (SendToMultiple)
        {
            var response = await _pushNotificationSender.SendPushNotificationToMultipleAsync(pnTokens, notification);

            await console.Output.WriteLineAsync(
                $"Successfully sent notifications: {response!.SuccessCount}/{pnTokens.Count}"
            );
        }
        else
        {
            foreach (var token in pnTokens)
            {
                var result = await _pushNotificationSender.SendPushNotificationToSingleAsync(token, notification);
                var successInfoMessage = result.Success ? "succeeded" : "failed";

                await console.Output.WriteLineAsync($"Sending to {token} {successInfoMessage}.");
            }
        }
    }
}
