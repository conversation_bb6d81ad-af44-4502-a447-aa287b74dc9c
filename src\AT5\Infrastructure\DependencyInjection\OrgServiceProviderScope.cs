﻿namespace AT.Infrastructure.DependencyInjection;

using System;
using Autofac;

internal sealed class OrgServiceProviderScope(ILifetimeScope _lifetimeScope) : IOrgServiceProviderScope
{
    public TService Resolve<TService>()
        where TService : class
    {
        return _lifetimeScope.Resolve<TService>();
    }

    public object Resolve(Type serviceType)
    {
        return _lifetimeScope.Resolve(serviceType);
    }

    public void Dispose()
    {
        _lifetimeScope.Dispose();
    }
}
