﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Calculation"/> in the database.
/// </summary>
internal static class CalculationGroupModelBuilder
{
    public static void BuildCalculationGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Calculation>(entity =>
        {
            entity.ToTable("Calculations");
            entity.HasKey(e => e.Id);

            entity.HasIndex(e => e.InputRosterId, "IX_FK_CalculationInputRoster");

            entity.HasIndex(e => e.OutputRosterId, "IX_FK_CalculationOutputRoster");

            entity.HasIndex(e => e.CalculationTypeId, "IX_FK_CalculationTypeCalculation");

            entity.HasIndex(e => e.CreatedById, "IX_FK_CalculationUser");

            entity.HasIndex(e => e.PlanningPeriodId, "IX_FK_PlanningPeriodCalculation");

            entity
                .HasIndex(e => new { e.Status, e.OutputRosterId }, "IX_NC_Calculations_RosterChangeFetch")
                .IncludeProperties(e => e.Finished);

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Finished).IsStoredAsDateTime();
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Started).IsStoredAsDateTime();
            entity.Property(e => e.Submitted).IsStoredAsDateTime();

            entity.Property(e => e.Id).ValueGeneratedNever();

            entity
                .HasOne(d => d.CalculationType)
                .WithMany(p => p.Calculations)
                .HasForeignKey(d => d.CalculationTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CalculationTypeCalculation");

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.Calculations)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CalculationUser");

            entity
                .HasOne(d => d.InputRoster)
                .WithMany(p => p.CalculationInputRosters)
                .HasForeignKey(d => d.InputRosterId)
                .HasConstraintName("FK_CalculationInputRoster");

            entity
                .HasOne(d => d.OutputRoster)
                .WithMany(p => p.CalculationOutputRosters)
                .HasForeignKey(d => d.OutputRosterId)
                .HasConstraintName("FK_CalculationOutputRoster");

            entity
                .HasOne(d => d.PlanningPeriod)
                .WithMany(p => p.Calculations)
                .HasForeignKey(d => d.PlanningPeriodId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PlanningPeriodCalculation");

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.Calculations)
                .UsingEntity<Dictionary<string, object>>(
                    "CalculationSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_CalculationSites_Site"),
                    l =>
                        l.HasOne<Calculation>()
                            .WithMany()
                            .HasForeignKey("CalculationsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_CalculationSites_Calculation"),
                    j =>
                    {
                        j.HasKey("CalculationsId", "SitesId");
                        j.ToTable("CalculationSites");
                        j.HasIndex(["SitesId"], "IX_FK_CalculationSites_Site");
                        j.IndexerProperty<CalculationId>("CalculationsId").HasColumnName("Calculations_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<CalculationError>(entity =>
        {
            entity.ToTable("CalculationErrors");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CalculationId, "IX_FK_CalculationCalculationError");

            entity
                .HasOne(d => d.Calculation)
                .WithMany(p => p.CalculationErrors)
                .HasForeignKey(d => d.CalculationId)
                .HasConstraintName("FK_CalculationCalculationError");
        });

        modelBuilder.Entity<CalculationLog>(entity =>
        {
            entity.ToTable("CalculationLogs");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CalculationId, "IX_FK_CalculationLogCalculation");

            entity.Property(e => e.CalculationId).HasColumnName("Calculation_Id");
            entity.Property(e => e.Finished).IsStoredAsDateTime();
            entity.Property(e => e.Queued).IsStoredAsDateTime();
            entity.Property(e => e.Started).IsStoredAsDateTime();

            entity
                .HasOne(d => d.Calculation)
                .WithMany(p => p.CalculationLogs)
                .HasForeignKey(d => d.CalculationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CalculationLogCalculation");
        });

        modelBuilder.Entity<CalculationLogEntry>(entity =>
        {
            entity.ToTable("CalculationLogEntries");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CalculationLogId, "IX_FK_CalculationLogEntryCalculationLog");

            entity.Property(e => e.CalculationLogId).HasColumnName("CalculationLog_Id");
            entity.Property(e => e.TimeStamp).IsStoredAsDateTime();

            entity
                .HasOne(d => d.CalculationLog)
                .WithMany(p => p.CalculationLogEntries)
                .HasForeignKey(d => d.CalculationLogId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CalculationLogEntryCalculationLog");
        });

        modelBuilder.Entity<CalculationPhase>(entity =>
        {
            entity.ToTable("CalculationPhases");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity
                .HasMany(d => d.RosteringRules)
                .WithMany(p => p.CalculationPhases)
                .UsingEntity<Dictionary<string, object>>(
                    "CalculationPhaseRosteringRule",
                    r =>
                        r.HasOne<RosteringRule>()
                            .WithMany()
                            .HasForeignKey("RosteringRulesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_CalculationPhaseRosteringRule_RosteringRule"),
                    l =>
                        l.HasOne<CalculationPhase>()
                            .WithMany()
                            .HasForeignKey("CalculationPhasesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_CalculationPhaseRosteringRule_CalculationPhase"),
                    j =>
                    {
                        j.HasKey("CalculationPhasesId", "RosteringRulesId");
                        j.ToTable("CalculationPhaseRosteringRule");
                        j.HasIndex(["RosteringRulesId"], "IX_FK_CalculationPhaseRosteringRule_RosteringRule");
                        j.IndexerProperty<CalculationPhaseId>("CalculationPhasesId")
                            .HasColumnName("CalculationPhases_Id");
                        j.IndexerProperty<RosteringRuleId>("RosteringRulesId").HasColumnName("RosteringRules_Id");
                    }
                );
        });

        modelBuilder.Entity<CalculationType>(entity =>
        {
            entity.ToTable("CalculationTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.CalculationTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "CalculationTypeSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_CalculationTypeSite_Site"),
                    l =>
                        l.HasOne<CalculationType>()
                            .WithMany()
                            .HasForeignKey("CalculationTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_CalculationTypeSite_CalculationType"),
                    j =>
                    {
                        j.HasKey("CalculationTypesId", "SitesId");
                        j.ToTable("CalculationTypeSite");
                        j.HasIndex(["SitesId"], "IX_FK_CalculationTypeSite_Site");
                        j.IndexerProperty<CalculationTypeId>("CalculationTypesId").HasColumnName("CalculationTypes_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<CalculationTypePhase>(entity =>
        {
            entity.ToTable("CalculationTypePhases");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CalculationPhaseId, "IX_FK_CalculationPhaseCalculationTypePhase");

            entity.HasIndex(e => e.CalculationTypeId, "IX_FK_CalculationTypeCalculationPhaseSet");

            entity
                .HasOne(d => d.CalculationPhase)
                .WithMany(p => p.CalculationTypePhases)
                .HasForeignKey(d => d.CalculationPhaseId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CalculationPhaseCalculationTypePhase");

            entity
                .HasOne(d => d.CalculationType)
                .WithMany(p => p.CalculationTypePhases)
                .HasForeignKey(d => d.CalculationTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CalculationTypeCalculationPhaseSet");
        });

        modelBuilder.Entity<RosteringRule>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RosteringRules");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Disabled).HasColumnOrder(iota);
            entity.Property(e => e.Type).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);

            entity.HasTimeInterval(e => e.TimeInterval, iota);
            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.Parameters).HasColumnOrder(iota);
            entity.Property(e => e.PastValidity).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);
        });

        modelBuilder.Entity<RosteringRuleFilters2>(entity =>
        {
            entity.ToTable("RosteringRuleFilters2");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.FilterId, "IX_FK_FilterRosteringRuleFilters");

            entity.HasIndex(e => e.RosteringRuleId, "IX_FK_RosteringRuleRosteringRuleFilters");

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.RosteringRuleFilters2s)
                .HasForeignKey(d => d.FilterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FilterRosteringRuleFilters");

            entity
                .HasOne(d => d.RosteringRule)
                .WithMany(p => p.RosteringRuleFilters2s)
                .HasForeignKey(d => d.RosteringRuleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosteringRuleRosteringRuleFilters");
        });
    }
}
