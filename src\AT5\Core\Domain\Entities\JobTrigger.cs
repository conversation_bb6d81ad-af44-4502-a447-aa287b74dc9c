﻿namespace AT.Core.Domain.Entities;

using Primitives.Enums;

public class JobTrigger
{
    public JobTriggerId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public string Parameters { get; set; } = null!;

    public JobTriggerType Type { get; set; }

    public bool Disabled { get; set; }

    public JobId JobId { get; set; }

    public string? RunParameters { get; set; }

    public virtual Job Job { get; set; } = null!;
}
