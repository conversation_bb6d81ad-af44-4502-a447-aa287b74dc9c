﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to activities in the database.
/// </summary>
internal static class ActivityGroupModelBuilder
{
    public static void BuildActivityGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<ActivitySkill>(entity =>
        {
            entity.ToTable("ActivitySkills");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.ActivityId, "IX_FK_ActivityTypeActivitySkill");

            entity.HasIndex(e => e.SkillId, "IX_FK_SkillActivitySkill");

            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity
                .HasOne(d => d.Activity)
                .WithMany(p => p.ActivitySkills)
                .HasForeignKey(d => d.ActivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActivityTypeActivitySkill");

            entity
                .HasOne(d => d.Skill)
                .WithMany(p => p.ActivitySkills)
                .HasForeignKey(d => d.SkillId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SkillActivitySkill");
        });

        modelBuilder.Entity<Adherence>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Adherences");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasIndex(e => e.RosterItemPartTypeId, "IX_FK_AdherenceRosterItemPartType");
            entity.Property(e => e.RosterItemPartTypeId).HasColumnOrder(iota);

            entity.HasIndex(e => e.StatusTypeId, "IX_FK_AdherenceStatusType");
            entity.Property(e => e.StatusTypeId).HasColumnOrder(iota);

            entity.HasIndex(e => e.AdherenceStatusId, "IX_FK_AdherenceAdherenceStatus");
            entity.Property(e => e.AdherenceStatusId).HasColumnOrder(iota);

            entity.HasTimeInterval(e => e.LowerLimit, iota);

            entity.HasTimeInterval(e => e.UpperLimit, iota);

            entity
                .HasOne(d => d.AdherenceStatus)
                .WithMany(p => p.Adherences)
                .HasForeignKey(d => d.AdherenceStatusId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AdherenceAdherenceStatus");

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.Adherences)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .HasConstraintName("FK_AdherenceRosterItemPartType");

            entity
                .HasOne(d => d.StatusType)
                .WithMany(p => p.Adherences)
                .HasForeignKey(d => d.StatusTypeId)
                .HasConstraintName("FK_AdherenceStatusType");
        });

        modelBuilder.Entity<AdherenceStatus>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("AdherenceStatuses");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnOrder(iota).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Abbreviation).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.Icon).HasMaxLength(50).HasColumnOrder(iota);
        });

        modelBuilder.Entity<BudgetActivity>(entity =>
        {
            entity.ToTable("BudgetActivities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.SkillId, "IX_FK_BudgetActivitySkill");

            entity.HasIndex(e => e.ActivityId, "IX_FK_BudgetActivityType");

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);

            entity
                .HasOne(d => d.Activity)
                .WithMany(p => p.BudgetActivities)
                .HasForeignKey(d => d.ActivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BudgetActivityType");

            entity
                .HasOne(d => d.Skill)
                .WithMany(p => p.BudgetActivities)
                .HasForeignKey(d => d.SkillId)
                .HasConstraintName("FK_BudgetActivitySkill");
        });

        modelBuilder.Entity<BudgetHoursAllowance>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("BudgetHoursAllowances");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.BudgetActivityId).HasColumnOrder(iota);
            entity.HasIndex(e => e.BudgetActivityId, "IX_FK_BudgetHoursAllowanceBudgetActivity");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_BudgetHoursAllowanceSite");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.BudgetActivity)
                .WithMany(p => p.BudgetHoursAllowances)
                .HasForeignKey(d => d.BudgetActivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BudgetHoursAllowanceBudgetActivity");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.BudgetHoursAllowances)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BudgetHoursAllowanceSite");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.BudgetActivityId,
                        e.SiteId,
                        e.Validity.Interval.Start,
                        e.Validity.Interval.End
                    },
                    "NCI_BudgetActivityId_SiteId_ValidityStart_ValidityEnd"
                )
                .WithSettings(
                    new
                    {
                        PAD_INDEX = "OFF",
                        STATISTICS_NORECOMPUTE = "OFF",
                        SORT_IN_TEMPDB = "OFF",
                        DROP_EXISTING = "OFF",
                        ONLINE = "OFF",
                        ALLOW_ROW_LOCKS = "ON",
                        ALLOW_PAGE_LOCKS = "ON",
                        OPTIMIZE_FOR_SEQUENTIAL_KEY = "OFF"
                    }
                );

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.SiteId,
                        e.Validity.Interval.Start,
                        e.Validity.IsInvalid
                    },
                    "NCI_SiteId_ValidityStart_ValidityInvalid_INCL"
                )
                .IncludeProperties(e => e.Validity.Interval.End)
                .WithSettings(
                    new
                    {
                        PAD_INDEX = "OFF",
                        STATISTICS_NORECOMPUTE = "OFF",
                        SORT_IN_TEMPDB = "OFF",
                        DROP_EXISTING = "OFF",
                        ONLINE = "OFF",
                        ALLOW_ROW_LOCKS = "ON",
                        ALLOW_PAGE_LOCKS = "ON",
                        OPTIMIZE_FOR_SEQUENTIAL_KEY = "OFF"
                    }
                );
        });

        modelBuilder.Entity<ExternalStatus>(entity =>
        {
            entity.ToTable("ExternalStatuses");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.StatusTypeId, "IX_FK_StatusTypeExternalStatus");

            entity.Property(e => e.ExternalCode).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ReasonCode).HasMaxLength(50);

            entity
                .HasOne(d => d.StatusType)
                .WithMany(p => p.ExternalStatuses)
                .HasForeignKey(d => d.StatusTypeId)
                .HasConstraintName("FK_StatusTypeExternalStatus");
        });

        modelBuilder.Entity<RosterItemPartRule>(entity =>
        {
            entity.ToTable("RosterItemPartRules");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.RosterItemPartTypeId, "IX_FK_RosterItemPartTypeRosterItemPartRule");

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.RosterItemPartRules)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemPartTypeRosterItemPartRule");

            entity
                .HasMany(d => d.RosterItemPartTypes)
                .WithMany(p => p.RosterItemPartRuleTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "RosterItemPartRuleRosterItemPartType",
                    r =>
                        r.HasOne<RosterItemPartType>()
                            .WithMany()
                            .HasForeignKey("RosterItemPartTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RosterItemRuleRosterItemPartType_RosterItemPartType"),
                    l =>
                        l.HasOne<RosterItemPartRule>()
                            .WithMany()
                            .HasForeignKey("RosterItemRuleRosterItemPartTypeRosterItemPartTypeId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RosterItemRuleRosterItemPartType_RosterItemRule"),
                    j =>
                    {
                        j.HasKey("RosterItemRuleRosterItemPartTypeRosterItemPartTypeId", "RosterItemPartTypesId");
                        j.ToTable("RosterItemPartRuleRosterItemPartType");
                        j.HasIndex(
                            ["RosterItemPartTypesId"],
                            "IX_FK_RosterItemRuleRosterItemPartType_RosterItemPartType"
                        );
                        j.IndexerProperty<RosterItemPartRuleId>("RosterItemRuleRosterItemPartTypeRosterItemPartTypeId")
                            .HasColumnName("RosterItemRuleRosterItemPartType_RosterItemPartType_Id");
                        j.IndexerProperty<RosterItemPartTypeId>("RosterItemPartTypesId")
                            .HasColumnName("RosterItemPartTypes_Id");
                    }
                );
        });

        modelBuilder.Entity<RosterItemPartType>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RosterItemPartTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Abbreviation).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.WorkingTime).HasColumnOrder(iota);
            entity.Property(e => e.ShowPriority).HasColumnOrder(iota);
            entity.Property(e => e.AppearancePriority).HasColumnOrder(iota);
            entity.Property(e => e.ExportCode).HasMaxLength(20).HasColumnOrder(iota);
            entity.Property(e => e.Disabled).HasColumnOrder(iota);
            entity.Property(e => e.IsGlobal).HasColumnOrder(iota);
            entity.Property(e => e.ShowInLegend).HasColumnOrder(iota);
            entity.Property(e => e.Rank).HasColumnOrder(iota);
            entity.Property(e => e.ImplicitAssignment).HasColumnOrder(iota);
            entity.HasRecurrence(e => e.Recurrence, iota);
        });

        modelBuilder.Entity<RosterItemPartTypeFilter>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RosterItemPartTypeFilters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.FilterId).HasColumnOrder(iota);
            entity.HasIndex(e => e.FilterId, "IX_FK_RosterItemPartTypeFilterFilter2");

            entity.Property(e => e.RosterItemPartTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RosterItemPartTypeId, "IX_FK_RosterItemPartTypeFilterRosterItemPartType");

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.Parameters).HasMaxLength(256).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.RosterItemPartTypeFilters)
                .HasForeignKey(d => d.FilterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemPartTypeFilterFilter2");

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithMany(p => p.RosterItemPartTypeFilters)
                .HasForeignKey(d => d.RosterItemPartTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemPartTypeFilterRosterItemPartType");
        });

        modelBuilder.Entity<ActivityType>(entity =>
        {
            entity.ToTable("RosterItemPartTypes_ActivityType");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.CcId).HasColumnOrder(0);
            entity.Property(e => e.RequestTypeId).HasColumnOrder(1);
            entity.Property(e => e.Productive).HasColumnOrder(2);
            entity.Property(e => e.Id).HasColumnOrder(3);

            entity.Property(e => e.CcId).HasMaxLength(255);

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithOne()
                .HasForeignKey<ActivityType>(d => d.Id)
                .HasConstraintName("FK_ActivityType_inherits_RosterItemPartType");
        });

        modelBuilder.Entity<BreakType>(entity =>
        {
            entity.ToTable("RosterItemPartTypes_BreakType");
            entity.HasKey(e => e.Id);

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithOne()
                .HasForeignKey<BreakType>(d => d.Id)
                .HasConstraintName("FK_BreakType_inherits_RosterItemPartType");
        });

        modelBuilder.Entity<CampaignType>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RosterItemPartTypes_CampaignType");
            entity.HasKey(e => e.Id);

            entity.HasDateInterval(e => e.Interval, iota);
            entity.Property(e => e.Contacts).HasColumnOrder(iota);
            entity.Property(e => e.Priority).HasColumnOrder(iota);
            entity.Property(e => e.DurationEffective).HasColumnOrder(iota);
            entity.Property(e => e.DurationIneffective).HasColumnOrder(iota);
            entity.Property(e => e.DurationNegative).HasColumnOrder(iota);
            entity.Property(e => e.RepetitionCount).HasColumnOrder(iota);
            entity.Property(e => e.TargetContacted).HasColumnOrder(iota);
            entity.Property(e => e.TargetEffective).HasColumnOrder(iota);
            entity.Property(e => e.RateContacted1).HasColumnOrder(iota);
            entity.Property(e => e.RateContacted2).HasColumnOrder(iota);
            entity.Property(e => e.RateContacted3).HasColumnOrder(iota);
            entity.Property(e => e.RateEffective1).HasColumnOrder(iota);
            entity.Property(e => e.RateEffective2).HasColumnOrder(iota);
            entity.Property(e => e.RateEffective3).HasColumnOrder(iota);

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithOne()
                .HasForeignKey<CampaignType>(d => d.Id)
                .HasConstraintName("FK_CampaignType_inherits_RosterItemPartType");
        });

        modelBuilder.Entity<StatusType>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("StatusTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.Abbreviation).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.ShowPriority).HasColumnOrder(iota);
            entity.Property(e => e.AppearancePriority).HasColumnOrder(iota);
            entity.Property(e => e.ExportCode).HasMaxLength(20).HasColumnOrder(iota);
            entity.Property(e => e.Disabled).HasColumnOrder(iota);

            entity
                .HasMany(d => d.ClosingStatuses)
                .WithMany(p => p.ClosingExternalStatusesExternalStatuses)
                .UsingEntity<Dictionary<string, object>>(
                    "ClosingExternalStatus",
                    r =>
                        r.HasOne<ExternalStatus>()
                            .WithMany()
                            .HasForeignKey("ClosingStatusesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ClosingExternalStatuses_ExternalStatus"),
                    l =>
                        l.HasOne<StatusType>()
                            .WithMany()
                            .HasForeignKey("ClosingExternalStatusesExternalStatusId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ClosingExternalStatuses_StatusType"),
                    j =>
                    {
                        j.HasKey("ClosingExternalStatusesExternalStatusId", "ClosingStatusesId");
                        j.ToTable("ClosingExternalStatuses");
                        j.HasIndex(["ClosingStatusesId"], "IX_FK_ClosingExternalStatuses_ExternalStatus");
                        j.IndexerProperty<StatusTypeId>("ClosingExternalStatusesExternalStatusId")
                            .HasColumnName("ClosingExternalStatuses_ExternalStatus_Id");
                        j.IndexerProperty<ExternalStatusId>("ClosingStatusesId").HasColumnName("ClosingStatuses_Id");
                    }
                );
        });

        modelBuilder.Entity<TimeOffType>(entity =>
        {
            entity.ToTable("RosterItemPartTypes_TimeOffType");
            entity.HasKey(e => e.Id);

            entity.HasIndex(e => e.ShiftInteraction, "NC_ShiftInteraction");

            entity.Property(e => e.ShiftInteraction).HasColumnOrder(0);
            entity.Property(e => e.Id).HasColumnOrder(1);

            entity
                .HasOne(d => d.RosterItemPartType)
                .WithOne()
                .HasForeignKey<TimeOffType>(d => d.Id)
                .HasConstraintName("FK_TimeOffType_inherits_RosterItemPartType");
        });
    }
}
