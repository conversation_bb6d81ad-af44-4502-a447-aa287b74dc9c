﻿namespace AT.Core.Jobs.DurableJobs;

using AT.Core.Jobs.DurableJobs.Identifiers;
using AT.Utilities.Cron;

public interface IDurableJobSeriesScheduler
{
    Task<IJobIdentifier> AddDurableJobSeries(JobSeriesDefinition jobSeriesDefinition, CancellationToken ct);

    Task<ITriggerIdentifier?> ScheduleDurableJobSeries(
        IJobIdentifier jobIdentifier,
        string triggerName,
        CronExpression cron,
        CancellationToken ct
    );

    Task<bool> UnscheduleDurableJobSeries(
        IReadOnlyCollection<ITriggerIdentifier> triggerIdentifiers,
        CancellationToken ct
    );

    Task<bool> DeleteDurableJobSeries(IReadOnlyCollection<IJobIdentifier> jobIdentifiers, CancellationToken ct);
}
