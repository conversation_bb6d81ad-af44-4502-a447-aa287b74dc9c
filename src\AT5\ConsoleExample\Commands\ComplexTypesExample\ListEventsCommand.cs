﻿namespace AT.ConsoleExample.Commands.ComplexTypesExample;

using System.Threading.Tasks;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Microsoft.EntityFrameworkCore;

[Command("ct list-events", Description = "Lists all events")]
public class ListEventsCommand(OrganizationDbContext _dbContext) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        var events = await _dbContext.Set<Event>().ToListAsync();

        foreach (var e in events)
        {
            await console.Output.WriteLineAsync($"[{e.Id}] Start: {e.Period.Start} End: {e.Period.End}");
        }
    }
}
