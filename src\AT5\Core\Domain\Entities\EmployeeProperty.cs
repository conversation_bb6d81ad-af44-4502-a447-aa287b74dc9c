﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeeProperty
{
    public EmployeePropertyId Id { get; set; }

    public string? Value { get; set; }

    public Validity Validity { get; set; }

    public PropertyId PropertyId { get; set; }

    public UserId EmployeeId { get; set; }

    public SiteId SiteId { get; set; }

    public PropertyState State { get; set; }

    public bool Locked { get; set; }

    public DateTime StateChanged { get; set; }

    public UserId StateChangedById { get; set; }

    public DateTime ValueChanged { get; set; }

    public UserId ValueChangedById { get; set; }

    public byte Source { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Property Property { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual Site Site { get; set; } = null!;

    public virtual User StateChangedBy { get; set; } = null!;

    public virtual User ValueChangedBy { get; set; } = null!;
}
