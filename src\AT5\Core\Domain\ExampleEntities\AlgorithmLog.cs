namespace AT.Core.Domain.ExampleEntities;

using Vogen;

[ValueObject<string>]
public readonly partial struct UserName
{
    public static Validation Validate(string value)
    {
        return !string.IsNullOrEmpty(value) ? Validation.Ok : Validation.Invalid("Username cannot be null or empty.");
    }
}

[ValueObject<DateTimeOffset>]
public readonly partial struct StartTime;

[ValueObject<DateTimeOffset>]
public readonly partial struct EndTime;

public record AlgorithmLog(UserName User, StartTime Start, EndTime End, AlgorithmInput Input, AlgorithmOutput Output);
