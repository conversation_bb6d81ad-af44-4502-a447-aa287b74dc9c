﻿namespace AT.Core.Domain.EmployeeRosterAggregate;

public interface IEmployeeRosterService
{
    // TODO: Should we also pass planningPeriodSiteId? EmployeeRosterFinish does have SiteId property but the usage is questionable.
    /// <summary>
    /// The returned intervals are connected, i.e., it will never return intervals like { (1.1.2000, 31.1.2000), (1.2.2000, 28.2.2000) },
    /// it would return just { (1.1.2000, 28.2.2000) }.
    /// </summary>
    Task<List<DateInterval>> GetFinishedIntervalsAsync(
        UserId employeeId,
        CancellationToken cancellationToken = default
    );

    Task<Dictionary<UserId, List<DateInterval>>> GetFinishedIntervalsPerEmployeeAsync(
        IReadOnlyCollection<UserId> employeeIds,
        CancellationToken cancellationToken = default
    );
}
