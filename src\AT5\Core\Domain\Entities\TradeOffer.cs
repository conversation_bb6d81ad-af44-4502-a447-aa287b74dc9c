﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using Base;

public class TradeOffer
{
    public TradeOfferId Id { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public DateTime Created { get; set; }

    public ChangeRequestId? ChangeRequestId { get; set; }

    public RosterItemId? RosterItemId2 { get; set; }

    public virtual ChangeRequest? ChangeRequest { get; set; }

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual RosterItem RosterItem { get; set; } = null!;

    public virtual RosterItem? RosterItem2 { get; set; }

    public virtual ICollection<TradeAnswer> TradeAnswers { get; set; } = new List<TradeAnswer>();
}
