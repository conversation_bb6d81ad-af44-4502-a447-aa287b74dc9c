﻿namespace AT.Utilities.Text;

using System.Text;

public static class CodePagesUtils
{
    // https://learn.microsoft.com/en-us/dotnet/api/system.text.codepagesencodingprovider.instance?view=net-8.0
    // Add an encoding provider for code pages supported in the desktop .NET Framework but not in the current .NET Framework platform.
    // Fix Log4net initialization exception: No data is available for encoding 437.
    public static void AddDesktopCodePages()
    {
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
    }
}
