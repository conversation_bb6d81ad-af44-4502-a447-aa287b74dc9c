﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class Employee
{
    public UserId Id { get; set; }

    public virtual User User { get; set; } = null!;

    public string? CcId { get; set; }

    public string? PersonalId { get; set; }

    public int? SystemId { get; set; }

    public DateOnly? EntryDate { get; set; }

    public DateOnly? FinishDate { get; set; }

    public DateOnly? StartPlanningDate { get; set; }

    public bool IsActive { get; set; }

    public double Holiday { get; set; }

    public int Seniority { get; set; }

    public int Rank { get; set; }

    public bool AllowReservations { get; set; }

    public bool SendCallendarEvents { get; set; }

    public double HolidayOver { get; set; }

    public bool UseAsTemplate { get; set; }

    public int? PositionId { get; set; }

    public string? TitleBefore { get; set; }

    public string? TitleAfter { get; set; }

    public Gender? Gender { get; set; }

    public DateOnly? DateOfBirth { get; set; }

    public AgencyId? AgencyId { get; set; }

    public bool MultiEmployee { get; set; }

    public UserId? MainEmployeeId { get; set; }

    public RosterItemType? RosterItemType { get; set; }

    public EmployeeRosterType EmployeeRosterType { get; set; }

    public DateOnly? ExpectedContractEnd { get; set; }

    public bool OmitImplicitShifts { get; set; }

    public bool OmitImplicitActivities { get; set; }

    public virtual Agency? Agency { get; set; }

    public virtual ICollection<Change> ChangeNewEmployees { get; set; } = new List<Change>();

    public virtual ICollection<Change> ChangeOldEmployees { get; set; } = new List<Change>();

    public virtual ICollection<EmployeeActivity> EmployeeActivities { get; set; } = new List<EmployeeActivity>();

    public virtual ICollection<EmployeeBalance> EmployeeBalances { get; set; } = new List<EmployeeBalance>();

    public virtual ICollection<EmployeeContract> EmployeeContracts { get; set; } = new List<EmployeeContract>();

    public virtual ICollection<EmployeeLocation> EmployeeLocations { get; set; } = new List<EmployeeLocation>();

    public virtual ICollection<EmployeeOvertimeSuggestion> EmployeeOvertimeSuggestions { get; set; } =
        new List<EmployeeOvertimeSuggestion>();

    public virtual ICollection<EmployeePosition> EmployeePositions { get; set; } = new List<EmployeePosition>();

    public virtual ICollection<EmployeeProperty> EmployeeProperties { get; set; } = new List<EmployeeProperty>();

    public virtual ICollection<EmployeePublicRoster> EmployeePublicRosters { get; set; } =
        new List<EmployeePublicRoster>();

    public virtual ICollection<EmployeeQueueProperty> EmployeeQueueProperties { get; set; } =
        new List<EmployeeQueueProperty>();

    public virtual ICollection<EmployeeRequestType> EmployeeRequestTypes { get; set; } =
        new List<EmployeeRequestType>();

    public virtual ICollection<EmployeeRosterFinish> EmployeeRosterFinishes { get; set; } =
        new List<EmployeeRosterFinish>();

    public virtual ICollection<EmployeeShiftTemplate> EmployeeShiftTemplates { get; set; } =
        new List<EmployeeShiftTemplate>();

    public virtual ICollection<EmployeeSkill> EmployeeSkills { get; set; } = new List<EmployeeSkill>();

    public virtual ICollection<EmployeeTimePreference> EmployeeTimePreferences { get; set; } =
        new List<EmployeeTimePreference>();

    public virtual ICollection<Employee> InverseMainEmployee { get; set; } = new List<Employee>();

    public virtual Employee? MainEmployee { get; set; }

    public virtual ICollection<EmployeeNote> EmployeeNotes { get; set; } = new List<EmployeeNote>();

    public virtual ICollection<RosterNote> RosterNotes { get; set; } = new List<RosterNote>();

    public virtual ICollection<Request> Requests { get; set; } = new List<Request>();

    public virtual ICollection<Reservation> Reservations { get; set; } = new List<Reservation>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<StatusPart> StatusParts { get; set; } = new List<StatusPart>();

    public virtual ICollection<WorkMission> WorkMissions { get; set; } = new List<WorkMission>();

    public virtual ICollection<WorkOrderItem> WorkOrderItems { get; set; } = new List<WorkOrderItem>();

    public virtual ICollection<RoleDelegation> RoleDelegations { get; set; } = new List<RoleDelegation>();

    public virtual ICollection<RoleDelegation> DelegatedRoleDelegations { get; set; } = new List<RoleDelegation>();

    public virtual ICollection<RoleDelegation> ExplicitlyGovernedByRoleDelegations { get; set; } =
        new List<RoleDelegation>();

    public virtual ICollection<RequestLimit> RequestLimits { get; set; } = new List<RequestLimit>();

    public virtual ICollection<Requirement> Requirements { get; set; } = new List<Requirement>();

    public virtual ICollection<TaskTodo> Tasks { get; set; } = new List<TaskTodo>();
}
