﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class CalculationLogEntry
{
    public CalculationLogEntryId Id { get; set; }

    public string Type { get; set; } = null!;

    public CalculationLogGroup Group { get; set; }

    public ErrorLevel Level { get; set; }

    public string Parameters { get; set; } = null!;

    public DateTime TimeStamp { get; set; }

    public CalculationLogId CalculationLogId { get; set; }

    public virtual CalculationLog CalculationLog { get; set; } = null!;
}
