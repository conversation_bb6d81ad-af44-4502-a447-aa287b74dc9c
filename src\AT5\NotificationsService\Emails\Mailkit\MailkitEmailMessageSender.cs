﻿namespace AT.NotificationsService.Emails.Mailkit;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.NotificationsService.Emails.Interfaces;
using AT.NotificationsService.General;
using AT.Primitives.Enums;
using AT.Utilities.Logging;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;

public class MailkitEmailMessageSender(
    IOptionsMonitor<EmailConfig> _emailConfigMonitor,
    ILogger<MailkitEmailMessageSender> _logger
) : IEmailMessageSender
{
    /// <param name="recipients">If <see langword="null"/>, uses recipients directly from <paramref name="emailMessage"/></param>
    public async Task<OperationResult> SendEmailMessageAsync(
        EmailMessage emailMessage,
        IEnumerable<EmailMessageRecipient>? recipients = null,
        CancellationToken cancellationToken = default
    )
    {
        try
        {
            var emailSettings = _emailConfigMonitor.CurrentValue.EmailSettings;

            recipients ??= emailMessage.EmailMessageRecipients;

            using var message = new MimeMessage();

            AddRecipients(message, recipients);

            message.From.Add(new MailboxAddress(emailSettings.FromName, emailSettings.FromAddress));
            message.Subject = emailMessage.Subject;

            var bodyBuilder = new BodyBuilder();

            if (emailMessage.BodyIsHtml)
            {
                bodyBuilder.HtmlBody = emailMessage.Body;
            }
            else
            {
                bodyBuilder.TextBody = emailMessage.Body;
            }

            foreach (var attachment in emailMessage.EmailMessageAttachments ?? [])
            {
                if (attachment.ContentType is null)
                {
                    bodyBuilder.Attachments.Add(attachment.FileName, attachment.Content);
                    continue;
                }

                var contentType = ContentType.Parse(attachment.ContentType);
                bodyBuilder.Attachments.Add(attachment.FileName, attachment.Content, contentType);
            }

            message.Body = bodyBuilder.ToMessageBody();

            using var client = new SmtpClient();

            if (emailSettings.IgnoreServerCertificationValidation)
            {
                client.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyerrors) => true;
            }

            var socketOption = emailSettings.EnableSsl ? SecureSocketOptions.StartTls : SecureSocketOptions.None;
            await client.ConnectAsync(emailSettings.Host, emailSettings.Port, socketOption, cancellationToken);

            if (
                !string.IsNullOrWhiteSpace(emailSettings.Username) && !string.IsNullOrWhiteSpace(emailSettings.Password)
            )
            {
                await client.AuthenticateAsync(emailSettings.Username, emailSettings.Password, cancellationToken);
            }

            await client.SendAsync(message, cancellationToken);
            await client.DisconnectAsync(true, cancellationToken);

            return new OperationResult(Success: true);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "SendMailAsync failed with exception");

            return new OperationResult(Success: false, ErrorMessage: ex.Message, Exception: ex);
        }
    }

    private static void AddRecipients(MimeMessage message, IEnumerable<EmailMessageRecipient> recipients)
    {
        foreach (var recipient in recipients.Where(recipient => IsEmailAddressAllowed(recipient.Address)))
        {
            switch (recipient.RecipientType)
            {
                case RecipientType.To:
                    message.To.Add(new MailboxAddress(null, recipient.Address));
                    break;
                case RecipientType.Cc:
                    message.Cc.Add(new MailboxAddress(null, recipient.Address));
                    break;
                case RecipientType.Bcc:
                    message.Bcc.Add(new MailboxAddress(null, recipient.Address));
                    break;
            }
        }
    }

    private static bool IsEmailAddressAllowed(string emailAddress)
    {
        if (string.IsNullOrWhiteSpace(emailAddress))
        {
            return false;
        }

#if !DEBUG
        return true;
#else
        var domain = emailAddress.Substring(emailAddress.IndexOf('@') + 1);
        return domain == "aristotelos.cz"
            || domain == "msps.cz"
            || domain == "aristotelos.cloud"
            || domain == "aristotelos.com"
            || emailAddress == "<EMAIL>"
            || emailAddress == "<EMAIL>";
#endif
    }
}
