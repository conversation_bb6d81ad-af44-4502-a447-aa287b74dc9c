namespace AT.ConsoleExample.Commands.AlgorithmExample;

using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Repositories.Example;
using Spectre.Console;

[Command("algorithm list", Description = "Lists the algorithm history")]
public class ListCommand(IAlgorithmLogRepository _repository) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        var logs = await _repository.GetLogsAsync();

        var userStyle = new Style(foreground: Color.Green);
        var inputStyle = new Style(foreground: Color.Blue);
        var outputStyle = new Style(foreground: Color.Yellow);
        var startStyle = new Style(foreground: Color.Cyan2);
        var endStyle = new Style(foreground: Color.Cyan3);

        var table = new Table();

        table.AddColumn("User");
        table.AddColumn("Input");
        table.AddColumn("Output");
        table.AddColumn("Start Time");
        table.AddColumn("End Time");

        foreach (var log in logs)
        {
            table.AddRow(
                new Markup(log.User.ToString(), userStyle),
                new Markup(log.Input.ToString(), inputStyle),
                new Markup(log.Output.ToString(), outputStyle),
                new Markup(log.Start.ToString(), startStyle),
                new Markup(log.End.ToString(), endStyle)
            );
        }

        AnsiConsole.Write(table);
    }
}
