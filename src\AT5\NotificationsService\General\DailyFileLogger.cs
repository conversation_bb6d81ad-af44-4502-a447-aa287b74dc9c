﻿namespace AT.NotificationsService.General;

using System.Text;
using AT.Utilities.Logging;

// FUTURE: Clean up the file and move it to the AT5/Utilities. Also, add interface.
// FUTURE: Use options pattern instead of putting specific settings in the constructor.

/// <param name="_fileNameTemplate">May contain substring %DATE% which is replaced by the current date when creating a new file.</param>
public class DailyFileLogger(
    ILogger<DailyFileLogger> _logger,
    string _directoryPathRelativeOrAbsolute,
    string _fileNameTemplate,
    Action<StreamWriter>? _onFileOpen = null
) : IDisposable
{
    public event Action<StreamWriter>? OnFileOpen = _onFileOpen;

    private readonly string _directoryPath = Path.GetFullPath(
        _directoryPathRelativeOrAbsolute,
        AppContext.BaseDirectory
    );
    private readonly object _writeMutex = new();

    private StreamWriter? _writer;
    private DateTime? _fileDate;
    private bool _disposed;

    public void WriteLine(string text)
    {
        lock (_writeMutex)
        {
            if (!UpdateFile())
            {
                _logger.Warn("The following text was not logged because UpdateFile failed: {UnloggedText}", text);
                return;
            }

            _writer?.WriteLine(text);
        }
    }

    private bool UpdateFile()
    {
        var today = DateTime.Today;

        if (_writer is not null && _fileDate == today)
        {
            return true;
        }

        if (!EnsureDirectoryExists())
        {
            return false;
        }

        var pathToLogFileTemplate = Path.GetFullPath(
            Path.Combine(_directoryPath, _fileNameTemplate),
            AppContext.BaseDirectory
        );
        var pathToLogFile = GetFilePath(pathToLogFileTemplate, today);

        try
        {
            _writer = new StreamWriter(pathToLogFile, append: true, encoding: Encoding.UTF8);

            lock (_writeMutex)
            {
                OnFileOpen?.Invoke(_writer);
            }

            _writer.AutoFlush = true;
            _fileDate = today;

            return true;
        }
        catch (Exception e)
        {
            _logger.Error(e, "Unable to log into file {PathToLogFile}", pathToLogFile);
            _writer = null;
            return false;
        }
    }

    private bool EnsureDirectoryExists()
    {
        if (Directory.Exists(_directoryPath))
        {
            return true;
        }

        try
        {
            Directory.CreateDirectory(_directoryPath);

            _logger.Info("Created directory {CreatedDirectory}", _directoryPath);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to created directory {DirectoryPath} with exception", _directoryPath);
        }

        return true;
    }

    private static string GetFilePath(string basePath, DateTime date)
    {
        if (basePath.Contains("%DATE%"))
        {
            var dateString = date.ToString("yyyy-MM-dd");
            return basePath.Replace("%DATE%", dateString);
        }

        return basePath;
    }

    ~DailyFileLogger() => Dispose(false);

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
        {
            return;
        }

        lock (_writeMutex)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _writer?.Dispose();
                    _writer = null;
                }

                _disposed = true;
            }
        }
    }
}
