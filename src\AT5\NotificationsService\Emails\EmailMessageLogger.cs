﻿namespace AT.NotificationsService.Emails;

using System.Diagnostics.CodeAnalysis;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.MasterDomain;
using AT.Utilities.Logging;
using Microsoft.Extensions.Options;
using NotificationsService.Emails.Interfaces;
using NotificationsService.General;

public sealed class EmailMessageLogger : IEmailLogger
{
    private readonly OrganizationInfo _organizationInfo;
    private readonly ILogger<EmailMessageLogger> _logger;
    private readonly ILogger<DailyFileLogger> _loggerForDailyFileLogger;

    private string _organizationAttachmentContentsDirectory;
    private DailyFileLogger _dailyFileLogger;
    private DateTime _todayCached;

    /// <summary>
    /// When CsvLogsDirectory changes, we want to re-create dailyFileLogger with the new path. However, dailyFilterLogger
    /// might be used at that moment, so we cannot dispose it. Therefore, we use locking.
    /// </summary>
    private readonly object _dailyFileLoggerLock = new();

    public EmailMessageLogger(
        ILogger<EmailMessageLogger> logger,
        ILogger<DailyFileLogger> loggerForDailyFileLogger,
        IOptionsMonitor<EmailConfig> emailConfigMonitor,
        OrganizationInfo organizationInfo
    )
    {
        _logger = logger;
        _loggerForDailyFileLogger = loggerForDailyFileLogger;
        _organizationInfo = organizationInfo;

        UseCsvLogsDirectory(
            emailConfigMonitor.CurrentValue.CsvLogsDirectory,
            emailConfigMonitor.CurrentValue.AttachmentContentsDirectory
        );

        emailConfigMonitor.OnChange(x => UseCsvLogsDirectory(x.CsvLogsDirectory, x.AttachmentContentsDirectory));
    }

    [MemberNotNull(nameof(_dailyFileLogger), nameof(_organizationAttachmentContentsDirectory))]
    public void UseCsvLogsDirectory(string csvLogsDirectory, string attachmentContentsDirectory)
    {
        lock (_dailyFileLoggerLock)
        {
            _dailyFileLogger?.Dispose();

            var orgCsvLogsDirectory = Path.GetFullPath(
                Path.Combine(csvLogsDirectory, _organizationInfo.Name),
                AppContext.BaseDirectory
            );
            _dailyFileLogger = new DailyFileLogger(
                _loggerForDailyFileLogger,
                orgCsvLogsDirectory,
                "emails_%DATE%.csv",
                _onFileOpen: LogHeader
            );
        }

        _organizationAttachmentContentsDirectory = Path.GetFullPath(
            Path.Combine(attachmentContentsDirectory, _organizationInfo.Name),
            AppContext.BaseDirectory
        );
    }

    public void LogMessageLoad(EmailMessage message)
    {
        LogEmailEvent(
            message,
            "Loaded",
            recipients: message.EmailMessageRecipients,
            attachments: message.EmailMessageAttachments
        );

        foreach (var attachment in message.EmailMessageAttachments ?? Enumerable.Empty<EmailMessageAttachment>())
        {
            if (!LogAttachment(message, attachment))
            {
                _logger.Error(
                    "Failed to log attachment {FileName} of the email {TrackingId} at {AttachmentsDirectory}.",
                    attachment.FileName,
                    message.TrackingId,
                    _organizationAttachmentContentsDirectory
                );
            }
        }
    }

    public void LogMessageProcessed(EmailMessage message)
    {
        LogEmailEvent(message, "Processed");
    }

    public void LogMessageSendSuccessful(EmailMessage message)
    {
        LogEmailEvent(message, "SendSuccessful");
    }

    public void LogMessageSendSuccessfulForRecipients(
        EmailMessage message,
        IEnumerable<EmailMessageRecipient> recipients
    )
    {
        LogEmailEvent(message, "SendSuccessfulForRecipients", recipients: recipients);
    }

    public void LogMessageSendFailed(EmailMessage message, string? eventReason = null)
    {
        LogEmailEvent(
            message,
            "SendFailed",
            recipients: message.EmailMessageRecipients,
            attachments: message.EmailMessageAttachments,
            eventReason: eventReason
        );
    }

    public void LogMessageSendFailedForRecipients(
        EmailMessage message,
        IEnumerable<EmailMessageRecipient> recipients,
        string? eventReason = null
    )
    {
        LogEmailEvent(message, "SendFailedForRecipients", recipients: recipients, eventReason: eventReason);
    }

    public void LogMessageExpired(EmailMessage message)
    {
        LogEmailEvent(
            message,
            "Expired",
            recipients: message.EmailMessageRecipients,
            attachments: message.EmailMessageAttachments
        );
    }

    public void LogMessageFilteredByRateLimiting(EmailMessage message)
    {
        LogEmailEvent(
            message,
            "FilteredByRateLimiting",
            recipients: message.EmailMessageRecipients,
            attachments: message.EmailMessageAttachments
        );
    }

    public void LogMessageRecipientsFilteredByRateLimiting(
        EmailMessage message,
        IEnumerable<EmailMessageRecipient> recipients
    )
    {
        LogEmailEvent(message, "RecipientsFilteredByRateLimiting", recipients: recipients);
    }

    private static void LogHeader(StreamWriter writer)
    {
        if (writer.BaseStream.Position == 0)
        {
            writer.WriteLine(
                "Time;Event;EventReason;Id;TrackingId;Type;EntityId;EventParameters;Subject;Body;BodyIsHtml;Generated;SendTime;UserIds;Addresses;RecipientTypes;AttachmentFileNames;AttachmentContentTypes;"
            );
        }
    }

    private bool LogAttachment(EmailMessage email, EmailMessageAttachment attachment)
    {
        if (_organizationAttachmentContentsDirectory is null)
        {
            return false;
        }

        var today = DateTime.Today;
        var currentDirectory = today.ToString("yyyy-MM-dd");
        var attachmentsDirectoryPath = Path.Combine(_organizationAttachmentContentsDirectory, currentDirectory);

        // If today hasn't changed since the last time this method was called, we avoid the system call to `Directory.Exists`
        if (_todayCached != today && !Directory.Exists(attachmentsDirectoryPath))
        {
            try
            {
                Directory.CreateDirectory(attachmentsDirectoryPath);
                _logger.Info("Created directory for attachments at {AttachmentsDirectory}", attachmentsDirectoryPath);
                _todayCached = today;
            }
            catch (Exception ex)
            {
                _logger.Warn(
                    ex,
                    "Failed to create directory for attachments at {AttachmentsDirectory}",
                    attachmentsDirectoryPath
                );
                return false;
            }
        }

        var pathToAttachmentFile = Path.Combine(attachmentsDirectoryPath, $"{email.TrackingId}_{attachment.FileName}");
        File.WriteAllBytes(pathToAttachmentFile, attachment.Content);

        return true;
    }

    private void LogEmailEvent(
        EmailMessage email,
        string logEvent,
        IEnumerable<EmailMessageRecipient>? recipients = null,
        IEnumerable<EmailMessageAttachment>? attachments = null,
        string? eventReason = null
    )
    {
        var now = DateTime.Now.ToString("o");
        var eventReasonStr = eventReason ?? string.Empty;
        var id = email.Id;
        var trackingId = email.TrackingId;
        var typeStr = email.EmailType.ToString();
        var entityId = email.EntityId;
        var eventParams = email.EventParameters;
        var subject = email.Subject;
        var body = email.Body;
        var bodyIsHtml = email.BodyIsHtml;
        var generated = email.Generated.ToString("o");
        var sendTime = email.SendTime?.ToString("o") ?? string.Empty;

        recipients ??= [];
        var userIds = string.Join(", ", recipients.Select(r => r.UserId));
        var addresses = string.Join(", ", recipients.Select(r => r.Address));
        var recipientTypes = string.Join(", ", recipients.Select(r => r.RecipientType.ToString()));

        attachments ??= [];
        var attachmentNames = string.Join(", ", attachments.Select(a => a.FileName));
        var attachmentContentTypes = string.Join(", ", attachments.Select(a => a.ContentType));

        var msgDataStr =
            $"{now};{logEvent};{eventReasonStr};{id};{trackingId};{typeStr};{entityId};{eventParams};{subject};{body};{bodyIsHtml};{generated};{sendTime}";
        var recipientDataStr = $"{userIds};{addresses};{recipientTypes}";
        var attachmentDataStr = $"{attachmentNames};{attachmentContentTypes}";

        var record = $"{msgDataStr};{recipientDataStr};{attachmentDataStr}".Replace("\r", "\\r").Replace("\n", "\\n"); // Each record in CSV must always be a single line.

        lock (_dailyFileLoggerLock)
        {
            _dailyFileLogger.WriteLine(record);
        }
    }

    public void Dispose()
    {
        _dailyFileLogger.Dispose();
    }
}
