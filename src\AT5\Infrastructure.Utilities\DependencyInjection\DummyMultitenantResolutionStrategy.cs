﻿namespace AT.Infrastructure.Utilities.DependencyInjection;

using Autofac.Multitenant;

/// <summary>
/// A dummy implementation of AutoFac's multitenant container resolution strategy. Some projects (e.g. NotificationsService)
/// works for all tenants at the same time (unlike e.g. ASP.NET Core, where we typically want to resolve current tenant from HttpContext.Request).
/// However, AutoFac's MultitenantContainer constructor requires that we provide some resolution strategy (even though we won't use CurrentTenant methods).
/// Thus, we can use this dummy implementation.
/// </summary>
public class DummyMultitenantResolutionStrategy : ITenantIdentificationStrategy
{
    public bool TryIdentifyTenant(out object? tenantId)
    {
        tenantId = null;
        return false;
    }
}
