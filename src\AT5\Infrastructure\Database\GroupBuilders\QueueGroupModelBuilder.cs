﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Queue"/> in the database.
/// </summary>
internal static class QueueGroupModelBuilder
{
    public static void BuildQueueGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<DataEvent>(entity =>
        {
            entity.ToTable("DataEvents");
            entity.HasKey(e => e.Type);

            entity.Property(e => e.Type).ValueGeneratedNever();
            entity.Property(e => e.DateTime).IsStoredAsDateTime();
        });

        modelBuilder.Entity<Event>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Events");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Note).HasMaxLength(1024);

            entity.HasDateTimeInterval(e => e.Period, iota);

            entity
                .HasMany(d => d.Predictions)
                .WithMany(p => p.Events)
                .UsingEntity<Dictionary<string, object>>(
                    "EventPrediction",
                    r =>
                        r.HasOne<Prediction>()
                            .WithMany()
                            .HasForeignKey("PredictionsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_EventPrediction_Prediction"),
                    l =>
                        l.HasOne<Event>()
                            .WithMany()
                            .HasForeignKey("EventsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_EventPrediction_Event"),
                    j =>
                    {
                        j.HasKey("EventsId", "PredictionsId");
                        j.ToTable("EventPrediction");
                        j.HasIndex(["PredictionsId"], "IX_FK_EventPrediction_Prediction");
                        j.IndexerProperty<EventId>("EventsId").HasColumnName("Events_Id");
                        j.IndexerProperty<PredictionId>("PredictionsId").HasColumnName("Predictions_Id");
                    }
                );
        });

        modelBuilder.Entity<Prediction>(entity =>
        {
            entity.ToTable("Predictions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.LogChanged).IsStoredAsDateTime().HasColumnName("Log_Changed");
            entity.Property(e => e.LogChangedBy).HasColumnName("Log_ChangedBy");
            entity.Property(e => e.LogCreated).IsStoredAsDateTime().HasColumnName("Log_Created");
            entity.Property(e => e.LogCreatedBy).HasColumnName("Log_CreatedBy");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity.HasIndex(e => e.SiteId, "IX_FK_PredictionSite");
            entity
                .HasOne(d => d.Site)
                .WithMany()
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PredictionSite");
        });

        modelBuilder.Entity<PredictionBin>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("PredictionBins");
            entity.Property(e => e.Ticks).HasColumnOrder(iota);
            entity.Property(e => e.PredictionId).HasColumnOrder(iota);
            entity.Property(e => e.Offered).HasColumnOrder(iota);
            entity.Property(e => e.ServiceTimeMu).HasColumnOrder(iota);
            entity.Property(e => e.QueueId).HasColumnOrder(iota);

            entity.HasKey(e => new
            {
                e.Ticks,
                e.PredictionId,
                e.QueueId
            });

            entity.HasIndex(e => e.PredictionId, "IX_FK_PredictionPredictionBin");

            entity.HasIndex(e => e.QueueId, "IX_FK_QueuePredictionBin");

            entity.Property(e => e.ServiceTimeMu).HasColumnType("decimal(18, 0)");

            entity
                .HasOne(d => d.Prediction)
                .WithMany(p => p.PredictionBins)
                .HasForeignKey(d => d.PredictionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PredictionPredictionBin");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.PredictionBins)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_QueuePredictionBin");
        });

        modelBuilder.Entity<PreparationWay>(entity =>
        {
            entity.ToTable("PreparationWays");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.SiteId, "IX_FK_PreparationWaySite");

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parameters).HasMaxLength(1024);

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.PreparationWays)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PreparationWaySite");
        });

        modelBuilder.Entity<PreparationWayBonusCoefficient>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("PreparationWayBonusCoefficients");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.BonusCoefficient).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.PreparationWayId).HasColumnOrder(iota);
            entity.HasIndex(e => e.PreparationWayId, "IX_FK_PreparationWayBonusCoefficientPreparationWay");

            entity
                .HasOne(d => d.PreparationWay)
                .WithMany(p => p.PreparationWayBonusCoefficients)
                .HasForeignKey(d => d.PreparationWayId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PreparationWayBonusCoefficientPreparationWay");
        });

        modelBuilder.Entity<Queue>(entity =>
        {
            entity.ToTable("Queues");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Abbreviation).HasMaxLength(10);
            entity.Property(e => e.CcId).HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.ForecastParameters).HasMaxLength(4000);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity
                .HasMany(d => d.ChildQueues)
                .WithMany(p => p.ParentQueues)
                .UsingEntity<Dictionary<string, object>>(
                    "QueueGrouping",
                    r =>
                        r.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("ChildQueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueGrouping_Queue1"),
                    l =>
                        l.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("ParentQueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueGrouping_Queue2"),
                    j =>
                    {
                        j.HasKey("ChildQueuesId", "ParentQueuesId");
                        j.ToTable("QueueGrouping");
                        j.HasIndex(["ParentQueuesId"], "IX_FK_QueueGrouping_Queue2");
                        j.IndexerProperty<QueueId>("ChildQueuesId").HasColumnName("ChildQueues_Id");
                        j.IndexerProperty<QueueId>("ParentQueuesId").HasColumnName("ParentQueues_Id");
                    }
                );

            entity
                .HasMany(d => d.Events)
                .WithMany(p => p.Queues)
                .UsingEntity<Dictionary<string, object>>(
                    "QueueEvent",
                    r =>
                        r.HasOne<Event>()
                            .WithMany()
                            .HasForeignKey("EventsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueEvents_Event"),
                    l =>
                        l.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("QueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueEvents_Queue"),
                    j =>
                    {
                        j.HasKey("QueuesId", "EventsId");
                        j.ToTable("QueueEvents");
                        j.HasIndex(["EventsId"], "IX_FK_QueueEvents_Event");
                        j.IndexerProperty<QueueId>("QueuesId").HasColumnName("Queues_Id");
                        j.IndexerProperty<EventId>("EventsId").HasColumnName("Events_Id");
                    }
                );

            entity
                .HasMany(d => d.ParentQueues)
                .WithMany(p => p.ChildQueues)
                .UsingEntity<Dictionary<string, object>>(
                    "QueueGrouping",
                    r =>
                        r.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("ParentQueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueGrouping_Queue2"),
                    l =>
                        l.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("ChildQueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueGrouping_Queue1"),
                    j =>
                    {
                        j.HasKey("ChildQueuesId", "ParentQueuesId");
                        j.ToTable("QueueGrouping");
                        j.HasIndex(["ParentQueuesId"], "IX_FK_QueueGrouping_Queue2");
                        j.IndexerProperty<QueueId>("ChildQueuesId").HasColumnName("ChildQueues_Id");
                        j.IndexerProperty<QueueId>("ParentQueuesId").HasColumnName("ParentQueues_Id");
                    }
                );

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.Queues)
                .UsingEntity<Dictionary<string, object>>(
                    "QueueSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueSite_Site"),
                    l =>
                        l.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("QueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_QueueSite_Queue"),
                    j =>
                    {
                        j.HasKey("QueuesId", "SitesId");
                        j.ToTable("QueueSite");
                        j.HasIndex(["SitesId"], "IX_FK_QueueSite_Site");
                        j.IndexerProperty<QueueId>("QueuesId").HasColumnName("Queues_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<QueueProperty>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("QueueProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Type).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.Value).HasMaxLength(1024).HasColumnOrder(iota);

            entity.Property(e => e.QueueId).HasColumnOrder(iota);
            entity.HasIndex(e => e.QueueId, "IX_FK_QueueQueueProperty");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.QueueProperties)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_QueueQueueProperty");
        });

        modelBuilder.Entity<RealBin>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("RealBins");

            entity.Property(e => e.Ticks).HasColumnOrder(iota);
            entity.Property(e => e.Offered).HasColumnOrder(iota);
            entity.Property(e => e.Handled).HasColumnOrder(iota);
            entity.Property(e => e.QueuedOver).HasColumnOrder(iota);
            entity.Property(e => e.Abandoned).HasColumnOrder(iota);
            entity.Property(e => e.AbandonedOver).HasColumnOrder(iota);
            entity.Property(e => e.ServiceTimeMu).HasColumnOrder(iota);
            entity.Property(e => e.QueueId).HasColumnOrder(iota);
            entity.Property(e => e.AbandonedShort).HasColumnOrder(iota);

            entity.HasKey(e => new { e.Ticks, e.QueueId });

            entity.HasIndex(e => e.QueueId, "IX_FK_QueueRealBin");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.RealBins)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_QueueRealBin");
        });

        modelBuilder.Entity<Routing>(entity =>
        {
            entity.ToTable("Routings");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.Routings)
                .UsingEntity<Dictionary<string, object>>(
                    "RoutingSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoutingSite_Site"),
                    l =>
                        l.HasOne<Routing>()
                            .WithMany()
                            .HasForeignKey("RoutingsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_RoutingSite_Routing"),
                    j =>
                    {
                        j.HasKey("RoutingsId", "SitesId");
                        j.ToTable("RoutingSite");
                        j.HasIndex(["SitesId"], "IX_FK_RoutingSite_Site");
                        j.IndexerProperty<RoutingId>("RoutingsId").HasColumnName("Routings_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<Skill>(entity =>
        {
            entity.ToTable("Skills");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Abbreviation).HasMaxLength(10);
            entity.Property(e => e.CcId).HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Parameters).HasMaxLength(1024);

            entity
                .HasMany(d => d.ChildSkills)
                .WithMany(p => p.ParentSkills)
                .UsingEntity<Dictionary<string, object>>(
                    "SkillGrouping",
                    r =>
                        r.HasOne<Skill>()
                            .WithMany()
                            .HasForeignKey("ChildSkillsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SkillGrouping_Skill"),
                    l =>
                        l.HasOne<Skill>()
                            .WithMany()
                            .HasForeignKey("ParentSkillsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SkillGrouping_Skill1"),
                    j =>
                    {
                        j.HasKey("ChildSkillsId", "ParentSkillsId");
                        j.ToTable("SkillGrouping");
                        j.HasIndex(["ParentSkillsId"], "IX_FK_SkillGrouping_Skill1");
                        j.IndexerProperty<SkillId>("ChildSkillsId").HasColumnName("ChildSkills_Id");
                        j.IndexerProperty<SkillId>("ParentSkillsId").HasColumnName("ParentSkills_Id");
                    }
                );

            entity
                .HasMany(d => d.ParentSkills)
                .WithMany(p => p.ChildSkills)
                .UsingEntity<Dictionary<string, object>>(
                    "SkillGrouping",
                    r =>
                        r.HasOne<Skill>()
                            .WithMany()
                            .HasForeignKey("ParentSkillsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SkillGrouping_Skill1"),
                    l =>
                        l.HasOne<Skill>()
                            .WithMany()
                            .HasForeignKey("ChildSkillsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_SkillGrouping_Skill"),
                    j =>
                    {
                        j.HasKey("ChildSkillsId", "ParentSkillsId");
                        j.ToTable("SkillGrouping");
                        j.HasIndex(["ParentSkillsId"], "IX_FK_SkillGrouping_Skill1");
                        j.IndexerProperty<SkillId>("ChildSkillsId").HasColumnName("ChildSkills_Id");
                        j.IndexerProperty<SkillId>("ParentSkillsId").HasColumnName("ParentSkills_Id");
                    }
                );
        });

        modelBuilder.Entity<WorkMission>(entity =>
        {
            entity.ToTable("WorkMissions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.EmployeeId, "IX_FK_WorkMissionEmployee");

            entity.HasIndex(e => e.QueueId, "IX_FK_WorkMissionQueue");

            entity.HasIndex(e => e.TypeId, "IX_FK_WorkMissionWorkMissionType");

            entity.HasIndex(
                e => new
                {
                    e.EmployeeId,
                    e.MissionStart,
                    e.TypeId
                },
                "NCI_EmployeeId_MissionStart_TypeId"
            );

            entity.Property(e => e.MissionEnd).IsStoredAsDateTime();
            entity.Property(e => e.MissionStart).IsStoredAsDateTime();
            entity.Property(e => e.SystemCode).HasMaxLength(20);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.WorkMissions)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkMissionEmployee");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.WorkMissions)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkMissionQueue");

            entity
                .HasOne(d => d.Type)
                .WithMany(p => p.WorkMissions)
                .HasForeignKey(d => d.TypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkMissionWorkMissionType");
        });

        modelBuilder.Entity<WorkMissionType>(entity =>
        {
            entity.ToTable("WorkMissionTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Color).HasMaxLength(7).IsFixedLength();
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parameters).HasMaxLength(1024);

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.WorkMissionTypes)
                .UsingEntity<Dictionary<string, object>>(
                    "WorkMissionTypeSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkMissionTypeSite_Site"),
                    l =>
                        l.HasOne<WorkMissionType>()
                            .WithMany()
                            .HasForeignKey("WorkMissionTypesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkMissionTypeSite_WorkMissionType"),
                    j =>
                    {
                        j.HasKey("WorkMissionTypesId", "SitesId");
                        j.ToTable("WorkMissionTypeSite");
                        j.HasIndex(["SitesId"], "IX_FK_WorkMissionTypeSite_Site");
                        j.IndexerProperty<WorkMissionTypeId>("WorkMissionTypesId").HasColumnName("WorkMissionTypes_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<WorkOrder>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("WorkOrders");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.OrderGroupId).HasColumnOrder(iota);
            entity.Property(e => e.Start).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Deadline).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.FirstProcessed).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.LastProcessed).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Volume).HasColumnOrder(iota);
            entity.Property(e => e.ProcessedVolume).HasColumnOrder(iota);

            entity.HasDoubleValueChange(e => e.ProductivityChange, iota);

            entity.Property(e => e.Color).HasMaxLength(7).HasColumnOrder(iota).IsFixedLength();

            entity.Property(e => e.QueueId).HasColumnOrder(iota);
            entity.HasIndex(e => e.QueueId, "IX_FK_WorkOrderQueue");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_WorkOrderSite");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.WorkOrders)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkOrderQueue");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.WorkOrders)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkOrderSite");
        });

        modelBuilder.Entity<WorkOrderItem>(entity =>
        {
            entity.ToTable("WorkOrderItems");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity
                .HasIndex(e => new { e.EmployeeId, e.Preparation }, "IX_FK_WorkOrderItemEmployee")
                .HasFillFactor(80)
                .IncludeProperties(e => new
                {
                    e.Volume,
                    e.Weight,
                    e.PreparationWayId
                });

            entity.HasIndex(e => e.PreparationWayId, "IX_FK_WorkOrderItemPreparationWay");

            entity.HasIndex(e => e.WorkOrderId, "IX_FK_WorkOrderWorkOrderItem");

            entity
                .HasIndex(e => new { e.Preparation, e.EmployeeId }, "NCI_Preparation_EmployeeId_INCL")
                .HasFillFactor(80)
                .IncludeProperties(e => new
                {
                    e.Volume,
                    e.Weight,
                    e.WorkOrderId,
                    e.PreparationWayId
                });

            entity
                .HasIndex(e => new { e.Preparation, e.WorkOrderId }, "NCI_Preparation_WorkOrderId_INCL")
                .HasFillFactor(80)
                .IncludeProperties(e => new
                {
                    e.PlannedStart,
                    e.Deadline,
                    e.Volume
                });

            entity.Property(e => e.Deadline).IsStoredAsDateTime();
            entity.Property(e => e.PlannedStart).IsStoredAsDateTime();
            entity.Property(e => e.Preparation).IsStoredAsDateTime();
            entity.Property(e => e.SystemCode).HasMaxLength(20);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.WorkOrderItems)
                .HasForeignKey(d => d.EmployeeId)
                .HasConstraintName("FK_WorkOrderItemEmployee");

            entity
                .HasOne(d => d.PreparationWay)
                .WithMany(p => p.WorkOrderItems)
                .HasForeignKey(d => d.PreparationWayId)
                .HasConstraintName("FK_WorkOrderItemPreparationWay");

            entity
                .HasOne(d => d.WorkOrder)
                .WithMany(p => p.WorkOrderItems)
                .HasForeignKey(d => d.WorkOrderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkOrderWorkOrderItem");
        });

        modelBuilder.Entity<WorkOrderPrediction>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("WorkOrderPredictions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.PlannedStart).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Deadline).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Volume).HasColumnOrder(iota);

            entity.HasDoubleValueChange(e => e.ProductivityChange, iota);

            entity.Property(e => e.Color).HasMaxLength(7).HasColumnOrder(iota).IsFixedLength();

            entity.Property(e => e.Ignore).HasColumnOrder(iota);

            entity.Property(e => e.QueueId).HasColumnOrder(iota);
            entity.HasIndex(e => e.QueueId, "IX_FK_WorkOrderPredictionQueue");

            entity.Property(e => e.PredictionId).HasColumnOrder(iota);
            entity.HasIndex(e => e.PredictionId, "IX_FK_PredictionWorkOrderPrediction");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_WorkOrderPredictionSite");

            entity
                .HasOne(d => d.Prediction)
                .WithMany(p => p.WorkOrderPredictions)
                .HasForeignKey(d => d.PredictionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PredictionWorkOrderPrediction");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.WorkOrderPredictions)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkOrderPredictionQueue");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.WorkOrderPredictions)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkOrderPredictionSite");
        });

        modelBuilder.Entity<WorkOrderProductivity>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("WorkOrderProductivities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_WorkOrderProductivitySite");

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity.HasDoubleValueChange(e => e.ProductivityChange, iota);

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.WorkOrderProductivities)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkOrderProductivitySite");

            entity
                .HasMany(d => d.Queues)
                .WithMany(p => p.WorkOrderProductivities)
                .UsingEntity<Dictionary<string, object>>(
                    "WorkOrderProductivityQueue",
                    r =>
                        r.HasOne<Queue>()
                            .WithMany()
                            .HasForeignKey("QueuesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkOrderProductivityQueue_Queue"),
                    l =>
                        l.HasOne<WorkOrderProductivity>()
                            .WithMany()
                            .HasForeignKey("WorkOrderProductivitiesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkOrderProductivityQueue_WorkOrderProductivity"),
                    j =>
                    {
                        j.HasKey("WorkOrderProductivitiesId", "QueuesId");
                        j.ToTable("WorkOrderProductivityQueue");
                        j.HasIndex(["QueuesId"], "IX_FK_WorkOrderProductivityQueue_Queue");
                        j.IndexerProperty<WorkOrderProductivityId>("WorkOrderProductivitiesId")
                            .HasColumnName("WorkOrderProductivities_Id");
                        j.IndexerProperty<QueueId>("QueuesId").HasColumnName("Queues_Id");
                    }
                );
        });
    }
}
