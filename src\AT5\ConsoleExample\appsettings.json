{"ConnectionStrings": {"OrgDbTemplate": "Data Source=.\\;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;"}, "OrgDb": "console-test", "RecreateOrgDb": false, "FileAlgorithmLogRepositorySettings": {"CacheFilePath": "c:/temp/cache.json"}, "CachingSettings": {"RetentionInMinutes": 10}, "AppSettingsExample": {"Value1": "Value 1 from appsettings.json", "Value2": "Value 2 from appsettings.json"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": {"type": "Serilog.Templates.ExpressionTemplate, Serilog.Expressions", "template": "[{@t:HH:mm:ss} {@l:u3}] {#if SourceContext is not null}{Concat('[', Substring(SourceContext, LastIndexOf(SourceContext, '.') + 1), ']', ' ')}{#end}{@m}\n{@x}", "theme": "Serilog.Templates.Themes.TemplateTheme::Code, Serilog.Expressions"}}}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day"}}], "Properties": {"Application": "Example"}}}