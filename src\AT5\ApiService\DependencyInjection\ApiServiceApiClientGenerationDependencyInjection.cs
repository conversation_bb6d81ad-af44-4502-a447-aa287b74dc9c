﻿namespace AT.ApiService.DependencyInjection;

using System.Reflection;
using AT.PrimitivesAT5;
using FastEndpoints.ClientGen;
using FastEndpoints.Swagger;
using NJsonSchema.CodeGeneration.CSharp;
using Scalar.AspNetCore;

internal static class ApiServiceApiClientGenerationDependencyInjection
{
    public static IServiceCollection AddApiServiceApiClientGenerationServices(this IServiceCollection services)
    {
        // Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
        // NOTE: Swagger seems to be needed for OpenAPI and FastEndpoints?
        services.SwaggerDocument(o =>
        {
            o.DocumentSettings = d =>
            {
                d.DocumentName = "v1";

                // NOTE: The following commented code instructs the OpenAPI document to use integer type for all Ids.
                // Without this, the OpenAPI document uses string type for Ids.
                //var idTypes = Assembly.GetAssembly(typeof(PrimitivesAT5Assembly))!.GetTypes()
                //    .Where(t => t.Namespace?.StartsWith("AT.PrimitivesAT5.Ids") == true);

                //foreach (var idType in idTypes)
                //{
                //    var mapper = new PrimitiveTypeMapper(
                //        idType, // Primitive CLR type to override
                //        schema =>
                //        {
                //            schema.Type = JsonObjectType.Integer;
                //        });

                //    d.SchemaSettings.TypeMappers.Add(mapper);
                //}
            };
            // Use DTOs without namespace. When set to false, the generation seems to work incorrectly.
            // Instead of generating e.g. `AT.Models.MyDTO` it generates `ATModelsMyDTO`.
            o.ShortSchemaNames = true;
            o.RemoveEmptyRequestSchema = true;
        });

        // NOTE: In previous commits we used the new method to generate OpenAPI document. The following two lines were needed.
        // Now it seems to be replaced by call to SwaggerDocument() but I'm not sure if we won't miss it.
        // https://gist.github.com/dj-nitehawk/c7052f01f3f650e67fb6782c84d3b5f0
        //services.AddOpenApi();
        //services.AddSwaggerDocument();

        return services;
    }

    public static async Task ConfigureApiServiceForApiClientGenerationAsync(this WebApplication app)
    {
        if (app.Environment.IsDevelopment())
        {
            // Map endpoint that provides OpenApi document.
            app.MapOpenApi();

            // Map ScalarUI for OpenApi.
            app.UseOpenApi(c => c.Path = "/openapi/{documentName}.json");
            app.MapScalarApiReference();
        }

        // This is called only if the project is run with --generateclients true
        await app.GenerateClientsAndExitAsync(
            documentName: "v1", // Document name used by `SwaggerDocument` extension method above.
            destinationPath: @"..\..\..\..\..\ApiClient",
            csSettings: c =>
            {
                c.ClassName = "ApiClient";
                c.GenerateClientClasses = true;
                c.GenerateClientInterfaces = true;
                c.GenerateDtoTypes = false;
                c.AdditionalNamespaceUsages =
                    // Get namespaces of all models in the Shared project.
                    Assembly
                        .GetAssembly(typeof(AT.Shared.SharedAssembly))!
                        .GetTypes()
                        .Where(t => t.Namespace?.StartsWith("AT.Shared.Models") == true)
                        // Add namespaces for primitive types, especially for Ids, such as UserId.
                        .Concat(Assembly.GetAssembly(typeof(PrimitivesAT5Assembly))!.GetTypes())
                        .Select(t => t.Namespace)
                        .Distinct()
                        .ToArray();
                c.UseBaseUrl = false; // If true, baseUrl is part of the generated API Client's constructor and the DI does not work.
                c.GenerateOptionalParameters = true; // So that there is no extra overload for every method that would include CancellationToken.
                c.CSharpGeneratorSettings.Namespace = "AT.ApiClient";
                c.CSharpGeneratorSettings.JsonLibrary = CSharpJsonLibrary.SystemTextJson;
                c.CSharpGeneratorSettings.JsonPolymorphicSerializationStyle =
                    CSharpJsonPolymorphicSerializationStyle.SystemTextJson;
                c.CSharpGeneratorSettings.DateTimeType = "System.DateTime"; // NOTE: Untested.
                c.CSharpGeneratorSettings.DateType = "System.DateOnly"; // NOTE: Untested.
                c.CSharpGeneratorSettings.TimeType = "System.TimeOnly"; // NOTE: Untested.
            },
            tsSettings: null
        );
    }
}
