﻿namespace AT.Core.Domain.Entities;

public class RosteringRule
{
    public RosteringRuleId Id { get; set; }

    public bool Disabled { get; set; }

    public string Type { get; set; } = null!;

    public string Name { get; set; } = null!;

    public TimeInterval TimeInterval { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public string Parameters { get; set; } = null!;

    public int PastValidity { get; set; }

    public string? Description { get; set; }

    public virtual ICollection<RosteringRuleFilters2> RosteringRuleFilters2s { get; set; } =
        new List<RosteringRuleFilters2>();

    public virtual ICollection<CalculationPhase> CalculationPhases { get; set; } = new List<CalculationPhase>();
}
