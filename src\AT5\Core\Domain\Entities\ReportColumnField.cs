﻿namespace AT.Core.Domain.Entities;

using Base;

public class ReportColumnField
{
    public ReportColumnFieldId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Method { get; set; } = null!;

    public bool Substract { get; set; }

    public ReportColumnId ReportColumnId { get; set; }

    public Filter2Id? FilterId { get; set; }

    public virtual Filter2? Filter { get; set; }

    public virtual ReportColumn ReportColumn { get; set; } = null!;
}
