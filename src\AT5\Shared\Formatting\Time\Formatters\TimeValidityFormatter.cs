﻿namespace AT.Shared.Formatting.Time.Formatters;

using System.Globalization;
using AT.Translations;
using SmartFormat;

public sealed class TimeValidityFormatter(ITranslator _translator)
{
    public string Format(TimeValidity value, Language? language = null)
    {
        var dateFormat = _translator.Translate<TimeFormatTranslations.DateTimeShort>(language);

        string start = value.Start?.ToString(dateFormat.Value, CultureInfo.InvariantCulture) ?? string.Empty;
        string end = value.End?.ToString(dateFormat.Value, CultureInfo.InvariantCulture) ?? string.Empty;

        if (value.Start.HasValue && value.End.HasValue)
        {
            return Smart.Format(CultureInfo.InvariantCulture, "{start} - {end}", start, end);
        }

        if (value.Start.HasValue || value.End.HasValue)
        {
            return Smart.Format(CultureInfo.InvariantCulture, "{start}...{end}", start, end);
        }

        return string.Empty;
    }
}
