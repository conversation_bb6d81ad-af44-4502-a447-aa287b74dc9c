﻿namespace AT.DataStructures.Time;

using System.Text.Json.Serialization;

public readonly partial record struct DateTimeInterval
{
    /// <summary>
    /// Gets the duration of the interval.
    /// </summary>
    public TimeSpan Duration => IsEmpty ? TimeSpan.Zero : End - Start;

    /// <summary>
    /// Gets the start date of the interval as a <see cref="DateOnly"/>.
    /// </summary>
    public DateOnly StartDate => DateOnly.FromDateTime(Start.Date);

    /// <summary>
    /// Gets the end date of the interval as a <see cref="DateOnly"/>.
    /// </summary>
    public DateOnly EndDate => DateOnly.FromDateTime(End.TimeOfDay == TimeSpan.Zero ? End.AddDays(-1) : End.Date);

    /// <summary>
    /// Implicitly converts a <see cref="DateTimeInterval"/> to an <see cref="OpenDateTimeInterval"/> using <see cref="ToOpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="interval">The <see cref="DateTimeInterval"/> to convert.</param>
    public static implicit operator OpenDateTimeInterval(DateTimeInterval interval)
    {
        return interval.ToOpenDateTimeInterval();
    }

    /// <summary>
    /// Converts a <see cref="DateTimeInterval"/> to an <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <returns>An <see cref="OpenDateTimeInterval"/> representing the same interval.</returns>
    public OpenDateTimeInterval ToOpenDateTimeInterval()
    {
        return new OpenDateTimeInterval(Start, End);
    }
}
