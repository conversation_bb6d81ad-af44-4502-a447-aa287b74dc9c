﻿namespace AT.Infrastructure.Utilities.AzureMonitor;

using System;
using System.Diagnostics;
using System.Reflection;
using Azure.Monitor.OpenTelemetry.Exporter;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;

public static class AzureMonitor
{
    public static IServiceCollection AddAzureMonitor(this IServiceCollection services, IConfiguration configuration)
    {
        var configurationSection = configuration.GetSection(AzureMonitorOptions.AzureMonitor);
        services.Configure<AzureMonitorOptions>(configurationSection);

        var settings = configurationSection.Get<AzureMonitorOptions>();
        if (string.IsNullOrEmpty(settings?.ConnectionString))
        {
            return services;
        }

        var assembly = Assembly.GetExecutingAssembly();
        string version = FileVersionInfo.GetVersionInfo(assembly.Location).ProductVersion ?? string.Empty;
        string shortVersion = string.Join(".", version.Split('.').Take(4));

        services
            .AddOpenTelemetry()
            .WithMetrics(options =>
            {
                options
                // include all built-in metric .AddAspNetCoreInstrumentation()
                // include example custom metric .AddMeter("Aristotelos.ExampleMetric")
                .AddMeter("Microsoft.Extensions.Diagnostics.HealthChecks");
                AddOpenTelemetryMetrics(options);
                options.AddAzureMonitorMetricExporter(o => o.ConnectionString = settings!.ConnectionString);
            })
            .WithTracing(tracing =>
            {
                /*tracing.AddAspNetCoreInstrumentation();*/
                /*builder.AddSqlClientInstrumentation(options =>
                {
                    options.SetDbStatementForText = true;
                    options.SetDbStatementForStoredProcedure = true;
                });*/
                if (settings.DoNotSampleRequestsWithErrors)
                {
                    tracing.AddProcessor(new CustomSamplingProcessor());
                }

                tracing.AddAzureMonitorTraceExporter(o =>
                {
                    o.ConnectionString = settings!.ConnectionString;
                    o.SamplingRatio = settings!.SamplingRatio;
                });
            })
            .ConfigureResource(r =>
                r.AddService(
                    serviceName: settings!.ServiceName,
                    serviceInstanceId: settings.ServiceInstanceId,
                    serviceVersion: shortVersion
                )
            );

        services.AddLogging(logging =>
        {
            logging.AddOpenTelemetry(options =>
            {
                options.IncludeScopes = true;
                options.AddAzureMonitorLogExporter(o => o.ConnectionString = settings!.ConnectionString);
            });
        });

        //services.AddHostedService<ExampleMetricHostedService>();
        services
            .AddHealthChecks()
            .AddCheck(
                "Health Check",
                () => HealthCheckResult.Healthy("Service is healthy"),
                timeout: TimeSpan.FromSeconds(settings!.HealthChecksInterval)
            );
        services.AddTelemetryHealthCheckPublisher();

        return services;
    }

    private static void AddOpenTelemetryMetrics(MeterProviderBuilder builder)
    {
        builder
            .AddProcessInstrumentation()
            //.AddView(instrumentName: "process.memory.usage", MetricStreamConfiguration.Drop)
            .AddView(instrumentName: "process.memory.virtual", MetricStreamConfiguration.Drop)
            //.AddView(instrumentName: "cpu.time", MetricStreamConfiguration.Drop)
            .AddView(instrumentName: "process.cpu.count", MetricStreamConfiguration.Drop)
            .AddView(instrumentName: "process.thread.count", MetricStreamConfiguration.Drop);
        /*.AddRuntimeInstrumentation()
        .AddView(instrumentName: "process.runtime.dotnet.gc.collections.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.gc.objects.size", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.gc.allocations.size", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.gc.committed_memory.size", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.gc.heap.size", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.gc.heap.fragmentation.size", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.gc.duration", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.jit.il_compiled.size", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.jit.methods_compiled.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.jit.compilation_time", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.monitor.lock_contention.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.thread_pool.threads.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.thread_pool.completed_items.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.thread_pool.queue.length", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.timer.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.assemblies.count", MetricStreamConfiguration.Drop)
        .AddView(instrumentName: "process.runtime.dotnet.exceptions.count", MetricStreamConfiguration.Drop)*/
    }
}
