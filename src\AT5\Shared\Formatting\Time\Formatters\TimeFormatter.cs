﻿namespace AT.Shared.Formatting.Time.Formatters;

using System;
using System.Globalization;
using AT.Translations;
using AT.Translations.Formatting.Time;

public sealed class TimeFormatter(ITranslator _translator)
{
    public string Format(TimeOnly value, TimeFormattingType type, Language? language = null)
    {
        return type switch
        {
            TimeFormattingType.Standard => FormatStandard(value, language),
            TimeFormattingType.WithoutSeconds => FormatWithoutSeconds(value, language),
            TimeFormattingType.Short => FormatShort(value, language),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {type} is unknown"),
#else
            _ => FormatStandard(value, language)
#endif
        };
    }

    public string FormatStandard(TimeOnly value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.Time>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }

    public string FormatWithoutSeconds(TimeOnly value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.TimeWithoutSeconds>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }

    public string FormatShort(TimeOnly value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.TimeShort>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }
}
