﻿namespace AT.Translations;

using System.Globalization;
using System.Resources;

/// <summary>
/// For accessing translations managed by <paramref name="_resourceManager"/>.
/// Code mostly taken from SmartFormat's default implementation of ILocalizationProvider:
/// https://github.com/axuno/SmartFormat/blob/main/src/SmartFormat/Utilities/LocalizationProvider.cs
/// </summary>
public class LocalizationProvider(ResourceManager _resourceManager)
{
    /// <summary>
    /// Gets the value from one of the registered resources.
    /// If no value was found, the <paramref name="name"/> is returned.
    /// </summary>
    /// <remarks>
    /// <see cref="CultureInfo.CurrentUICulture"/> is used, if no <see cref="CultureInfo"/> is provided as a parameter.
    /// </remarks>
    /// <param name="name"></param>
    /// <returns>The value from one of the registered resources. If no value was found, the <paramref name="name"/> is returned.</returns>
    public virtual string? GetString(string name)
    {
        return GetString(name, default(CultureInfo?));
    }

    /// <summary>
    /// Gets the value from one of the registered resources.
    /// If no value was found, the <paramref name="name"/> is returned.
    /// </summary>
    /// <param name="name"></param>
    /// <param name="cultureName"></param>
    /// <returns>The value from one of the registered resources. If no value was found, the <paramref name="name"/> is returned.</returns>
    public virtual string? GetString(string name, string cultureName)
    {
        return GetString(name, CultureInfo.GetCultureInfo(cultureName));
    }

    /// <summary>
    /// Gets the value from one of the registered string resources for the specified culture.
    /// If no value was found, the <paramref name="name"/> is returned.
    /// </summary>
    /// <remarks>
    /// <see cref="CultureInfo.CurrentUICulture"/> is used, if no <see cref="CultureInfo"/> is provided as a parameter.
    /// </remarks>
    /// <param name="name"></param>
    /// <param name="cultureInfo"></param>
    /// <returns>The value from one of the registered string resources for the specified culture. If no value was found, the <paramref name="name"/> is returned.</returns>
    public virtual string? GetString(string name, CultureInfo? cultureInfo)
    {
        string? value =
            cultureInfo != null ? _resourceManager.GetString(name, cultureInfo) : _resourceManager.GetString(name);

        if (value is null && FallbackCulture != null)
        {
            value = _resourceManager.GetString(name, FallbackCulture);
        }

        if (value is not null)
        {
            return value;
        }

        return ReturnNameIfNotFound ? name : null;
    }

    /// <summary>
    /// Gets or sets the fallback <see cref="CultureInfo"/>, if the localized string cannot be found in the specified culture.
    /// </summary>
    public virtual CultureInfo? FallbackCulture { get; set; }

    /// <summary>
    /// If <see langword="true"/>, the requested name will be returned, instead of null.
    /// </summary>
    /// <remarks>
    /// Same behavior as e.g. <c>Microsoft.Extensions.Localization.ResourceManagerStringLocalizer</c>, if <see langword="true"/>.
    /// </remarks>
    public virtual bool ReturnNameIfNotFound { get; set; } = false;
}
