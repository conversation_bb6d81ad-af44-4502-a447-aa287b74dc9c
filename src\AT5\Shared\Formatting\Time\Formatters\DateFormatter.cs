﻿namespace AT.Shared.Formatting.Time.Formatters;

using System;
using System.Globalization;
using AT.Translations;
using AT.Translations.Formatting.Time;

public sealed class DateFormatter(ITranslator _translator)
{
    public string Format(DateOnly value, DateFormattingType type, Language? language = null)
    {
        return type switch
        {
            DateFormattingType.Standard => FormatStandard(value, language),
            DateFormattingType.MonthAndYear => FormatMonthAndYear(value, language),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {type} is unknown"),
#else
            _ => FormatStandard(value, language)
#endif
        };
    }

    public string FormatStandard(DateOnly value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.Date>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }

    public string FormatShort(DateOnly value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.DateShort>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }

    public string FormatMonthAndYear(DateOnly value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.DateWithMonthAndYearOnly>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }
}
