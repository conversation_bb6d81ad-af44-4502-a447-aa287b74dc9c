﻿namespace AT.Infrastructure.Database;

using AT.Infrastructure.Database.GroupBuilders;
using AT.Infrastructure.Database.Infrastructure;
using AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;

public class OrganizationDbContext(DbContextOptions<OrganizationDbContext> _options) : DbContext(_options)
{
    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        base.ConfigureConventions(configurationBuilder);

        // Foreign keys are defined manually and validated against the AT4 model.
        // Since some indexes are created outside of Entity Framework,
        // and EF's ForeignKeyIndexConvention adds additional indexes on foreign key properties by default,
        // we disable automatic index creation for foreign keys to avoid duplicates.
        // https://learn.microsoft.com/en-us/dotnet/api/microsoft.entityframeworkcore.metadata.conventions.foreignkeyindexconvention?view=efcore-9.0
        configurationBuilder?.Conventions.Remove(typeof(ForeignKeyIndexConvention));

        // This will add Vogen convertors, this is a generated extension method
        configurationBuilder.RegisterAllInVogenEfCoreConverters();
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        var context = new GroupModelBuilderContext()
        {
            ModelBuilder = modelBuilder,
            AdditionalIndexCollection = new DummyAdditionalIndexCollection(),
        };

        BuildModel(context);
    }

    protected static void BuildModel(GroupModelBuilderContext context)
    {
        ActivityGroupModelBuilder.BuildActivityGroup(context);
        ApprovalsGroupModelBuilder.BuildApprovalsGroup(context);
        AuditGroupModelBuilder.BuildAuditGroup(context);
        CalculationGroupModelBuilder.BuildCalculationGroup(context);
        ConfigurationGroupModelBuilder.BuildConfigurationGroup(context);
        ContractGroupModelBuilder.BuildContractGroup(context);
        JobGroupModelBuilder.BuildConfigurationGroup(context);
        NoteGroupModelBuilder.BuildNoteGroup(context);
        NotificationGroupModelBuilder.BuildNotificationGroup(context);
        PropertyGroupModelBuilder.BuildPropertyGroup(context);
        QueueGroupModelBuilder.BuildQueueGroup(context);
        ReportGroupModelBuilder.BuildReportGroup(context);
        RequestGroupModelBuilder.BuildRequestGroup(context);
        RosterItemGroupModelBuilder.BuildRosterItemGroup(context);
        RosterGroupModelBuilder.BuildRosterGroup(context);
        SiteGroupModelBuilder.BuildSiteGroup(context);
        ShiftGroupModelBuilder.BuildShiftGroup(context);
        TeamGroupModelBuilder.BuildTeamGroup(context);
        UserGroupModelBuilder.BuildUserGroup(context);
    }
}
