var builder = DistributedApplication.CreateBuilder(args);

var cache = builder.AddGarnet("AristoTelosCache", 6379);

var apiService = builder.AddProject<Projects.ApiService>("apiservice").WithReference(cache).WaitFor(cache);

builder
    .AddProject<Projects.Web_Server>("web-server")
    .WithExternalHttpEndpoints()
    .WithReference(cache)
    .WaitFor(cache)
    .WithReference(apiService)
    .WaitFor(apiService);

// FUTURE: add project Projects.JobService and NotificationsService but make it conditional (on environment variables? and set in appsettings?)

await builder.Build().RunAsync();
