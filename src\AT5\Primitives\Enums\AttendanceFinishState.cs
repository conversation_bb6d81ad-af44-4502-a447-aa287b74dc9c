﻿namespace AT.Primitives.Enums;

using System.Diagnostics.CodeAnalysis;

[SuppressMessage(
    "StyleCop.CSharp.MaintainabilityRules",
    "SA1402:FileMayOnlyContainASingleClass",
    Justification = "Simple file structure."
)]
public enum AttendanceFinishState
{
    Open = 0,
    Finished = 1,
}

[SuppressMessage(
    "StyleCop.CSharp.MaintainabilityRules",
    "SA1402:FileMayOnlyContainASingleClass",
    Justification = "Simple file structure."
)]
public static class AttendanceStateHelper
{
    public static AttendanceFinishState FromPropertyState(PropertyState propertyState)
    {
        if (propertyState == PropertyState.Approved)
        {
            return AttendanceFinishState.Finished;
        }

        return AttendanceFinishState.Open;
    }

    public static PropertyState ToPropertyState(AttendanceFinishState attendanceState)
    {
        if (attendanceState == AttendanceFinishState.Finished)
        {
            return PropertyState.Approved;
        }

        return PropertyState.Pending;
    }
}
