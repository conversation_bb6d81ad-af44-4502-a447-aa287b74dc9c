﻿namespace AT.Infrastructure.Jobs.DurableJobs;

using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AT.Core.Jobs.DurableJobs;
using AT.Core.Jobs.DurableJobs.Identifiers;
using AT.Core.MasterDomain;
using AT.Infrastructure.DependencyInjection;
using AT.Infrastructure.Jobs.DurableJobs.Identifiers;
using AT.Infrastructure.Jobs.QuatzWrappers;
using AT.Utilities.Logging;
using Quartz;

public class DurableJobSeriesScheduler(
    ILogger<DurableJobSeriesScheduler> _logger,
    FullOrgId _fullOrgId,
    IOrgServiceProvider _orgServiceProvider,
    IQuartzSchedulerProvider _quartzSchedulerProvider
) : IDurableJobSeriesScheduler
{
    public async Task<IJobIdentifier> AddDurableJobSeries(JobSeriesDefinition jobSeriesDefinition, CancellationToken ct)
    {
        var jobKey = CreateJobKey(jobSeriesDefinition.Name);

        var jobSeriesContext = new DurableJobSeriesWrapperContext(_orgServiceProvider, jobSeriesDefinition);
        var jobDataMap = new JobDataMap() { { nameof(DurableJobSeriesWrapperContext), jobSeriesContext } };

        var quartzJob = JobBuilder
            .Create()
            .OfType<DurableJobSeriesWrapper>()
            .UsingJobData(jobDataMap)
            .WithIdentity(jobKey)
            .StoreDurably()
            .Build();

        var scheduler = await _quartzSchedulerProvider.GetStartedScheduler(ct);

        _logger.Info("Adding durable job with key {JobKey}.", jobKey);
        await scheduler.AddJob(quartzJob, replace: false, ct);
        return new JobIdentifier(jobKey);
    }

    public async Task<bool> DeleteDurableJobSeries(
        IReadOnlyCollection<IJobIdentifier> jobIdentifiers,
        CancellationToken ct
    )
    {
        var scheduler = await _quartzSchedulerProvider.GetStartedScheduler(ct);
        _logger.Info("Deleting {JobsCount} durable jobs.", jobIdentifiers.Count);
        var jobIdentifiersTyped = jobIdentifiers.Select(x => (JobIdentifier)x);
        return await scheduler.DeleteJobs([.. jobIdentifiersTyped.Select(x => x.JobKey)], ct);
    }

    public async Task<ITriggerIdentifier?> ScheduleDurableJobSeries(
        IJobIdentifier jobIdentifier,
        string triggerName,
        AT.Utilities.Cron.CronExpression cron,
        CancellationToken ct
    )
    {
        var jobIdentifierTyped = (JobIdentifier)jobIdentifier;
        var triggerKey = CreateTriggerKey(triggerName);
        var quartzTriggerBuilder = TriggerBuilder.Create().WithIdentity(triggerKey).WithCronSchedule(cron.Expression);

        var scheduler = await _quartzSchedulerProvider.GetStartedScheduler(ct);
        var jobKey = jobIdentifierTyped.JobKey;
        var quartzJob = await scheduler.GetJobDetail(jobKey, ct);
        if (quartzJob is null)
        {
            return null;
        }

        quartzTriggerBuilder.ForJob(quartzJob);
        var quartzTrigger = quartzTriggerBuilder.Build();

        _logger.Info(
            "Scheduling durable job with key {JobKey}, trigger key {TriggerKey} and cron {Cron}.",
            jobKey,
            triggerKey,
            cron
        );
        await scheduler.ScheduleJob(quartzTrigger, ct);
        return new TriggerIdentifier(triggerKey);
    }

    public async Task<bool> UnscheduleDurableJobSeries(
        IReadOnlyCollection<ITriggerIdentifier> triggerIdentifiers,
        CancellationToken ct
    )
    {
        var scheduler = await _quartzSchedulerProvider.GetStartedScheduler(ct);
        _logger.Info("Unscheduling {TriggersCount} triggers.", triggerIdentifiers.Count);
        var triggerIdentifiersTyped = triggerIdentifiers.Select(x => (TriggerIdentifier)x);
        return await scheduler.UnscheduleJobs([.. triggerIdentifiersTyped.Select(x => x.TriggerKey)], ct);
    }

    private JobKey CreateJobKey(string seriesName)
    {
        return new JobKey(seriesName, _fullOrgId.DatabaseName);
    }

    private TriggerKey CreateTriggerKey(string triggerName)
    {
        return new TriggerKey(triggerName, _fullOrgId.DatabaseName);
    }
}
