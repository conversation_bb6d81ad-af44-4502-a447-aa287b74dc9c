﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class CalculationLog
{
    public CalculationLogId Id { get; set; }

    public CalculationStatus Status { get; set; }

    public CalculationSolution Solution { get; set; }

    public double SolutionGap { get; set; }

    public DateTime? Queued { get; set; }

    public DateTime? Started { get; set; }

    public DateTime? Finished { get; set; }

    public CalculationPhaseId? CalculationPhaseId { get; set; }

    public CalculationId CalculationId { get; set; }

    public virtual Calculation Calculation { get; set; } = null!;

    public virtual ICollection<CalculationLogEntry> CalculationLogEntries { get; set; } =
        new List<CalculationLogEntry>();
}
