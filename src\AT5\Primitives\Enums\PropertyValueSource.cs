﻿namespace AT.Primitives.Enums;

public enum PropertyValueSource : byte
{
    /// <summary>
    ///     Value source unknown.
    /// </summary>
    Unknown = 0,

    /// <summary>
    ///     Value set by user.
    /// </summary>
    Manual = 1,

    /// <summary>
    ///     Value was computed and then stored.
    /// </summary>
    Computed = 2,

    /// <summary>
    ///     Value was synchronized from another system.
    /// </summary>
    Synchronized = 3,

    /// <summary>
    ///     Value is not stored, it is directly computed on the when needed.
    /// </summary>
    ComputedDirectly = 4,
}
