﻿namespace AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;

public class InstantPushNotificationsBatchSpec : Specification<PushNotification>
{
    public InstantPushNotificationsBatchSpec(int? batchSize = null)
    {
        Query.Where(pn => pn.SendTime == null).Include(pn => pn.PushNotificationRecipients);

        if (batchSize > 0)
        {
            Query.Take(batchSize.Value);
        }
    }
}
