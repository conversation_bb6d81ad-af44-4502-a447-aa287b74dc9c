﻿namespace AT.Core.Domain.Entities;

public class ShiftSystemDay
{
    public ShiftSystemDayId Id { get; set; }

    public short Rank { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public string? Parameters { get; set; }

    public ShiftSystemId ShiftSystemId { get; set; }

    public string? Text { get; set; }

    public string? BackgroundColor { get; set; }

    public virtual ShiftSystem ShiftSystem { get; set; } = null!;

    public virtual ICollection<ShiftTemplate> ShiftTemplates { get; set; } = new List<ShiftTemplate>();
}
