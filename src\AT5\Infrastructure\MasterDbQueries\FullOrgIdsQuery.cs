﻿namespace AT.Infrastructure.MasterDbQueries;

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

internal sealed class FullOrgIdsQuery(IDbContextFactory<MasterDbContext> _masterDbContextFactory) : IFullOrgIdsQuery
{
    public async Task<List<FullOrgId>> GetOrgIdsAsync(CancellationToken cancellationToken = default)
    {
        using var masterDbContext = _masterDbContextFactory.CreateDbContext();
        return await masterDbContext
            .Organizations.AsQueryable()
            .AsNoTracking()
            .Select(x => new FullOrgId(x.Id, x.DatabaseName))
            .ToListAsync(cancellationToken);
    }

    public List<FullOrgId> GetOrgIds()
    {
        using var masterDbContext = _masterDbContextFactory.CreateDbContext();
        return masterDbContext
            .Organizations.AsQueryable()
            .AsNoTracking()
            .Select(x => new FullOrgId(x.Id, x.DatabaseName))
            .ToList();
    }
}
