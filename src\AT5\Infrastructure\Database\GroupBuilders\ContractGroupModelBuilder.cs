﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Contract"/> in the database.
/// </summary>
internal static class ContractGroupModelBuilder
{
    public static void BuildContractGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Contract>(entity =>
        {
            entity.ToTable("Contracts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Code).HasMaxLength(50);
        });

        modelBuilder.Entity<Position>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Positions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Abbreviation).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);

            entity.Property(e => e.ExternalId).HasColumnOrder(iota);
            entity.Property(e => e.Code).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Disabled).HasColumnOrder(iota);
            entity.Property(e => e.IsGlobal).HasColumnOrder(iota);
            entity.Property(e => e.DoNotSynchronize).HasColumnOrder(iota);

            entity
                .HasMany(d => d.Roles)
                .WithMany(p => p.Positions)
                .UsingEntity<Dictionary<string, object>>(
                    "PositionRole",
                    r =>
                        r.HasOne<Role>()
                            .WithMany()
                            .HasForeignKey("RolesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PositionRole_Role"),
                    l =>
                        l.HasOne<Position>()
                            .WithMany()
                            .HasForeignKey("PositionsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PositionRole_Position"),
                    j =>
                    {
                        j.HasKey("PositionsId", "RolesId");
                        j.ToTable("PositionRole");
                        j.HasIndex(["RolesId"], "IX_FK_PositionRole_Role");
                        j.IndexerProperty<PositionId>("PositionsId").HasColumnName("Positions_Id");
                        j.IndexerProperty<RoleId>("RolesId").HasColumnName("Roles_Id");
                    }
                );
        });

        modelBuilder.Entity<WorkingTime>(entity =>
        {
            entity.ToTable("WorkingTimes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.ShiftTemplateId, "IX_FK_WorkingTimeShiftTemplate");

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity
                .HasOne(d => d.ShiftTemplate)
                .WithMany(p => p.WorkingTimes)
                .HasForeignKey(d => d.ShiftTemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkingTimeShiftTemplate");

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.WorkingTimes)
                .UsingEntity<Dictionary<string, object>>(
                    "WorkingTimeSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkingTimeSite_Site"),
                    l =>
                        l.HasOne<WorkingTime>()
                            .WithMany()
                            .HasForeignKey("WorkingTimesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkingTimeSite_WorkingTime"),
                    j =>
                    {
                        j.HasKey("WorkingTimesId", "SitesId");
                        j.ToTable("WorkingTimeSite");
                        j.HasIndex(["SitesId"], "IX_FK_WorkingTimeSite_Site");
                        j.IndexerProperty<WorkingTimeId>("WorkingTimesId").HasColumnName("WorkingTimes_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<WorkingTimeModel>(entity =>
        {
            entity.ToTable("WorkingTimeModels");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.BalanceConfigurationId, "IX_FK_BalanceConfigurationWorkingTimeModel");

            entity.HasIndex(e => e.ParentModelId, "IX_FK_WorkingTimeModelHierarchy");

            entity.HasIndex(e => e.WorkingTimeId, "IX_FK_WorkingTimeModelWorkingTime");

            entity.Property(e => e.Code).HasMaxLength(10);
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity
                .HasOne(d => d.BalanceConfiguration)
                .WithMany(p => p.WorkingTimeModels)
                .HasForeignKey(d => d.BalanceConfigurationId)
                .HasConstraintName("FK_BalanceConfigurationWorkingTimeModel");

            entity
                .HasOne(d => d.ParentModel)
                .WithMany(p => p.InverseParentModel)
                .HasForeignKey(d => d.ParentModelId)
                .HasConstraintName("FK_WorkingTimeModelHierarchy");

            entity
                .HasOne(d => d.WorkingTime)
                .WithMany(p => p.WorkingTimeModels)
                .HasForeignKey(d => d.WorkingTimeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkingTimeModelWorkingTime");

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.WorkingTimeModels)
                .UsingEntity<Dictionary<string, object>>(
                    "WorkingTimeModelSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkingTimeModelSite_Site"),
                    l =>
                        l.HasOne<WorkingTimeModel>()
                            .WithMany()
                            .HasForeignKey("WorkingTimeModelsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_WorkingTimeModelSite_WorkingTimeModel"),
                    j =>
                    {
                        j.HasKey("WorkingTimeModelsId", "SitesId");
                        j.ToTable("WorkingTimeModelSite");
                        j.HasIndex(["SitesId"], "IX_FK_WorkingTimeModelSite_Site");
                        j.IndexerProperty<WorkingTimeModelId>("WorkingTimeModelsId")
                            .HasColumnName("WorkingTimeModels_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<WorkingTimeModelShiftSystem>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("WorkingTimeModelShiftSystems");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.ShiftSystemStart).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.WorkingTimeModelId).HasColumnOrder(iota);
            entity.HasIndex(e => e.WorkingTimeModelId, "IX_FK_WorkingTimeModelWorkingTimeModelShiftSystem");

            entity.Property(e => e.ShiftSystemId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ShiftSystemId, "IX_FK_ShiftSystemWorkingTimeModelShiftSystem");

            entity
                .HasOne(d => d.ShiftSystem)
                .WithMany(p => p.WorkingTimeModelShiftSystems)
                .HasForeignKey(d => d.ShiftSystemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ShiftSystemWorkingTimeModelShiftSystem");

            entity
                .HasOne(d => d.WorkingTimeModel)
                .WithMany(p => p.WorkingTimeModelShiftSystems)
                .HasForeignKey(d => d.WorkingTimeModelId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkingTimeModelWorkingTimeModelShiftSystem");
        });
    }
}
