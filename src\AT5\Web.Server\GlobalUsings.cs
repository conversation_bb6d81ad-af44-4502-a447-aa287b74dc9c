﻿#pragma warning disable // IDE0005 - Using directive is unnecessary.
// Global usings from implicit usings, except we don't want Microsoft.Extensions.Logging because of conflicting ILogger<T>

// System
global using System;
global using System.Collections.Generic;
global using System.IO;
global using System.Linq;
global using System.Net.Http;
global using System.Net.Http.Json;
global using System.Threading;
global using System.Threading.Tasks;
// AristoTelos
global using AT.Utilities.Logging;
// ASP.NET Core specific
global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.Hosting;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Routing;
// Dependency Injection
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.Extensions.Hosting;
