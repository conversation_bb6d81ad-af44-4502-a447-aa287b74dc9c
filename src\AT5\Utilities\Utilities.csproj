﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
		<Platforms>x64</Platforms>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
		<ProjectReference Include="..\Primitives\Primitives.csproj" />
	</ItemGroup>

	<!--Include only trivial class-library packages!-->
	<ItemGroup>
		<PackageReference Include="FluentValidation" />
		<PackageReference Include="Vogen" />

		<!--Definition of Serilog's ILogger.-->
		<PackageReference Include="Serilog" />
		<!--Without this, ReadFrom.Configuration doesn't work.-->
		<PackageReference Include="Serilog.Expressions" />
		<!--AddSerilog extension method for dependency injection.-->
		<PackageReference Include="Serilog.Extensions.Logging" />
		<!--Methods for working with Serilog's configuration.-->
		<PackageReference Include="Serilog.Settings.Configuration" />
		<!--ConfigurationBuilder for Serilog instance creation from config.-->
		<PackageReference Include="Microsoft.Extensions.Configuration" />
	</ItemGroup>

	<ItemGroup>
		<Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
	</ItemGroup>
</Project>
