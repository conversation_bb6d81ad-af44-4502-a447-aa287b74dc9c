﻿namespace AT.Infrastructure.Repositories;

using Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using DataAccess;
using Database;

public sealed class PushNotificationRepository(OrganizationDbContext _dbContext)
    : EfRepository<PushNotification>(_dbContext),
        IPushNotificationRepository,
        IRepositoryWithFactoryMethod<IPushNotificationRepository>
{
    public static new IPushNotificationRepository Create(OrganizationDbContext dbContext)
    {
        return new PushNotificationRepository(dbContext);
    }

    public override void AddForDelete(PushNotification pushNotification)
    {
        // FUTURE: cascading delete on EmailMessageRecipients?
        foreach (var recipient in pushNotification.PushNotificationRecipients)
        {
            DbContext.Remove(recipient);
        }

        DbContext.Remove(pushNotification);
    }
}
