﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;
using Base;

public class Role
{
    public RoleId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool AssignableGlobaly { get; set; }

    public bool AssignableToSite { get; set; }

    public bool AssignableToTeam { get; set; }

    public UserId? SingleUserId { get; set; }

    public bool AssignableToUser { get; set; }

    public string? Code { get; set; }

    public virtual ICollection<PermissionsGroup> PermissionsGroups { get; set; } = new List<PermissionsGroup>();

    public virtual User? SingleUser { get; set; }

    public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    public virtual ICollection<RoleDelegationType> DelegateRoleDelegationTypes { get; set; } =
        new List<RoleDelegationType>();

    public virtual ICollection<PermissionsGroup> ExcludedInPermissionGroups { get; set; } =
        new List<PermissionsGroup>();

    public virtual ICollection<RoleDelegationType> ExcludedInRoleDelegationTypes { get; set; } =
        new List<RoleDelegationType>();

    public virtual ICollection<LicenceRule> LicenceRules { get; set; } = new List<LicenceRule>();

    public virtual ICollection<Role> ManagedRoles { get; set; } = new List<Role>();

    public virtual ICollection<PermissionsGroup> ManagerRoles { get; set; } = new List<PermissionsGroup>();

    public virtual ICollection<Role> ManagingRoles { get; set; } = new List<Role>();

    public virtual ICollection<Position> Positions { get; set; } = new List<Position>();

    public virtual ICollection<Report> Reports { get; set; } = new List<Report>();

    public virtual ICollection<RoleDelegationType> RoleDelegationTypes { get; set; } = new List<RoleDelegationType>();
}
