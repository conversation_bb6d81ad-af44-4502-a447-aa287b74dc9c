namespace AT.UseCases;

using Core.Domain.ExampleEntities;
using Core.Services.Algorithm;
using Quotient = Core.Domain.ExampleEntities.Quotient;

public class Algorithm : IAlgorithm
{
    public async Task<AlgorithmOutput> PerformAsync(AlgorithmInput input)
    {
        // Work simulation
        await Task.Delay(1666);

        return new(Quotient.From(input.Number1.Value / input.Number2.Value));
    }
}
