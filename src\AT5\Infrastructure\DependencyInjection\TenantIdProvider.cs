﻿namespace AT.Infrastructure.DependencyInjection;

/// <summary>
/// Provides initial organization id in dependency injection. Each tenant has own TenantIdProvider registered.
/// Default tenant has <see cref="IsDefaultTenant"/> true, normal tenants have false and <see cref="Id"/> specified.
/// </summary>
public sealed class TenantIdProvider(OrganizationId? _organizationId)
{
    public OrganizationId? OrganizationId { get; } = _organizationId;

    public bool IsDefaultTenant => OrganizationId is null;
}
