﻿namespace AT.Core.Domain.Entities;

public class CampaignType
{
    public DateInterval Interval { get; set; }

    public int Contacts { get; set; }

    public double Priority { get; set; }

    public double DurationEffective { get; set; }

    public double DurationIneffective { get; set; }

    public double DurationNegative { get; set; }

    public int RepetitionCount { get; set; }

    public double TargetContacted { get; set; }

    public double? TargetEffective { get; set; }

    public double RateContacted1 { get; set; }

    public double? RateContacted2 { get; set; }

    public double? RateContacted3 { get; set; }

    public double RateEffective1 { get; set; }

    public double? RateEffective2 { get; set; }

    public double? RateEffective3 { get; set; }

    public RosterItemPartTypeId Id { get; set; }

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;
}
