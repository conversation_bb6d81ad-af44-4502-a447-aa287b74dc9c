﻿<Project Sdk="Microsoft.NET.Sdk">

  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <WarningsAsErrors>Nullable</WarningsAsErrors>
    <IsAspireHost>true</IsAspireHost>
    <UserSecretsId>6004599e-b5d7-4d53-803c-cd70d0b85f3b</UserSecretsId>
    <Platforms>x64</Platforms>
    <RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <!--This must be in here because Directory.packages.props generates AssemblyInfo from Primitives for each project.-->
    <ProjectReference Include="..\JobService\JobService.csproj" />
    <ProjectReference Include="..\Primitives\Primitives.csproj" />

    <ProjectReference Include="..\ApiService\ApiService.csproj" />
    <ProjectReference Include="..\Web.Server\Web.Server.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.AppHost" />
    <PackageReference Include="Aspire.Hosting.Garnet" />
    <PackageReference Include="Aspire.Hosting.Redis" />
  </ItemGroup>

</Project>
