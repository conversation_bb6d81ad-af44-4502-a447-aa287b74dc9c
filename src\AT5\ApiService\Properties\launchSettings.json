{"profiles": {"http": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5337"}, "https": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7488;http://localhost:5337"}, "API_Client": {"commandName": "Project", "commandLineArgs": "--generateclients true"}}, "$schema": "https://json.schemastore.org/launchsettings.json"}