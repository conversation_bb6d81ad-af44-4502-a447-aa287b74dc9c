﻿namespace AT.Core.Domain.RosterItemAggregate;

public interface IPeriodTimeOffWriteService
{
    /// <summary>
    /// The created roster items share one (newly-created) <see cref="ChangeLog"/>.
    /// </summary>
    Task<List<RosterItem>> CreatePeriodTimeOffsAsync(
        IEnumerable<PeriodTimeOffInfo> periodTimeOffInfos,
        CancellationToken cancellationToken = default
    );

    /// <summary>
    /// The update creates a single <see cref="ChangeLog"/>.
    /// </summary>
    Task UpdatePeriodTimeOffsAsync(IEnumerable<RosterItem> rosterItem, CancellationToken cancellationToken = default);
}
