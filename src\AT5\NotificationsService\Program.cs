using AT.Infrastructure.Utilities.DependencyInjection;
using AT.Infrastructure.Utilities.EntityFramework;
using AT.NotificationsService.DependencyInjection;
using CliFx;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;

// Brief description of the NotificationsService project:
// https://dev.azure.com/aristotelos/AristoTelos/_wiki/wikis/Dev.wiki/133/NotificationsService

EntityFrameworkProfiler.InitializeIfDebugging();

// When this project is run as a windows service, we want to treat all paths relative to the executable, not the system folder.
Directory.SetCurrentDirectory(AppDomain.CurrentDomain.BaseDirectory);

var configuration = new ConfigurationBuilder()
    .AddEnvironmentVariables()
    .AddCommandLine(args)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
    .Build();

// If true, attempts to run a console command. Otherwise, runs the notifications service.
if (Environment.UserInteractive && args.Length > 0)
{
    await new CliApplicationBuilder()
        .AddCommandsFromThisAssembly()
        .UseTypeActivator(commandTypes =>
            NotificationsServiceDependencyInjection.CreateSingleOrganizationServiceProviderForCliFx(
                commandTypes,
                configuration
            )
        )
        .Build()
        .RunAsync();

    return;
}

var hostBuilder = Host.CreateDefaultBuilder(args)
    .ConfigureAppConfiguration(builder =>
    {
        builder.Sources.Clear();
        builder.AddConfiguration(configuration);
    });

// Use AutoFac's Multitenant container for resolving services. This call also populates the container with the services registered into builder.Services.
hostBuilder.UseServiceProviderFactory(
    new CustomMultitenantAutofacServiceProviderFactory(
        NotificationsServiceDependencyInjection.CreateMultitenantContainer,
        containerBuilder => NotificationsServiceDependencyInjection.Populate(containerBuilder, configuration)
    )
);

var host = hostBuilder.UseWindowsService().Build();

await host.RunAsync();
