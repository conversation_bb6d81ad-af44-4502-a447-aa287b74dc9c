﻿namespace AT.Core.MasterDomain.Entities;

public class Organization
{
    public OrganizationId Id { get; set; }

    public string Name { get; set; } = null!;

    public string UrlId { get; set; } = null!;

    public string DatabaseName { get; set; } = null!;

    public string HeaderTitle { get; set; } = null!;

    public string? HeaderColor { get; set; }

    public virtual ICollection<Service> Services { get; set; } = new List<Service>();
}
