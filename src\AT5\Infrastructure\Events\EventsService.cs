﻿namespace AT.Infrastructure.Events;

using AT.Utilities.Logging;
using AT.Utilities.Parsing;
using StackExchange.Redis;

public class EventsService(
    ILogger<EventsService> _log,
    IConnectionMultiplexer _connectionMultiplexer,
    IJsonParser _jsonParser
) : IEventsPublisher, IEventsSubscriber
{
    public async Task PublishAsync(string channel, string message)
    {
        var subscriber = _connectionMultiplexer.GetSubscriber();
        await subscriber.PublishAsync(RedisChannel.Literal(channel), message);
    }

    public Task PublishAsync<T>(string channel, T message)
    {
        var serializedMessage = _jsonParser.Serialize(message);
        return PublishAsync(channel, serializedMessage);
    }

    public void Publish(string channel, string message)
    {
        var subscriber = _connectionMultiplexer.GetSubscriber();
        subscriber.Publish(RedisChannel.Literal(channel), message);
    }

    public void Publish<T>(string channel, T message)
    {
        var serializedMessage = _jsonParser.Serialize(message);
        Publish(channel, serializedMessage);
    }

    public async Task SubscribeAsync(string channel, Action<string, string> handler)
    {
        var subscriptionHandler = CreateSubscriptionHandler(channel, handler);
        var subscriber = _connectionMultiplexer.GetSubscriber();
        await subscriber.SubscribeAsync(RedisChannel.Literal(channel), subscriptionHandler);
    }

    public Task SubscribeAsync<T>(string channel, Action<EventContext<T>> handler)
    {
        var untypedHandler = CreateUntypedHandler(handler);
        return SubscribeAsync(channel, untypedHandler);
    }

    public void Subscribe(string channel, Action<string, string> handler)
    {
        var subscriptionHandler = CreateSubscriptionHandler(channel, handler);
        var subscriber = _connectionMultiplexer.GetSubscriber();
        subscriber.Subscribe(RedisChannel.Literal(channel), subscriptionHandler);
    }

    public void Subscribe<T>(string channel, Action<EventContext<T>> handler)
    {
        var untypedHandler = CreateUntypedHandler(handler);
        Subscribe(channel, untypedHandler);
    }

    private Action<RedisChannel, RedisValue> CreateSubscriptionHandler(
        string channel,
        Action<string, string> untypedHandler
    )
    {
        void subscriptionHandler(RedisChannel redisChannel, RedisValue redisValue)
        {
            if (redisChannel.IsNullOrEmpty)
            {
                _log.Error($"Cannot handle message on channel '{channel}'. Channel is empty.");
                return;
            }

            if (redisValue.IsNullOrEmpty)
            {
                _log.Error($"Cannot handle message on channel '{channel}'. Value is empty.");
                return;
            }

            untypedHandler(redisChannel!, redisValue!);
        }

        return subscriptionHandler;
    }

    private Action<string, string> CreateUntypedHandler<T>(Action<EventContext<T>> handler)
    {
        void untypedHandler(string channel, string serializedMessage)
        {
            if (!_jsonParser.TryDeserialize<T>(serializedMessage, out var value) || value is null)
            {
                _log.Error(
                    $"Failed to deserialize redis value '{serializedMessage}' when handling message on channel '{channel}'."
                );
                return;
            }

            var context = new EventContext<T>(channel, value);
            handler(context);
        }

        return untypedHandler;
    }
}
