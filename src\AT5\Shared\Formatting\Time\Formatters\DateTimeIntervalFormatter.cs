﻿namespace AT.Shared.Formatting.Time.Formatters;

using System;
using System.Globalization;
using AT.DataStructures.Time;
using AT.Translations;
using AT.Translations.Formatting.Time;
using SmartFormat;

public sealed class DateTimeIntervalFormatter(ITranslator _translator)
{
    public string Format(DateTimeInterval value, DateTimeIntervalFormattingType type, Language? language = null)
    {
        return type switch
        {
            DateTimeIntervalFormattingType.Standard => FormatStandard(value, language),
            DateTimeIntervalFormattingType.Short => FormatShort(value, language),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {type} is unknown"),
#else
            _ => FormatStandard(value, language)
#endif
        };
    }

    public string FormatStandard(DateTimeInterval value, Language? language = null)
    {
        var dateFormat = _translator.Translate<TimeFormatTranslations.Date>(language);
        var timeFormat = _translator.Translate<TimeFormatTranslations.Time>(language);

        string startDate = value.Start.Date.ToString(dateFormat.Value, CultureInfo.InvariantCulture);
        string endDate = value.End.Date.ToString(dateFormat.Value, CultureInfo.InvariantCulture);
        string startTime = TimeOnly.FromDateTime(value.Start).ToString(timeFormat.Value, CultureInfo.InvariantCulture);
        string endTime = TimeOnly.FromDateTime(value.End).ToString(timeFormat.Value, CultureInfo.InvariantCulture);

        return Smart.Format(
            CultureInfo.InvariantCulture,
            "{startDate} {startTime} - {endDate} {endTime}",
            startDate,
            startTime,
            endDate,
            endTime
        );
    }

    public string FormatShort(DateTimeInterval value, Language? language = null)
    {
        var dateFormat = _translator.Translate<TimeFormatTranslations.DateShort>(language);
        var timeFormat = _translator.Translate<TimeFormatTranslations.TimeShort>(language);

        string startDate = value.Start.Date.ToString(dateFormat.Value, CultureInfo.InvariantCulture);
        string endDate = value.End.Date.ToString(dateFormat.Value, CultureInfo.InvariantCulture);
        string startTime = TimeOnly.FromDateTime(value.Start).ToString(timeFormat.Value, CultureInfo.InvariantCulture);
        string endTime = TimeOnly.FromDateTime(value.End).ToString(timeFormat.Value, CultureInfo.InvariantCulture);

        if (value.Start.Date == value.End.Date)
        {
            return Smart.Format(
                CultureInfo.InvariantCulture,
                "{startDate} {startTime}-{endTime}",
                startDate,
                startTime,
                endTime
            );
        }

        if (value.Start.TimeOfDay == TimeSpan.Zero && value.End.TimeOfDay == TimeSpan.Zero)
        {
            string endDatePreviousDay = value.End.AddDays(-1).ToString(dateFormat.Value, CultureInfo.InvariantCulture);

            // FUTURE: Isn't it weird that we print e.g. "5.5.2025-5.5.2025" when the interval is a single day?
            return Smart.Format(
                CultureInfo.InvariantCulture,
                "{startDate}-{endDatePreviousDay}",
                startDate,
                endDatePreviousDay
            );
        }

        return Smart.Format(
            CultureInfo.InvariantCulture,
            "{startDate} {startTime} - {endDate} {endTime}",
            startDate,
            startTime,
            endDate,
            endTime
        );
    }
}
