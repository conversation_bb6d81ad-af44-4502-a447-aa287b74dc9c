﻿namespace AT.Shared.Models.Demo;

using FastEndpoints;

public sealed class City
{
    public int Id { get; set; }

    public required string Name { get; init; }

    public int Population { get; init; }
}

public sealed class GetCitiesResponse
{
    public required City[] Cities { get; set; }
}

public sealed class GetCityResponse
{
    public required City City { get; set; }
}

public sealed class PostCityResponse
{
    public required City City { get; set; }
}

public sealed class PostCityRequest
{
    public required string Name { get; set; }

    public int Population { get; set; }
}

public sealed class GetCitiesRequest
{
    // The QueryParam attribute is for <PERSON>wagger to know that this is a query parameter. It doesn't seem to work right now.
    // It still thinks it comes from body.
    [QueryParam]
    public int? MinId { get; set; }

    // The QueryParam attribute is for Swagger to know that this is a query parameter. It doesn't seem to work right now.
    // It still thinks it comes from body.
    [QueryParam]
    public int? MaxId { get; set; }
}

public sealed class GetCityRequest
{
    public int CityId { get; set; }
}

public sealed class DeleteCityRequest
{
    public int CityId { get; set; }
}
