using AT.ApiService.DependencyInjection;
using AT.Infrastructure.Utilities.EntityFramework;

EntityFrameworkProfiler.InitializeIfDebugging();

// When this project is run as a windows service, we want to treat all paths relative to the executable, not the system folder.
Directory.SetCurrentDirectory(AppDomain.CurrentDomain.BaseDirectory);

var configuration = new ConfigurationBuilder()
    .AddEnvironmentVariables()
    .AddCommandLine(args)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
    .Build();

var builder = WebApplication.CreateBuilder(args).AddServiceDefaults(); // Add service defaults & Aspire client integrations.

builder.AddRedisClient("AristoTelosCache");

// External libraries provide a registration to the Microsoft's IServiceCollection. Therefore, we cannot register them directly
// to the AutoFac's container. Instead, the AutoFac loads them from builder.Services. Thus, register them in there.
builder.Services.AddNonAutofacApiServiceServices(configuration);

// Use AutoFac's Multitenant container for resolving services. This call also populates the container with the services registered into builder.Services.
builder.Host.UseServiceProviderFactory(
    new AutofacMultitenantServiceProviderFactory(
        ApiServiceDependencyInjection.CreateMultitenantContainer,
        containerBuilder =>
            ApiServiceDependencyInjection.PopulateWithAutoFacRegistrations(containerBuilder, configuration)
    )
);

var app = builder.Build();

await app.ConfigureApiServiceAsync();

await app.RunAsync();
