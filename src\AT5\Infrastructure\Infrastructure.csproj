﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
        <Platforms>x64</Platforms>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Core\Core.csproj" />
		<ProjectReference Include="..\UseCases\UseCases.csproj" />
        <ProjectReference Include="..\Infrastructure.Utilities\Infrastructure.Utilities.csproj" />

		<ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
		<ProjectReference Include="..\Primitives\Primitives.csproj" />
		<ProjectReference Include="..\DataStructures\DataStructures.csproj" />
		<ProjectReference Include="..\Utilities\Utilities.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Autofac.Extensions.DependencyInjection" />
        <PackageReference Include="Autofac.Multitenant" />
        <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" />
        <PackageReference Include="LinqKit.Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="StackExchange.Redis" />
        <PackageReference Include="System.IO.Abstractions" />
        <PackageReference Include="Quartz" VersionOverride="3.14.0" />
        <PackageReference Include="Quartz.Extensions.DependencyInjection" VersionOverride="3.14.0" />
    </ItemGroup>

</Project>