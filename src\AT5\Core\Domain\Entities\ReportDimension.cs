﻿namespace AT.Core.Domain.Entities;

using Base;

public class ReportDimension
{
    public ReportDimensionId Id { get; set; }

    public int Rank { get; set; }

    public string Name { get; set; } = null!;

    public string Method { get; set; } = null!;

    public ReportId ReportId { get; set; }

    public string? Parametes { get; set; }

    public int? SortingRank { get; set; }

    public bool SortDescending { get; set; }

    public bool Hidden { get; set; }

    public virtual Report Report { get; set; } = null!;

    public virtual ICollection<ReportSubtotal> Subtotals { get; set; } = new List<ReportSubtotal>();
}
