﻿namespace AT.Infrastructure.DependencyInjection.Extensions;

using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.Database;
using AT.Infrastructure.MasterDbQueries;
using AT.Utilities.EntityFramework;
using Autofac;
using Autofac.Builder;
using Autofac.Core.Resolving.Pipeline;
using Autofac.Multitenant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

public static class DbContextDepenencyInjectionExtensions
{
    // FUTURE: Generalize "MasterDb" string.
    public static IServiceCollection AddMasterDatabaseServices(
        this IServiceCollection serviceCollection,
        IConfiguration configuration
    )
    {
        serviceCollection.AddDbContextFactory<MasterDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("MasterDb"))
        );

        serviceCollection.AddTransient<IFullOrgIdsQuery, FullOrgIdsQuery>();
        serviceCollection.AddTransient<IOrganizationInfoQuery, OrganizationInfoQuery>();

        return serviceCollection;
    }

    public static ContainerBuilder AddOrgDbContextServicesFromOrganizationDbTemplate(
        this ContainerBuilder containerBuilder,
        IConfiguration configuration
    )
    {
        // Prepare DbContext registrations to .NET ServiceCollection.
        var serviceCollection = new ServiceCollection();

        string? orgDbTemplate = configuration.GetConnectionString("OrgDbTemplate");
        if (string.IsNullOrEmpty(orgDbTemplate))
        {
            throw new InvalidOperationException(
                "OrgDbTemplate not found in the ConnectionStrings section of the configuration."
            );
        }

        string getOrgDbConnectionString(OrgDbName orgDbName)
        {
            return EntityFrameworkUtils.CreateConnectionStringForDatabase(orgDbTemplate, orgDbName.DatabaseName);
        }

        serviceCollection.AddDbContext<OrganizationDbContext>(
            (provider, options) =>
            {
                string connectionString = getOrgDbConnectionString(provider.GetRequiredService<OrgDbName>());
                options.UseSqlServer(connectionString);
            }
        );

        serviceCollection.AddDbContextFactory<OrganizationDbContext>(
            (provider, options) =>
            {
                string connectionString = getOrgDbConnectionString(provider.GetRequiredService<OrgDbName>());
                options.UseSqlServer(connectionString);
            }
        );

        serviceCollection.AddDbContext<OrganizationDbContextReadOnly>(
            (provider, options) =>
            {
                string connectionString = getOrgDbConnectionString(provider.GetRequiredService<OrgDbName>());
                options.UseSqlServer(connectionString);
            }
        );

        // This is probably not needed but will keep it for now.
        serviceCollection.AddDbContextFactory<OrganizationDbContextReadOnly>(
            (provider, options) =>
            {
                string connectionString = getOrgDbConnectionString(provider.GetRequiredService<OrgDbName>());
                options.UseSqlServer(connectionString);
            }
        );

        // Add the registrations to Autofac container.
        // Based on Autofac Populate() implementation, but only covers needed cases.
        foreach (var serviceDescriptor in serviceCollection)
        {
            if (serviceDescriptor.ImplementationType is not null)
            {
                containerBuilder
                    .RegisterType(serviceDescriptor.ImplementationType)
                    .As(serviceDescriptor.ServiceType)
                    .ConfigureLifecycle(serviceDescriptor.Lifetime)
                    .AddLifetimeCheckEventToDbContext();
            }
            else if (serviceDescriptor.ImplementationFactory is not null)
            {
                var registration = RegistrationBuilder
                    .ForDelegate(
                        serviceDescriptor.ServiceType,
                        (context, parameters) =>
                        {
                            var serviceProvider = context.Resolve<IServiceProvider>();
                            return serviceDescriptor.ImplementationFactory(serviceProvider);
                        }
                    )
                    .As(serviceDescriptor.ServiceType)
                    .ConfigureLifecycle(serviceDescriptor.Lifetime)
                    .CreateRegistration();

                containerBuilder.RegisterComponent(registration);
            }
            else
            {
                throw new InvalidOperationException("ImplementationType or ImplementationFactory expected.");
            }
        }

        return containerBuilder;
    }

    // Based on Autofac Populate() implementation but singletons are registered per tenant.
    private static IRegistrationBuilder<object, TActivatorData, TRegistrationStyle> ConfigureLifecycle<
        TActivatorData,
        TRegistrationStyle
    >(
        this IRegistrationBuilder<object, TActivatorData, TRegistrationStyle> registrationBuilder,
        ServiceLifetime lifecycleKind
    )
    {
        switch (lifecycleKind)
        {
            case ServiceLifetime.Singleton:
                registrationBuilder.InstancePerTenant();
                break;
            case ServiceLifetime.Scoped:
                registrationBuilder.InstancePerLifetimeScope();
                break;
            case ServiceLifetime.Transient:
                registrationBuilder.InstancePerDependency();
                break;
        }

        return registrationBuilder;
    }

    private static IRegistrationBuilder<object, TActivatorData, TRegistrationStyle> AddLifetimeCheckEventToDbContext<
        TActivatorData,
        TRegistrationStyle
    >(this IRegistrationBuilder<object, TActivatorData, TRegistrationStyle> registrationBuilder)
        where TActivatorData : ReflectionActivatorData
    {
#if DEBUG
        // Add check that we are not resolving OrganizationDbContext in root or tenantLifetim scopes.
        if (
            registrationBuilder.ActivatorData.ImplementationType == typeof(OrganizationDbContext)
            || registrationBuilder.ActivatorData.ImplementationType == typeof(OrganizationDbContextReadOnly)
        )
        {
            return registrationBuilder.OnActivating(e =>
            {
                // Ugly but works for now.
                var context = (ResolveRequestContext)e.Context;
                CheckThatWeAreUnderTenantLifetimeScopeOrThrow(context.ActivationScope);
            });
        }
#endif

        return registrationBuilder;
    }

    // This method is only unused in DEBUG mode
#pragma warning disable S1144
    private static void CheckThatWeAreUnderTenantLifetimeScopeOrThrow(ILifetimeScope lifetimeScope)
    {
        if (lifetimeScope.Tag.Equals("root") || lifetimeScope.Tag.Equals("tenantLifetime"))
        {
            throw new InvalidOperationException(
                $"Lifetime scope: {lifetimeScope.Tag}. "
                    + $"DbContext can only be resolved in a scope under tenant lifetime. "
                    + $"It cannot be resolved in a root or tenantLifetime scope."
            );
        }
    }
#pragma warning restore S1144
}
