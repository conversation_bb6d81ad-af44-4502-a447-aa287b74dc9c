namespace AT.Utilities.Parsing;

using System.Diagnostics.CodeAnalysis;

/// <summary>
/// Provides methods for serializing and deserializing JSON data.
/// </summary>
public interface IJsonParser
{
    /// <summary>
    /// Attempts to deserialize the specified JSON string to an object of type T.
    /// </summary>
    /// <typeparam name="T">The type of the object to deserialize to.</typeparam>
    /// <param name="jsonData">The JSON string to deserialize.</param>
    /// <param name="result">The resulting deserialized object, if successful.</param>
    /// <returns>True if the deserialization was successful; otherwise, false.</returns>
    bool TryDeserialize<T>([StringSyntax(StringSyntaxAttribute.Json)] string jsonData, out T? result);

    /// <summary>
    /// Attempts to deserialize the specified JSON string to an object of type specified by resultType.
    /// </summary>
    /// <param name="jsonData">The JSON string to deserialize.</param>
    /// <param name="resultType">The type of the object to deserialize to.</typeparam>
    /// <param name="result">The resulting deserialized object, if successful.</param>
    /// <returns>True if the deserialization was successful; otherwise, false.</returns>
    bool TryDeserialize(
        [StringSyntax(StringSyntaxAttribute.Json)] string jsonData,
        Type resultType,
        out object? result
    );

    /// <summary>
    /// Serializes the specified object to a JSON string.
    /// </summary>
    /// <typeparam name="T">The type of the object to serialize.</typeparam>
    /// <param name="value">The object to serialize.</param>
    /// <param name="prettyPrint">True to format the JSON string with indentation for human readability; otherwise, false.</param>
    /// <returns>A JSON string representation of the object.</returns>
    string Serialize<T>(T value, bool prettyPrint = true);
}
