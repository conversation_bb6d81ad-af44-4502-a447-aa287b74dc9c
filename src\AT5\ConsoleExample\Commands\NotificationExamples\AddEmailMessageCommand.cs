namespace AT.ConsoleExample.Commands.NotificationExamples;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.PrimitivesAT5.Ids;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

[Command("emailMessage add", Description = "Adds a new email message")]
public class AddEmailMessageCommand(IEmailMessageRepository _emailMessageRepository) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        EmailMessageRecipient emailMessageRecipient = new() { UserId = UserId.From(2), Address = "<EMAIL>" };

        EmailMessage emailMessage =
            new()
            {
                Subject = "Test subject",
                Body = "Test body",
                EmailMessageRecipients = [emailMessageRecipient],
                Generated = DateTime.Now,
                TrackingId = Guid.NewGuid(),
            };

        _emailMessageRepository.AddForInsert(emailMessage);

        await _emailMessageRepository.CommitAsync();
    }
}
