﻿namespace AT.Infrastructure.Jobs.DependencyInjection;

using AT.Infrastructure.Jobs.QuatzWrappers;
using Microsoft.Extensions.DependencyInjection;
using Quartz;

public static class CustomQuartzModule
{
    public static IServiceCollection AddQuartzCustom(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddQuartz();
        serviceCollection.AddSingleton<IQuartzSchedulerProvider, QuartzSchedulerProvider>();
        return serviceCollection;
    }
}
