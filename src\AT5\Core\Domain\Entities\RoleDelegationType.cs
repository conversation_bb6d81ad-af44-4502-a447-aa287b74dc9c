﻿namespace AT.Core.Domain.Entities;

public class RoleDelegationType
{
    public RoleDelegationTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public Validity Validity { get; set; }

    public DelegateOrganizationUnits DelegateOrganizationUnits { get; set; }

    public int? OrganizationStructureLevelsDown { get; set; }

    public int? OrganizationStructureLevelsUp { get; set; }

    public int? MaxDays { get; set; }

    public int? MaxDaysBeforeStart { get; set; }

    public virtual ICollection<RoleDelegation> RoleDelegations { get; set; } = new List<RoleDelegation>();

    public virtual ICollection<Role> DelegateRoles { get; set; } = new List<Role>();

    public virtual ICollection<Role> ExcludedSubordinateRoles { get; set; } = new List<Role>();

    public virtual ICollection<Permission> PermissionsTypes { get; set; } = new List<Permission>();

    public virtual ICollection<Role> Roles { get; set; } = new List<Role>();
}
