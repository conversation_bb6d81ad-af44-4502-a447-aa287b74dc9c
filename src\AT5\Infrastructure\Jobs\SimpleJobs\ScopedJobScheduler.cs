﻿namespace AT.Infrastructure.Jobs.SimpleJobs;

using AT.Core.MasterDomain;
using AT.Core.SimpleJobs;
using AT.Infrastructure.DependencyInjection;
using AT.Infrastructure.Jobs.QuatzWrappers;
using AT.Utilities.Logging;
using AT.Utilities.Reflection;
using Quartz;

public sealed class ScopedJobScheduler(
    ILogger<ScopedJobScheduler> _logger,
    IQuartzSchedulerProvider _quartzSchedulerProvider,
    IOrgServiceProvider _orgServiceProvider,
    FullOrgId _fullOrgId
) : ISimpleJobScheduler
{
    public async Task ScheduleJob<TJob>(AT.Utilities.Cron.CronExpression cron, string jobName, CancellationToken ct)
        where TJob : class, ISimpleJob
    {
        var scheduler = await _quartzSchedulerProvider.GetStartedScheduler(ct);

        var jobKey = CreateJobKey<TJob>(jobName);
        var jobTriggerName = $"{jobKey.Name}_trigger";

        var jobParams = new ScopedJobWrapperContext(_orgServiceProvider);
        var jobDataMap = new JobDataMap() { { nameof(ScopedJobWrapperContext), jobParams } };

        var jobDetail = JobBuilder
            .Create<NonconcurrentScopedJobWrapper<TJob>>()
            .UsingJobData(jobDataMap)
            .WithIdentity(jobKey)
            .Build();

        var jobTrigger = TriggerBuilder
            .Create()
            .WithIdentity(jobTriggerName, _fullOrgId.DatabaseName)
            .WithCronSchedule(cron.Expression)
            .Build();

        _logger.Info(
            "Scheduling job {JobKey} with trigger {JobTriggerName}, cron: {Cron}.",
            jobKey,
            jobTriggerName,
            cron
        );
        await scheduler.ScheduleJob(jobDetail, jobTrigger, ct);
    }

    private JobKey CreateJobKey<TJob>(string jobName)
        where TJob : class, ISimpleJob
    {
        var fullJobName = GetFullJobName<TJob>(jobName);
        return new JobKey(fullJobName, _fullOrgId.DatabaseName);
    }

    private string GetFullJobName<TJob>(string jobName)
        where TJob : class, ISimpleJob
    {
        var jobTypeName = typeof(TJob).GetShortName();
        return $"Org-{_fullOrgId.DatabaseName}_{jobTypeName}_{jobName}";
    }
}
