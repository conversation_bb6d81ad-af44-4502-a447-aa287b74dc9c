﻿namespace AT.Infrastructure.Loops;

using System.Threading;
using System.Threading.Tasks;
using AT.Core.Loops;
using AT.Infrastructure.DependencyInjection;

public class Loop<TLoopIteration, TLoopContext>(
    IOrgServiceProvider _orgServiceProvider,
    TimeSpan _delay,
    TLoopContext _loopContext
) : ILoop
    where TLoopIteration : class, ILoopIteration<TLoopContext>
{
    private CancellationTokenSource? _cts;
    private Task? _loopTask;

    public Task StartAsync(CancellationToken tk = default)
    {
        if (_loopTask != null && !_loopTask.IsCompleted)
        {
            throw new InvalidOperationException("Loop is already running.");
        }

        _cts = CancellationTokenSource.CreateLinkedTokenSource(tk);
        _loopTask = Task.Run(() => RunLoopAsync(_cts.Token), tk);

        return Task.CompletedTask;
    }

    public async Task StopAsync()
    {
        if (_cts is null)
        {
            return;
        }

        await _cts.CancelAsync();
    }

    private async Task RunLoopAsync(CancellationToken token)
    {
        while (!token.IsCancellationRequested)
        {
            try
            {
                using var scopedResolver = _orgServiceProvider.BeginScope();
                var iteration = scopedResolver.Resolve<TLoopIteration>();
                await iteration.Execute(_loopContext, token);
            }
            catch (OperationCanceledException) when (token.IsCancellationRequested)
            {
                break;
            }
            catch (Exception ex)
            {
                // Handle/log exception as needed
                Console.WriteLine($"Loop iteration error: {ex}");
            }

            try
            {
                await Task.Delay(_delay, token);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
    }

    public void Dispose()
    {
        _cts?.Dispose();
    }
}
