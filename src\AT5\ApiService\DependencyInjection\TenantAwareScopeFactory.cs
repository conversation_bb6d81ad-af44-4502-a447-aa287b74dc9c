﻿namespace AT.ApiService.DependencyInjection;

using AT.Infrastructure.DependencyInjection;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Autofac.Multitenant;

// Autofac ServiceScopeFactory is here: https://github.com/autofac/Autofac.Extensions.DependencyInjection/blob/f2170d3d4f8a5dcde556ae1a776c83a5c3d6c892/src/Autofac.Extensions.DependencyInjection/AutofacServiceScopeFactory.cs
// Our class, added so that initialization of Fastendpoints works.
internal class TenantAwareScopeFactory(
    ILifetimeScope _currentLifetimeScope,
    ITenantIdentificationStrategy _tenantIdentificationStrategy,
    AppServiceProvider _appServiceProvider
) : IServiceScopeFactory
{
    public IServiceScope CreateScope()
    {
        var lifetimeScopeToUse = _currentLifetimeScope;

        // In ApiService we want to always resolve in a specific tenant, never in a root scope.
        // This is needed for FastEndpoints (and other) initialization because controller have to be resolved in a tenant (be it a default tenant).
        // And we don't want to "BeginLifetimeScope" directly on a MultitenantContainer because we want to initialize each container first (done inside AppServiceProvider).
        if (_currentLifetimeScope is MultitenantContainer || _currentLifetimeScope.Tag.Equals("root"))
        {
            _tenantIdentificationStrategy.TryIdentifyTenant(out object? tenantId);

            // Uses default tenant if tenantId is null.
            var tenantLifetime = _appServiceProvider.GetTenantLifetimeScopeUntyped(tenantId);
            lifetimeScopeToUse = tenantLifetime;
        }

        return new AristoTelosServiceScope(lifetimeScopeToUse.BeginLifetimeScope());
    }

    // Pretty much copied from Autofac.Extensions.DependencyInjection.AutofacServiceScope
    internal sealed class AristoTelosServiceScope(ILifetimeScope _lifetimeScope) : IServiceScope
    {
        private readonly AutofacServiceProvider _autofacServiceProvider = new AutofacServiceProvider(_lifetimeScope);

        public IServiceProvider ServiceProvider => _autofacServiceProvider;

        public void Dispose()
        {
            _autofacServiceProvider.Dispose();
        }
    }
}
