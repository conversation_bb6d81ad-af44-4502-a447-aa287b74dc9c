﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.ApprovalsAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to approvals in the database.
/// </summary>
internal static class ApprovalsGroupModelBuilder
{
    public static void BuildApprovalsGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Approval>(entity =>
        {
            Iota iota = new();

            entity.ToTable("Approvals");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.ApprovalTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ApprovalTypeId, "IX_FK_ApprovalApprovalType");

            entity.Property(e => e.Status).HasColumnOrder(iota);

            entity.Property(e => e.RosterItemId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RosterItemId, "IX_FK_ApprovalRosterItem");

            entity.Property(e => e.LastChanged).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.LastChangedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.LastChangedById, "IX_FK_ApprovalUser");

            entity.Property(e => e.LastNote).HasMaxLength(255).HasColumnOrder(iota);

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_ApprovalEmployee");

            entity
                .HasOne(d => d.ApprovalType)
                .WithMany(p => p.Approvals)
                .HasForeignKey(d => d.ApprovalTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalApprovalType");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany()
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalRosterItem");

            entity
                .HasOne(d => d.LastChangedBy)
                .WithMany()
                .HasForeignKey(d => d.LastChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalUser");

            entity
                .HasOne(d => d.Employee)
                .WithMany()
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalEmployee");
        });

        modelBuilder.Entity<ApprovalType>(entity =>
        {
            Iota iota = new();

            entity.ToTable("ApprovalTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(50).HasColumnOrder(iota);

            entity.Property(e => e.Kind).HasColumnOrder(iota);

            entity.Property(e => e.Parameters).HasMaxLength(4000).HasColumnOrder(iota);
        });

        modelBuilder.Entity<ApprovalHistory>(entity =>
        {
            Iota iota = new();

            entity.ToTable("ApprovalHistories");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.ApprovalId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ApprovalId, "IX_FK_ApprovalHistoryApproval");

            entity.Property(e => e.Status).HasColumnOrder(iota);

            entity.Property(e => e.Note).IsRequired(false).HasMaxLength(255).HasColumnOrder(iota);

            entity.Property(e => e.Changed).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.ChangedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.ChangedById, "IX_FK_ApprovalHistoryUser");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_ApprovalHistoryEmployee");

            entity
                .HasOne(d => d.Approval)
                .WithMany(x => x.ApprovalHistories)
                .HasForeignKey(d => d.ApprovalId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalHistoryApproval");

            entity
                .HasOne(d => d.ChangedBy)
                .WithMany()
                .HasForeignKey(d => d.ChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalHistoryUser");

            entity
                .HasOne(d => d.Employee)
                .WithMany()
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalHistoryEmployee");
        });

        modelBuilder.Entity<ApprovalTypeFilter>(entity =>
        {
            Iota iota = new();

            entity.ToTable("ApprovalTypeFilters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.FilterId).HasColumnOrder(iota);
            entity.HasIndex(e => e.FilterId, "IX_FK_ApprovalTypeFilterFilter2");

            entity.Property(e => e.ApprovalTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ApprovalTypeId, "IX_FK_ApprovalTypeApprovalTypeFilter");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Filter)
                .WithMany()
                .HasForeignKey(d => d.FilterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalTypeFilterFilter2");

            entity
                .HasOne(d => d.ApprovalType)
                .WithMany(x => x.Filters)
                .HasForeignKey(d => d.ApprovalTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ApprovalTypeApprovalTypeFilter");
        });
    }
}
