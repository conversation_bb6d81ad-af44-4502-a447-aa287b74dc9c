﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
        <Platforms>x64</Platforms>
        <LangVersion>13.0</LangVersion>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
      <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Enums\Language.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Language.tt</DependentUpon>
      </Compile>
    </ItemGroup>

    <ItemGroup>
      <None Update="Enums\Language.tt">
        <Generator>TextTemplatingFileGenerator</Generator>
        <LastGenOutput>Language.cs</LastGenOutput>
      </None>
    </ItemGroup>

</Project>