﻿namespace AT.ApiService.Endpoints.Demo;

using System.Threading;
using System.Threading.Tasks;
using AT.Shared.Models.Demo;
using FastEndpoints;

public class GetCityEndpoint(CitiesRepository citiesRepository) : Endpoint<GetCityRequest, GetCityResponse>
{
    public override void Configure()
    {
        Get("/demo/cities/{CityId}");
        AllowAnonymous();
    }

    public override async Task HandleAsync(GetCityRequest req, CancellationToken ct)
    {
        var city = citiesRepository.GetCity(req.CityId);
        if (city is null)
        {
            await SendNotFoundAsync(ct);
            return;
        }

        await SendAsync(new GetCityResponse() { City = city, }, cancellation: ct);
    }
}
