﻿namespace AT.NotificationsService.General;

using AT.Core.Domain.NotificationMessageAggregates;
using AT.NotificationsService.General.Interfaces;
using AT.Utilities.Logging;

public sealed class MessagesProcessor<TMessageType, TMessageTypeRecipient>(
    ILogger<MessagesProcessor<TMessageType, TMessageTypeRecipient>> _logger,
    IMessageProvider<TMessageType> _messageProvider,
    IMessageService<TMessageType> _messageService,
    IMessageLogger<TMessageType, TMessageTypeRecipient> _messageLogger
) : IMessageProcessor
    where TMessageType : INotificationMessage
    where TMessageTypeRecipient : INotificationMessageRecipient
{
    public async Task<int> ProcessMessages(int maxBatchSize, CancellationToken cancellationToken)
    {
        var messages = await _messageProvider.GetInstantMessagesAsync(maxBatchSize);
        if (messages.Count == 0)
        {
            return 0;
        }

        _logger.Info("Loaded {MessageCount} messages...", messages.Count);

        messages.ForEach(_messageLogger.LogMessageLoad);

        var sendMessagesResult = await _messageService.SendMessagesAsync(messages, cancellationToken);

        if (!sendMessagesResult.Success)
        {
            _logger.Warn(
                "SendMessages failed completely for {FailedMessagesCount} messages and for {PartiallyFailedMessagesCount} messages partially.",
                sendMessagesResult.FailedMessages.Count,
                sendMessagesResult.PartiallyFailedMessages.Count
            );

            // Intentionally not `return` in here -- even if the send failed, we want to mark the message as processed.
        }

        await _messageProvider.MarkAsProcessedAsync(messages);
        messages.ForEach(_messageLogger.LogMessageProcessed);

        return messages.Count;
    }
}
