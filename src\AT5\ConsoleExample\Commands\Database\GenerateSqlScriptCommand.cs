﻿namespace AT.ConsoleExample.Commands.Database;

using AT.Infrastructure.Database;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Microsoft.EntityFrameworkCore;

[Command("db generate-org-creation-script", Description = "Generates SQL script that creates DB for organization.")]
public class GenerateSqlScriptCommand(OrganizationDbContext _rawDbContext) : ICommand
{
    [CommandOption("path", 'p', Description = "File path to generate the script to.")]
    public required string FilePath { get; set; }

    public async ValueTask ExecuteAsync(IConsole console)
    {
        var createScript = _rawDbContext.Database.GenerateCreateScript();
        await File.WriteAllTextAsync(FilePath, createScript);
    }
}
