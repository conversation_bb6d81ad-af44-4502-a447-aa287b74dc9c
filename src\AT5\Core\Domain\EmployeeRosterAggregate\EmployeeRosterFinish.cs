﻿namespace AT.Core.Domain.EmployeeRosterAggregate;

public class EmployeeRosterFinish
{
    public EmployeeRosterFinishId Id { get; set; }

    public SiteId SiteId { get; set; }

    public UserId EmployeeId { get; set; }

    public DateInterval DateInterval { get; set; }

    public AttendanceFinishState State { get; set; }

    public UserId? FinishAuthorId { get; set; }

    public DateTime? FinishTimeStamp { get; set; }

    public bool Locked { get; set; }

    public UserId? LockAuthorId { get; set; }

    public DateTime? LockTimeStamp { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
