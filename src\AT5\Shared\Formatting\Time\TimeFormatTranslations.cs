﻿namespace AT.Shared.Formatting.Time;

using AT.Translations;

[ResxSource(outputFileName: "TimeFormat")]
public static class TimeFormatTranslations
{
    public record Date : ITranslationNoParameters;

    public record DateShort : ITranslationNoParameters;

    public record DateWithMonthAndYearOnly : ITranslationNoParameters;

    public record DateTime : ITranslationNoParameters;

    public record DateTimeShort : ITranslationNoParameters;

    public record Time : ITranslationNoParameters;

    public record TimeShort : ITranslationNoParameters;

    public record TimeWithoutSeconds : ITranslationNoParameters;
}
