﻿namespace AT.Core.Jobs.JobService;

using System.Collections.Immutable;
using AT.Core.Jobs.DurableJobs;
using AT.Core.MasterDomain;
using AT.Primitives.Enums;
using AT.Utilities.Logging;
using AT.Utilities.Parsing;

public class JobServiceBuilder(
    ILogger<JobService> _jobServiceLogger,
    IDurableJobSeriesScheduler _scopedJobSeriesScheduler,
    FullOrgId _fullOrgId,
    IJsonParser _jsonParser
) : IJobServiceBuilder
{
    private readonly Dictionary<JobType, JobSpecification> _jobSpecifications = [];

    private string? _aristoTelosWebUrl = null;

    public IJobServiceBuilder AddJobSpecification(JobSpecification jobSpecification)
    {
        _jobSpecifications.Add(jobSpecification.JobType, jobSpecification);
        return this;
    }

    public IJobServiceBuilder SetAristoTelosWebUrl(string? aristoTelosWebUrl)
    {
        _aristoTelosWebUrl = aristoTelosWebUrl;

        return this;
    }

    public IJobService Create()
    {
        return new JobService(
            _jobServiceLogger,
            _scopedJob<PERSON>eriesScheduler,
            _fullOrgId,
            _jsonParser,
            _aristoTelosWebUrl,
            _jobSpecifications.ToImmutableDictionary()
        );
    }
}
