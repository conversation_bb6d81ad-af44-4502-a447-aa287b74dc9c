﻿namespace AT.Shared.Formatting;

using System.Reflection;
using AT.DataStructures.Time;
using AT.Translations;
using AT.Translations.Formatting;
using AT.Translations.Formatting.Numbers;
using AT.Translations.Formatting.Time;

public class TranslationParametersFormatter(Formatters _formatters) : ITranslationParametersFormatter
{
    public Dictionary<string, object?> Format(
        string messageTemplate,
        ITranslationWithParameters translation,
        Language? language = null
    )
    {
        Dictionary<string, object?> parameters = [];

        foreach (var property in translation.GetType().GetProperties())
        {
            object? parameterValue = property.GetValue(translation);
            string formattedParameter = Format(property, parameterValue, language);

            parameters.Add(property.Name, formattedParameter);

            if (messageTemplate.Contains("{" + property.Name + "Raw:"))
            {
                parameters.Add($"{property.Name}Raw", parameterValue);
            }
        }

        return parameters;
    }

    private string Format(PropertyInfo translationParameter, object? value, Language? language)
    {
        if (value is null)
        {
            return "";
        }

        if (translationParameter.PropertyType == typeof(int))
        {
            return FormatInteger((int)value, language);
        }

        if (translationParameter.PropertyType == typeof(double))
        {
            return FormatDouble(translationParameter, (double)value, language);
        }

        if (translationParameter.PropertyType == typeof(DateInterval))
        {
            return FormatDateInterval(translationParameter, (DateInterval)value, language);
        }

        if (translationParameter.PropertyType == typeof(DateTimeInterval))
        {
            return FormatDateTimeInterval(translationParameter, (DateTimeInterval)value, language);
        }

        if (translationParameter.PropertyType == typeof(DateOnly))
        {
            return FormatDate(translationParameter, (DateOnly)value, language);
        }

        if (translationParameter.PropertyType == typeof(DateTime))
        {
            return FormatDateTime(translationParameter, (DateTime)value, language);
        }

        if (translationParameter.PropertyType == typeof(TimeOnly))
        {
            return FormatTime(translationParameter, (TimeOnly)value, language);
        }

        if (translationParameter.PropertyType == typeof(Validity))
        {
            return FormatValidity((Validity)value, language);
        }

        if (translationParameter.PropertyType == typeof(TimeValidity))
        {
            return FormatTimeValidity((TimeValidity)value, language);
        }

        if (translationParameter.PropertyType == typeof(TimeSpan))
        {
            return FormatTimeSpan(translationParameter, (TimeSpan)value);
        }

        return $"{value}";
    }

    private string FormatInteger(int value, Language? language = null)
    {
        return _formatters.NumberFormatters.IntegerFormatter.Format(value, language);
    }

    private string FormatDouble(PropertyInfo translationParameter, double value, Language? language)
    {
        var attribute = translationParameter.GetCustomAttribute<DoubleFormattingAttribute>();
        var decimalPlaces = attribute?.MaxDecimalPlaces ?? MaxDecimalPlaces.Two;

        return _formatters.NumberFormatters.DoubleFormatter.Format(value, decimalPlaces, language);
    }

    private string FormatDate(PropertyInfo translationParameter, DateOnly value, Language? language)
    {
        var attribute = translationParameter.GetCustomAttribute<DateFormattingAttribute>();
        var type = attribute?.Type ?? DateFormattingType.Standard;

        return _formatters.TimeFormatters.DateFormatter.Format(value, type, language);
    }

    private string FormatDateTime(PropertyInfo translationParameter, DateTime value, Language? language)
    {
        var attribute = translationParameter.GetCustomAttribute<DateTimeFormattingAttribute>();
        var type = attribute?.Type ?? DateTimeFormattingType.Standard;

        return _formatters.TimeFormatters.DateTimeFormatter.Format(value, type, language);
    }

    private string FormatTime(PropertyInfo translationParameter, TimeOnly value, Language? language)
    {
        var attribute = translationParameter.GetCustomAttribute<TimeFormattingAttribute>();
        var type = attribute?.Type ?? TimeFormattingType.Standard;

        return _formatters.TimeFormatters.TimeFormatter.Format(value, type, language);
    }

    private string FormatDateInterval(PropertyInfo translationParameter, DateInterval value, Language? language)
    {
        var attribute = translationParameter.GetCustomAttribute<DateIntervalFormattingAttribute>();
        var type = attribute?.Type ?? DateIntervalFormattingType.Standard;

        return _formatters.TimeFormatters.DateIntervalFormatter.Format(value, type, language);
    }

    private string FormatDateTimeInterval(PropertyInfo translationParameter, DateTimeInterval value, Language? language)
    {
        var attribute = translationParameter.GetCustomAttribute<DateTimeIntervalFormattingAttribute>();
        var type = attribute?.Type ?? DateTimeIntervalFormattingType.Standard;

        return _formatters.TimeFormatters.DateTimeIntervalFormatter.Format(value, type, language);
    }

    private string FormatValidity(Validity value, Language? language = null)
    {
        return _formatters.TimeFormatters.ValidityFormatter.Format(value, language);
    }

    private string FormatTimeValidity(TimeValidity value, Language? language = null)
    {
        return _formatters.TimeFormatters.TimeValidityFormatter.Format(value, language);
    }

    private string FormatTimeSpan(PropertyInfo translationParameter, TimeSpan value)
    {
        var attribute = translationParameter.GetCustomAttribute<TimeSpanFormattingAttribute>();
        var type = attribute?.Type ?? TimeSpanFormattingType.Standard;

        return _formatters.TimeFormatters.TimeSpanFormatter.Format(value, type);
    }
}
