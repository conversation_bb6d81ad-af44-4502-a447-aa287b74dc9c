namespace AT.Utilities.Validation;

using System.ComponentModel.DataAnnotations;

public static class ValidationContextExtensions
{
    /// <summary>
    /// Creates a ValidationResult with a specified error message and optionally includes the member name from the ValidationContext.
    /// </summary>
    /// <param name="validationContext">The validation context containing the member name.</param>
    /// <param name="errorMessage">The error message to be included in the ValidationResult.</param>
    /// <returns>A ValidationResult with the specified error message and member name.</returns>
    public static ValidationResult CreateValidationResult(this ValidationContext validationContext, string errorMessage)
    {
        return new ValidationResult(
            errorMessage,
            validationContext.MemberName is null ? null : new[] { validationContext.MemberName! }
        );
    }
}
