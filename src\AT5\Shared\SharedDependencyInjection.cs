﻿namespace AT.Shared;

using AT.Shared.Formatting;
using AT.Shared.Formatting.Number;
using AT.Shared.Formatting.Number.Formatters;
using AT.Shared.Formatting.Time;
using AT.Shared.Formatting.Time.Formatters;
using AT.Shared.Resources;
using AT.Translations;
using AT.Translations.Formatting;
using AT.Utilities.Collections;
using AT.Utilities.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

public static class SharedDependencyInjection
{
    // FUTURE: This is not a great place for this method, move it elsewhere.
    public static IServiceCollection AddTranslator(
        this IServiceCollection serviceCollection,
        IEnumerable<ResourceManagerWrapper> resourceManagerWrappers
    )
    {
        // Make sure we allow injecting Lazy<T> services because Trans<PERSON> needs to inject
        // Lazy<ITranslationParametersFormatter> due to circular dependency.
        serviceCollection.AddLazyServiceAsSingleton();

        serviceCollection.AddSingleton<ITranslationParametersFormatter, TranslationParametersFormatter>();
        serviceCollection.AddSingleton<ITranslator, Translator>();

        // We always want to include resources from this project because it contains translated format strings.
        SharedResourceManagers.ResourceManagers.ForEach(x => serviceCollection.AddSingleton(x));
        resourceManagerWrappers.ForEach(x => serviceCollection.AddSingleton(x));

        serviceCollection.AddSingleton<DoubleFormatter>();
        serviceCollection.AddSingleton<IntegerFormatter>();

        serviceCollection.AddSingleton<DateFormatter>();
        serviceCollection.AddSingleton<TimeFormatter>();
        serviceCollection.AddSingleton<DateTimeFormatter>();
        serviceCollection.AddSingleton<DateIntervalFormatter>();
        serviceCollection.AddSingleton<DateTimeIntervalFormatter>();
        serviceCollection.AddSingleton<ValidityFormatter>();
        serviceCollection.AddSingleton<TimeValidityFormatter>();
        serviceCollection.AddSingleton<TimeSpanFormatter>();

        serviceCollection.AddSingleton<NumberFormatters>();
        serviceCollection.AddSingleton<TimeFormatters>();
        serviceCollection.AddSingleton<Formatters>();

        return serviceCollection;
    }
}
