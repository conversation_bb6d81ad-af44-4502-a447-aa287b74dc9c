﻿namespace AT.Infrastructure.DataAccess;

using System.Collections.Generic;
using AT.Core.DataAccess;
using AT.Core.MasterDomain.Entities;
using AT.Infrastructure.Database;

public class MasterDatabaseRepository(MasterDbContext _masterDbContext) : IMasterDatabaseRepository
{
    public Organization? GetOrganization(string organizationDatabaseName)
    {
        return _masterDbContext.Organizations.FirstOrDefault(o => o.DatabaseName == organizationDatabaseName);
    }

    public List<Organization> GetOrganizations()
    {
        return _masterDbContext.Organizations.ToList();
    }
}
