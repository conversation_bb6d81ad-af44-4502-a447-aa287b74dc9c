﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeeQueueProperty
{
    public EmployeeQueuePropertyId Id { get; set; }

    public QueuePropertyType Type { get; set; }

    public Validity Validity { get; set; }

    public string Value { get; set; } = null!;

    public QueueId QueueId { get; set; }

    public UserId EmployeeId { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Queue Queue { get; set; } = null!;
}
