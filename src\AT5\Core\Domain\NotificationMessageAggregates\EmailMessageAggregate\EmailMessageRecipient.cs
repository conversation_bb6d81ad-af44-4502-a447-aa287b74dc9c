﻿namespace AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;

using AT.Core.Domain.NotificationMessageAggregates;
using AT.Core.Domain.UserAggregate;
using Primitives.Enums;

public class EmailMessageRecipient : IEntity, INotificationMessageRecipient
{
    public long Id { get; set; }

    public long EmailMessagesId { get; set; }

    public string Address { get; set; } = null!;

    public UserId? UserId { get; set; }

    public RecipientType RecipientType { get; set; }

    public virtual EmailMessage EmailMessage { get; set; } = null!;

    public long MessageId => EmailMessagesId;

    public INotificationMessage Message => EmailMessage;

    public EmailMessageRecipient Clone()
    {
        return new EmailMessageRecipient()
        {
            Id = Id,
            Address = Address,
            UserId = UserId,
            RecipientType = RecipientType,
            EmailMessage = EmailMessage,
            EmailMessagesId = EmailMessagesId,
        };
    }
}
