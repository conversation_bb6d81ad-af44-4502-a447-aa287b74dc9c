﻿namespace AT.Core.Jobs.JobService.EmailsService;

using System;
using System.Collections.Generic;
using System.IO;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Primitives.Enums;
using AT.Utilities.Logging;

// FUTURE: IEmailMessageRepository shall not be referenced directly here.
// FUTURE: Requires feature 'EmailMessageSender'.
public class JobEmailsService(
    ILogger<JobEmailsService> _logger,
    IEmailMessageRepository _emailMessageRepository,
    string _jobName,
    string[] _successEmails,
    string[] _errorEmails
) : IJobEmailsService
{
    public Task SendSuccessEmails(
        string? message = null,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    )
    {
        if (_successEmails.Length == 0)
        {
            return Task.CompletedTask;
        }

        _logger.Info(
            "Sending success emails with message '{Message}' and {AttachmentCount} attachment.",
            message,
            attachments?.Count() ?? 0
        );

        var now = DateTime.Now;
        var subject = $"AristoTelos {_jobName} Scheduled Job Success {now.Date:dd.MM.yyyy}";
        var body = $"AristoTelos {_jobName} scheduled job finished successfully at {now:dd.MM.yyyy HH:mm:ss}.";
        if (message is not null)
        {
            body += $"{Environment.NewLine}{Environment.NewLine}{message}";
        }

        return SendSuccessEmails(subject, body, attachments);
    }

    public Task SendSuccessEmails(
        string subject,
        string body,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    )
    {
        if (_successEmails.Length == 0)
        {
            return Task.CompletedTask;
        }

        _logger.Info(
            "Sending success emails with subject '{Subject}', body '{Body}' and {AttachmentCount} attachment.",
            subject,
            body,
            attachments?.Count() ?? 0
        );

        return SendEmails(_successEmails, subject, body, attachments);
    }

    public Task SendErrorEmails(
        string? message = null,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    )
    {
        if (_errorEmails.Length == 0)
        {
            return Task.CompletedTask;
        }

        _logger.Info(
            "Sending error emails with message '{Message}' and {AttachmentCount} attachment.",
            message,
            attachments?.Count() ?? 0
        );

        var now = DateTime.Now;
        var subject = $"AristoTelos {_jobName} Scheduled Job Error {now.Date:dd.MM.yyyy}";
        var body = $"AristoTelos {_jobName} scheduled job failed at {now:dd.MM.yyyy HH:mm:ss}.";
        if (message is not null)
        {
            body += $"{Environment.NewLine}{Environment.NewLine}{message}";
        }

        return SendErrorEmails(subject, body, attachments);
    }

    public Task SendErrorEmails(
        string subject,
        string body,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    )
    {
        if (_errorEmails.Length == 0)
        {
            return Task.CompletedTask;
        }

        _logger.Info(
            "Sending error emails with subject '{Subject}', body '{Body}' and {AttachmentCount} attachment.",
            subject,
            body,
            attachments?.Count() ?? 0
        );

        return SendEmails(_errorEmails, subject, body, attachments);
    }

    public Task SendErrorEmails(Exception e)
    {
        if (_errorEmails.Length == 0)
        {
            return Task.CompletedTask;
        }

        _logger.Info("Sending error emails with an exception.");

        return SendErrorEmails(e.Message);
    }

    private Task SendEmails(
        string[] emails,
        string subject,
        string body,
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    )
    {
        var emailRecipients = CreateRecipients(emails);
        var emailAttachments = CreateAttachments(attachments);
        var emailMessage = CreateEmailMessage(subject, body, emailRecipients, emailAttachments);

        _emailMessageRepository.AddForInsert(emailMessage);
        return _emailMessageRepository.CommitAsync();
    }

    private static EmailMessage CreateEmailMessage(
        string subject,
        string body,
        ICollection<EmailMessageRecipient> recipients,
        ICollection<EmailMessageAttachment> attachments
    )
    {
        return new EmailMessage()
        {
            EmailType = EmailMessageType.General,
            Subject = subject,
            Body = body,
            BodyIsHtml = false,
            EmailMessageRecipients = recipients,
            SendTime = null,
            EntityId = null,
            EventParameters = null,
            Generated = DateTime.Now,
            TrackingId = Guid.NewGuid(),
            EmailMessageAttachments = attachments,
        };
    }

    private static EmailMessageRecipient[] CreateRecipients(string[] emails)
    {
        return emails.Select(e => CreateRecipient(e)).ToArray();
    }

    private static EmailMessageRecipient CreateRecipient(string email)
    {
        return new EmailMessageRecipient() { Address = email, RecipientType = RecipientType.To, };
    }

    private static EmailMessageAttachment[] CreateAttachments(
        IEnumerable<(string AttachmentFileName, Stream Attachment)>? attachments = null
    )
    {
        return attachments?.Select(x => CreateAttachment(x.Attachment, x.AttachmentFileName)).ToArray() ?? [];
    }

    private static EmailMessageAttachment CreateAttachment(Stream contentStream, string attachmentName)
    {
        using var memoryStream = new MemoryStream();
        contentStream.CopyTo(memoryStream);
        var content = memoryStream.ToArray();

        return new EmailMessageAttachment() { FileName = attachmentName, Content = content, };
    }
}
