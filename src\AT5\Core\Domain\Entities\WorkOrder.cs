﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;

public class WorkOrder
{
    public WorkOrderId Id { get; set; }

    public string Name { get; set; } = null!;

    public int OrderGroupId { get; set; }

    public DateTime Start { get; set; }

    public DateTime Deadline { get; set; }

    public DateTime? FirstProcessed { get; set; }

    public DateTime? LastProcessed { get; set; }

    public double Volume { get; set; }

    public double ProcessedVolume { get; set; }

    public DoubleValueChange ProductivityChange { get; set; } = null!;

    public string? Color { get; set; }

    public QueueId QueueId { get; set; }

    public SiteId SiteId { get; set; }

    public virtual Queue Queue { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;

    public virtual ICollection<WorkOrderItem> WorkOrderItems { get; set; } = new List<WorkOrderItem>();
}
