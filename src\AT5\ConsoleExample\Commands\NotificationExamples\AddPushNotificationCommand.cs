namespace AT.ConsoleExample.Commands.NotificationExamples;

using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Primitives.Enums;
using AT.PrimitivesAT5.Ids;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

[Command("pushnotification add", Description = "Adds a new push notification")]
public class AddPushNotificationCommand(IPushNotificationRepository _pushNotificationsRepository) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        PushNotificationRecipient recipient = new() { UserId = UserId.From(1), };

        PushNotification pn =
            new()
            {
                Title = "Test subject PN",
                Body = "Test body PN",
                PushNotificationRecipients = [recipient],
                Generated = DateTime.Now,
                Type = PushNotificationType.BreakStart,
            };

        _pushNotificationsRepository.AddForInsert(pn);

        await _pushNotificationsRepository.CommitAsync();
    }
}
