﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ApprovalsAggregate;
using AT.Core.Domain.UserAggregate;

public class RemovedNotification
{
    public RemovedNotificationId Id { get; set; }

    public UserId UserId { get; set; }

    public ChangeRequestId? ChangeRequestId { get; set; }

    public TradeOfferId? TradeOfferId { get; set; }

    public ReservationId? ReservationId { get; set; }

    public DateTime Created { get; set; }

    public TaskTodoId? TaskId { get; set; }

    public RequestId? RequestId { get; set; }

    public EmployeePropertyId? EmployeePropertyId { get; set; }

    public RoleDelegationId? RoleDelegationId { get; set; }

    public DateOnly? ShiftConfirmationDate { get; set; }

    public CalculationId? CalculationId { get; set; }

    public RequestPropertyId? RequestPropertyId { get; set; }

    public ApprovalHistoryId? ApprovalHistoryId { get; set; }

    public virtual Calculation? Calculation { get; set; }

    public virtual ChangeRequest? ChangeRequest { get; set; }

    public virtual EmployeeProperty? EmployeeProperty { get; set; }

    public virtual Request? Request { get; set; }

    public virtual RequestProperty? RequestProperty { get; set; }

    public virtual Reservation? Reservation { get; set; }

    public virtual RoleDelegation? RoleDelegation { get; set; }

    public virtual TaskTodo? Task { get; set; }

    public virtual TradeOffer? TradeOffer { get; set; }

    public virtual ApprovalHistory? ApprovalHistory { get; set; }

    public virtual User User { get; set; } = null!;
}
