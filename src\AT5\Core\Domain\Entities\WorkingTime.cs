﻿namespace AT.Core.Domain.Entities;

using Base;

public class WorkingTime
{
    public WorkingTimeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public double WeekWorkingHours { get; set; }

    public double? MaxWeekWorkingHours { get; set; }

    public double WeekOverTimeLimitHours { get; set; }

    public int WeekWorkDays { get; set; }

    public bool SkipWorkingHolidays { get; set; }

    public bool SkipNotWorkingHolidays { get; set; }

    public double PlanningTolleranceMax { get; set; }

    public double PlanningTolleranceMin { get; set; }

    public string? Parameters { get; set; }

    public bool IsGlobal { get; set; }

    public ShiftTemplateId ShiftTemplateId { get; set; }

    public virtual ShiftTemplate ShiftTemplate { get; set; } = null!;

    public virtual ICollection<WorkingTimeModel> WorkingTimeModels { get; set; } = new List<WorkingTimeModel>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
