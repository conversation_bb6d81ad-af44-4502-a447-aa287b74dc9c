﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.Entities;
using AT.Core.Domain.UserAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="User"/> in the database.
/// </summary>
internal static class UserGroupModelBuilder
{
    public static void BuildUserGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Agency>(entity =>
        {
            entity.ToTable("Agencies");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);
        });

        modelBuilder.Entity<AuthenticationToken>(entity =>
        {
            entity.ToTable("AuthenticationTokens");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.UserId, "IX_FK_UserAuthenticationToken");

            entity.Property(e => e.TokenData).HasMaxLength(256);
            entity.Property(e => e.ValidityEnd).IsStoredAsDateTime();
            entity.Property(e => e.ValidityStart).IsStoredAsDateTime();

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.AuthenticationTokens)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserAuthenticationToken");
        });

        modelBuilder.Entity<EmployeeActivity>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeActivities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Min).HasColumnOrder(iota);
            entity.Property(e => e.Max).HasColumnOrder(iota);

            entity.Property(e => e.Priority).HasColumnOrder(iota);

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeEmployeeActivity");

            entity.Property(e => e.ActivityId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ActivityId, "IX_FK_ActivityEmployeeActivity");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeeActivitySite");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Activity)
                .WithMany(p => p.EmployeeActivities)
                .HasForeignKey(d => d.ActivityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActivityEmployeeActivity");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeActivities)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeeActivity");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeActivities)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeActivitySite");
        });

        modelBuilder.Entity<EmployeeBalance>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeBalances");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasColumnOrder(iota).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeeBalanceSite");
            entity.Property(e => e.SiteId).HasColumnOrder(iota);

            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeBalanceEmployee");
            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);

            entity.HasDateInterval(e => e.DateInterval, iota);

            entity.Property(e => e.FixedIntervalStart).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.AuthorId).HasColumnOrder(iota);
            entity.Property(e => e.TimeStamp).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Balance).HasColumnOrder(iota);
            entity.Property(e => e.Refund).HasColumnOrder(iota);
            entity.Property(e => e.AdditionalTransferredBalanceCompensation).HasColumnOrder(iota);
            entity.Property(e => e.FinalState).HasMaxLength(4000).HasColumnOrder(iota);
            entity.Property(e => e.BalanceType).HasColumnOrder(iota);
            entity.Property(e => e.PaidCompensatoryOvertime).HasColumnOrder(iota);
            entity.Property(e => e.InitialPaidOvertime).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeBalances)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeBalanceEmployee");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeBalances)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeBalanceSite");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.SiteId,
                        e.DateInterval.Start,
                        e.DateInterval.End
                    },
                    "IX_NC_EmployeeBalances_Fetch1"
                )
                .IncludeProperties(e => e.EmployeeId);
        });

        modelBuilder.Entity<EmployeeContract>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeContracts");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.ContractId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ContractId, "IX_FK_ContractEmployeeContract");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.EmployeeId,
                        e.Validity.Interval.Start,
                        e.Validity.Interval.End
                    },
                    "IX_FK_EmployeeEmployeeContract"
                )
                .WithSettings(new { FILLFACTOR = 80 });

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeeContractSite");

            entity.HasValidity(e => e.Validity, iota);

            entity.HasValidity(e => e.ShiftPlanning, iota);

            entity.Property(e => e.Code).HasMaxLength(50);

            entity
                .HasOne(d => d.Contract)
                .WithMany(p => p.EmployeeContracts)
                .HasForeignKey(d => d.ContractId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ContractEmployeeContract");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeContracts)
                .HasForeignKey(d => d.EmployeeId)
                .HasConstraintName("FK_EmployeeEmployeeContract");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeContracts)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeContractSite");

            entity
                .HasMany(d => d.AllowedSites)
                .WithMany(p => p.EmployeeContractAllowedSiteSites)
                .UsingEntity<Dictionary<string, object>>(
                    "EmployeeContractAllowedSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("AllowedSitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_EmployeeContractAllowedSite_Site"),
                    l =>
                        l.HasOne<EmployeeContract>()
                            .WithMany()
                            .HasForeignKey("EmployeeContractAllowedSiteSiteId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_EmployeeContractAllowedSite_EmployeeContract"),
                    j =>
                    {
                        j.HasKey("EmployeeContractAllowedSiteSiteId", "AllowedSitesId");
                        j.ToTable("EmployeeContractAllowedSite");
                        j.HasIndex(["AllowedSitesId"], "IX_FK_EmployeeContractAllowedSite_Site");
                        j.IndexerProperty<EmployeeContractId>("EmployeeContractAllowedSiteSiteId")
                            .HasColumnName("EmployeeContractAllowedSite_Site_Id");
                        j.IndexerProperty<SiteId>("AllowedSitesId").HasColumnName("AllowedSites_Id");
                    }
                );
        });

        modelBuilder.Entity<EmployeeDefaultWorkingTimeModel>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeDefaultWorkingTimeModels");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.ShiftSystemStart).HasColumnOrder(iota).IsStoredAsDateTime();

            entity.Property(e => e.EmployeeContractId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeContractId, "IX_FK_EmployeeContractEmployeeDefaultWorkingTimeModel");

            entity.Property(e => e.WorkingTimeModelId).HasColumnOrder(iota);
            entity.HasIndex(e => e.WorkingTimeModelId, "IX_FK_WorkingTimeModelEmployeeDefaultWorkingTimeModel");

            entity
                .HasOne(d => d.EmployeeContract)
                .WithMany(p => p.EmployeeDefaultWorkingTimeModels)
                .HasForeignKey(d => d.EmployeeContractId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeContractEmployeeDefaultWorkingTimeModel");

            entity
                .HasOne(d => d.WorkingTimeModel)
                .WithMany(p => p.EmployeeDefaultWorkingTimeModels)
                .HasForeignKey(d => d.WorkingTimeModelId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkingTimeModelEmployeeDefaultWorkingTimeModel");
        });

        modelBuilder.Entity<EmployeeLocation>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeLocations");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Min).HasColumnOrder(iota);
            entity.Property(e => e.Max).HasColumnOrder(iota);
            entity.Property(e => e.Priority).HasColumnOrder(iota);

            entity.Property(e => e.LocationId).HasColumnOrder(iota);
            entity.HasIndex(e => e.LocationId, "IX_FK_LocationEmployeeLocation");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeEmployeeLocation");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeeLocationSite");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeLocations)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeeLocation");

            entity
                .HasOne(d => d.Location)
                .WithMany(p => p.EmployeeLocations)
                .HasForeignKey(d => d.LocationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_LocationEmployeeLocation");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeLocations)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeLocationSite");
        });

        modelBuilder.Entity<EmployeeOvertimeSuggestion>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeOvertimeSuggestions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeOvertimeSuggestionEmployee");

            entity.HasDateTimeInterval(e => e.Interval, iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeOvertimeSuggestions)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeOvertimeSuggestionEmployee");
        });

        modelBuilder.Entity<EmployeePosition>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeePositions");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeEmployeePositions");

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.PositionId).HasColumnOrder(iota);
            entity.HasIndex(e => e.PositionId, "IX_FK_PositionEmployeePosition");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeePositionSite");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeePositions)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeePositions");

            entity
                .HasOne(d => d.Position)
                .WithMany(p => p.EmployeePositions)
                .HasForeignKey(d => d.PositionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PositionEmployeePosition");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeePositions)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePositionSite");
        });

        modelBuilder.Entity<EmployeeProperty>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Value).HasColumnOrder(iota).HasMaxLength(4000);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.PropertyId).HasColumnOrder(iota);
            entity.HasIndex(e => e.PropertyId, "IX_FK_PropertyEmployeeProperty");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.EmployeeId,
                        e.PropertyId,
                        e.SiteId
                    },
                    "IX_FK_EmployeeEmployeeProperty"
                )
                .IncludeProperties(e => new
                {
                    e.Validity.Interval.Start,
                    e.Validity.Interval.End,
                    e.Validity.IsInvalid
                })
                .WithSettings(new { FILLFACTOR = 80 });

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeePropertySite");

            entity.Property(e => e.State).HasColumnOrder(iota);
            entity.Property(e => e.Locked).HasColumnOrder(iota);

            entity.Property(e => e.StateChanged).HasColumnOrder(iota).HasColumnType("datetime");
            entity.Property(e => e.StateChangedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.StateChangedById, "IX_FK_EmployeePropertyStateChangeUser");

            entity.Property(e => e.ValueChanged).HasColumnOrder(iota).HasColumnType("datetime");
            entity.Property(e => e.ValueChangedById).HasColumnOrder(iota);
            entity.HasIndex(e => e.ValueChangedById, "IX_FK_EmployeePropertyValueChangeUser");

            entity.Property(e => e.Source).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeProperties)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeeProperty");

            entity
                .HasOne(d => d.Property)
                .WithMany(p => p.EmployeeProperties)
                .HasForeignKey(d => d.PropertyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PropertyEmployeeProperty");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeProperties)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePropertySite");

            entity
                .HasOne(d => d.StateChangedBy)
                .WithMany(p => p.EmployeePropertyStateChangedBies)
                .HasForeignKey(d => d.StateChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePropertyStateChangeUser");

            entity
                .HasOne(d => d.ValueChangedBy)
                .WithMany(p => p.EmployeePropertyValueChangedBies)
                .HasForeignKey(d => d.ValueChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePropertyValueChangeUser");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.PropertyId,
                        e.SiteId,
                        e.State,
                        e.Validity.Interval.Start,
                        e.Validity.Interval.End,
                        e.Validity.IsInvalid
                    },
                    "IX_NC_EmployeeProperties_Fetch1"
                )
                .IncludeProperties(e => e.EmployeeId);
        });

        modelBuilder.Entity<EmployeePublicRoster>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeePublicRosters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeePublicRosterSite");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeePublicRosterEmployee");

            entity.HasDateInterval(e => e.Interval, iota);

            entity.Property(e => e.AuthorId).HasColumnOrder(iota);
            entity.HasIndex(e => e.AuthorId, "IX_FK_EmployeePublicRosterUser");

            entity.Property(e => e.Timestamp).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Type).HasColumnOrder(iota);
            entity.Property(e => e.EmptyPublish).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Author)
                .WithMany(p => p.EmployeePublicRosterAuthors)
                .HasForeignKey(d => d.AuthorId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePublicRosterUser");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeePublicRosters)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePublicRosterEmployee");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeePublicRosters)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePublicRosterSite");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.SiteId,
                        e.Interval.Start,
                        e.Interval.End
                    },
                    "IX_NC_EmployeePublicRosters_Fetch1"
                )
                .IncludeProperties(e => e.EmployeeId);
        });

        modelBuilder.Entity<EmployeeQueueProperty>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeQueueProperties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Type).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.Value).HasColumnOrder(iota).HasMaxLength(1024);

            entity.Property(e => e.QueueId).HasColumnOrder(iota);
            entity.HasIndex(e => e.QueueId, "IX_FK_QueueEmployeeQueueProperty");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeEmployeeQueueProperty");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeQueueProperties)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeeQueueProperty");

            entity
                .HasOne(d => d.Queue)
                .WithMany(p => p.EmployeeQueueProperties)
                .HasForeignKey(d => d.QueueId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_QueueEmployeeQueueProperty");
        });

        modelBuilder.Entity<EmployeeRequestType>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeRequestTypes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeEmployeeRequestType");

            entity.Property(e => e.RequestTypeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RequestTypeId, "IX_FK_RequestTypeEmployeeRequestType");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_SiteEmployeeRequestType");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeRequestTypes)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeeRequestType");

            entity
                .HasOne(d => d.RequestType)
                .WithMany(p => p.EmployeeRequestTypes)
                .HasForeignKey(d => d.RequestTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RequestTypeEmployeeRequestType");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeRequestTypes)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteEmployeeRequestType");
        });

        modelBuilder.Entity<EmployeeRosterFinish>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeRosterFinishes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeeRosterFinishSite");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeRosterFinishEmployee");

            entity.HasDateInterval(e => e.DateInterval, iota);

            entity.Property(e => e.State).HasColumnOrder(iota);
            entity.Property(e => e.FinishAuthorId).HasColumnOrder(iota);
            entity.Property(e => e.FinishTimeStamp).IsStoredAsDateTime().HasColumnOrder(iota);

            entity.Property(e => e.Locked).HasColumnOrder(iota);
            entity.Property(e => e.LockAuthorId).HasColumnOrder(iota);
            entity.Property(e => e.LockTimeStamp).IsStoredAsDateTime().HasColumnOrder(iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeRosterFinishes)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeRosterFinishEmployee");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeRosterFinishes)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeRosterFinishSite");

            entity
                .HasIndex(
                    context.AdditionalIndexCollection,
                    e => new
                    {
                        e.SiteId,
                        e.State,
                        e.DateInterval.Start,
                        e.DateInterval.End
                    },
                    "IX_NC_EmployeeRosterFinishes_Fetch1"
                )
                .IncludeProperties(e => e.EmployeeId);
        });

        modelBuilder.Entity<EmployeeShiftTemplate>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeShiftTemplates");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Min).HasColumnOrder(iota);
            entity.Property(e => e.Max).HasColumnOrder(iota);
            entity.Property(e => e.Priority).HasColumnOrder(iota);

            entity.Property(e => e.ShiftTemplateId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ShiftTemplateId, "IX_FK_ShiftTemplateEmployeeShiftTemplate");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeShiftTemplateEmployee");

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_EmployeeShiftTemplateSite");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeShiftTemplates)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeShiftTemplateEmployee");

            entity
                .HasOne(d => d.ShiftTemplate)
                .WithMany(p => p.EmployeeShiftTemplates)
                .HasForeignKey(d => d.ShiftTemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ShiftTemplateEmployeeShiftTemplate");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.EmployeeShiftTemplates)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeShiftTemplateSite");
        });

        modelBuilder.Entity<EmployeeSkill>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeSkills");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.SkillId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SkillId, "IX_FK_SkillEmployeeSkill");

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeEmployeeSkill");

            entity.Property(e => e.Level).HasColumnOrder(iota);

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.CommunicationType).HasColumnOrder(iota);
            entity.Property(e => e.Language).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeSkills)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeEmployeeSkill");

            entity
                .HasOne(d => d.Skill)
                .WithMany(p => p.EmployeeSkills)
                .HasForeignKey(d => d.SkillId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SkillEmployeeSkill");
        });

        modelBuilder.Entity<EmployeeTimePreference>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeTimePreferences");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Type).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasTimeInterval(e => e.TimeInterval, iota);

            entity.Property(e => e.Min).HasColumnOrder(iota);
            entity.Property(e => e.Max).HasColumnOrder(iota);
            entity.Property(e => e.Priority).HasColumnOrder(iota);

            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.EmployeeId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeePreference");

            entity.Property(e => e.Weeks).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeTimePreferences)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeePreference");
        });

        modelBuilder.Entity<EmployeeWorkingTimeModel>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("EmployeeWorkingTimeModels");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.ShiftSystemStart).HasColumnOrder(iota).IsStoredAsDateTime();

            entity.Property(e => e.EmployeeContractId).HasColumnOrder(iota);
            entity.HasIndex(e => e.EmployeeContractId, "IX_FK_EmployeeContractEmployeeWorkingTimeModel");

            entity.Property(e => e.WorkingTimeModelId).HasColumnOrder(iota);
            entity.HasIndex(e => e.WorkingTimeModelId, "IX_FK_WorkingTimeModelEmployeeWorkingTimeModel");

            entity.Property(e => e.DoNotSynchronize).HasColumnOrder(iota);

            entity
                .HasOne(d => d.EmployeeContract)
                .WithMany(p => p.EmployeeWorkingTimeModels)
                .HasForeignKey(d => d.EmployeeContractId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeContractEmployeeWorkingTimeModel");

            entity
                .HasOne(d => d.WorkingTimeModel)
                .WithMany(p => p.EmployeeWorkingTimeModels)
                .HasForeignKey(d => d.WorkingTimeModelId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_WorkingTimeModelEmployeeWorkingTimeModel");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("Users");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.Username, "IX_NC_Users_Username");

            entity.Property(e => e.Changed).IsStoredAsDateTime();
            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.Property(e => e.Email2).HasMaxLength(100);
            entity.Property(e => e.FailedPasswordStart).IsStoredAsDateTime();
            entity.Property(e => e.FirstName).HasMaxLength(50);
            entity.Property(e => e.LastLockout).IsStoredAsDateTime();
            entity.Property(e => e.LastLogin).IsStoredAsDateTime();
            entity.Property(e => e.LastName).HasMaxLength(50);
            entity.Property(e => e.LastPasswordChange).IsStoredAsDateTime();
            entity.Property(e => e.MiddleName).HasMaxLength(50);
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.Phone).HasMaxLength(20);
            entity.Property(e => e.Username).HasMaxLength(100);
        });

        modelBuilder.Entity<UserFilter>(entity =>
        {
            entity.ToTable("UserFilters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.UserId, "IX_FK_UserFilterUser");

            entity.Property(e => e.Changed).IsStoredAsDateTime();
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Value).HasMaxLength(4000);

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.UserFilters)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserFilterUser");
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("UserRoles");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.RoleId).HasColumnOrder(iota);
            entity.HasIndex(e => e.RoleId, "IX_FK_UserRolesRole");

            entity.Property(e => e.UserId).HasColumnOrder(iota);
            entity.HasIndex(e => e.UserId, "IX_FK_UserRolesUser");

            entity.Property(e => e.AssignmentType).HasColumnOrder(iota);

            entity.HasTimeValidity(e => e.Validity, iota);

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_UserRoleSite");

            entity.Property(e => e.TeamId).HasColumnOrder(iota);
            entity.HasIndex(e => e.TeamId, "IX_FK_UserRoleTeam");

            entity.Property(e => e.SubordinateUserId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SubordinateUserId, "IX_FK_UserUserRole");

            entity.Property(e => e.Synchronized).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Role)
                .WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserRolesRole");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.SiteId)
                .HasConstraintName("FK_UserRoleSite");

            entity
                .HasOne(d => d.SubordinateUser)
                .WithMany(p => p.UserRoleSubordinateUsers)
                .HasForeignKey(d => d.SubordinateUserId)
                .HasConstraintName("FK_UserUserRole");

            entity
                .HasOne(d => d.Team)
                .WithMany(p => p.UserRoles)
                .HasForeignKey(d => d.TeamId)
                .HasConstraintName("FK_UserRoleTeam");

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.UserRoleUsers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserRolesUser");
        });

        modelBuilder.Entity<Employee>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("Users_Employee");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.CcId).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.PersonalId).HasMaxLength(50).HasColumnOrder(iota);
            entity.Property(e => e.SystemId).HasColumnOrder(iota);
            entity.Property(e => e.EntryDate).HasColumnOrder(iota).IsStoredAsDateTime();
            entity.Property(e => e.FinishDate).HasColumnOrder(iota).IsStoredAsDateTime();
            entity.Property(e => e.StartPlanningDate).HasColumnOrder(iota).IsStoredAsDateTime();
            entity.Property(e => e.IsActive).HasColumnOrder(iota);
            entity.Property(e => e.Holiday).HasColumnOrder(iota);
            entity.Property(e => e.Seniority).HasColumnOrder(iota);
            entity.Property(e => e.Rank).HasColumnOrder(iota);
            entity.Property(e => e.AllowReservations).HasColumnOrder(iota);
            entity.Property(e => e.SendCallendarEvents).HasColumnOrder(iota);
            entity.Property(e => e.HolidayOver).HasColumnOrder(iota);
            entity.Property(e => e.UseAsTemplate).HasColumnOrder(iota);
            entity.Property(e => e.PositionId).HasColumnOrder(iota);
            entity.Property(e => e.TitleBefore).HasMaxLength(20).HasColumnOrder(iota);
            entity.Property(e => e.TitleAfter).HasMaxLength(20).HasColumnOrder(iota);
            entity.Property(e => e.Gender).HasColumnOrder(iota);
            entity.Property(e => e.DateOfBirth).HasColumnOrder(iota).IsStoredAsDateTime();

            entity.Property(e => e.AgencyId).HasColumnOrder(iota);
            entity.HasIndex(e => e.AgencyId, "IX_FK_EmployeeAgency");

            entity.Property(e => e.MultiEmployee).HasColumnOrder(iota);
            entity.HasIndex(e => e.MainEmployeeId, "IX_FK_EmployeeMainEmployee");

            entity.Property(e => e.Id).HasColumnOrder(iota);
            entity.Property(e => e.MainEmployeeId).HasColumnOrder(iota);
            entity.Property(e => e.RosterItemType).HasColumnOrder(iota);
            entity.Property(e => e.EmployeeRosterType).HasColumnOrder(iota);
            entity.Property(e => e.ExpectedContractEnd).HasColumnOrder(iota).IsStoredAsDateTime();
            entity.Property(e => e.OmitImplicitShifts).HasColumnOrder(iota);
            entity.Property(e => e.OmitImplicitActivities).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Agency)
                .WithMany(p => p.Employees)
                .HasForeignKey(d => d.AgencyId)
                .HasConstraintName("FK_EmployeeAgency");

            entity
                .HasOne(d => d.User)
                .WithOne()
                .HasForeignKey<Employee>(d => d.Id)
                .HasConstraintName("FK_Employee_inherits_User");

            entity
                .HasOne(d => d.MainEmployee)
                .WithMany(p => p.InverseMainEmployee)
                .HasForeignKey(d => d.MainEmployeeId)
                .HasConstraintName("FK_EmployeeMainEmployee");
        });
    }
}
