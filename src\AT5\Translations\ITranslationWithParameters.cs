﻿namespace AT.Translations;

/// <summary>
/// Marker interface for translations that contain input parameters.
/// The name of the specific record/class that implements this interface is the
/// translation's key in the resx file. The implementation may contain parameters with
/// attributes that specify their formatting in the resulting <see cref="TranslatedText" />.
/// </summary>
public interface ITranslationWithParameters : ITranslation
{ }