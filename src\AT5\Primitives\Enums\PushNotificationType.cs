﻿namespace AT.Primitives.Enums;

public enum PushNotificationType
{
    BreakStart = 0,
    BreakEnd = 1,
    CalculationEnd = 2,
    TaskCreation = 3,
    TaskEdit = 4,
    TaskCancellation = 5,
    TaskStatusChange = 6,
    RosterItemChange = 7,
    RosterItemChangeRejected = 8,
    RosterPublish = 9,
    RequestPropertyDocumentCreated = 10,
    RequestPropertyDocumentProcessed = 11,
    WorksheetConfirmation = 12,
    RoleDelegation = 13,
    Request = 14,
    TradeAccepted = 15,
    TradeAsk = 16,
    TradeCancel = 17,
    ChangeRequest = 18,
    OpenShift = 19,
}
