﻿namespace AT.NotificationsService.General.Interfaces;

/// <summary>
/// Responsible for processing messages of a certain type (e.g., Push Notifications).
/// That typically includes loading, sending, and deleting them from the message provider (e.g. the database).
/// </summary>
public interface IMessageProcessor
{
    /// <summary>
    /// Processes a single batch of messages form their corresponding queue.
    /// </summary>
    /// <returns>
    /// Number of messages processed. The task finishes when cancellation on <paramref name="cancellationToken"/> is requested.
    /// </returns>
    Task<int> ProcessMessages(int maxBatchSize, CancellationToken cancellationToken);
}
