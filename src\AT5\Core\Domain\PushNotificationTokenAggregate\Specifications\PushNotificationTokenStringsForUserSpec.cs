﻿namespace AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate.Specifications;

using Ardalis.Specification;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Core.Domain.UserAggregate;

public class PushNotificationTokenStringsForUserSpec : Specification<PushNotificationToken, string>
{
    public PushNotificationTokenStringsForUserSpec(UserId userId)
    {
        Query.Select(pnt => pnt.Token).Where(pnt => pnt.UserId == userId);
    }
}
