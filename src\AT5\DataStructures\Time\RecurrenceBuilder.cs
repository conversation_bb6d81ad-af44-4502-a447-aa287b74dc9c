﻿namespace AT.DataStructures.Time;

using System.Globalization;
using AT.Primitives.Enums;

/// <summary>
/// A builder for creating and configuring recurrence.
/// </summary>
public class RecurrenceBuilder
{
    /// <summary>
    /// Gets the start date and time of the recurrence pattern.
    /// </summary>
    public DateTime? Start { get; private set; }

    /// <summary>
    /// Gets the end date and time of the recurrence pattern.
    /// </summary>
    public DateTime? End { get; private set; }

    /// <summary>
    /// Gets the weekdays included in the recurrence pattern.
    /// </summary>
    public Weekdays Weekdays { get; private set; }

    /// <summary>
    /// Gets a value indicating whether working holidays should be excluded from the recurrence.
    /// </summary>
    public bool ExcludeWorkingHolidays { get; private set; }

    /// <summary>
    /// Gets a value indicating whether non-working holidays should be excluded from the recurrence.
    /// </summary>
    public bool ExcludeNotWorkingHolidays { get; private set; }

    /// <summary>
    /// Gets a value indicating whether working holidays should be included in the recurrence.
    /// </summary>
    public bool IncludeWorkingHolidays { get; private set; }

    /// <summary>
    /// Gets a value indicating whether non-working holidays should be included in the recurrence.
    /// </summary>
    public bool IncludeNotWorkingHolidays { get; private set; }

    /// <summary>
    /// Gets the list of exception dates as a comma-separated string.
    /// </summary>
    public string Exceptions { get; private set; }

    /// <summary>
    /// Gets the list of inclusion dates as a comma-separated string.
    /// </summary>
    public string Inclusions { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="RecurrenceBuilder"/> class with default values.
    /// </summary>
    public RecurrenceBuilder()
        : this(new Recurrence()) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="RecurrenceBuilder"/> class with values from an existing <see cref="Recurrence"/>.
    /// </summary>
    /// <param name="recurrence">The recurrence to initialize the builder with.</param>
    public RecurrenceBuilder(Recurrence recurrence)
    {
        Start = recurrence.Start;
        End = recurrence.End;
        Weekdays = recurrence.Weekdays;
        ExcludeWorkingHolidays = recurrence.ExcludeWorkingHolidays;
        ExcludeNotWorkingHolidays = recurrence.ExcludeNotWorkingHolidays;
        IncludeWorkingHolidays = recurrence.IncludeWorkingHolidays;
        IncludeNotWorkingHolidays = recurrence.IncludeNotWorkingHolidays;
        Exceptions = recurrence.Exceptions;
        Inclusions = recurrence.Inclusions;
    }

    /// <summary>
    /// Sets the start date and time for the recurrence pattern.
    /// </summary>
    /// <param name="start">The start date and time.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithStart(DateTime? start)
    {
        Start = start;
        return this;
    }

    /// <summary>
    /// Removes the start date and time, making it unspecified.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithUnspecifiedStart()
    {
        return WithStart(null);
    }

    /// <summary>
    /// Sets the end date and time for the recurrence pattern.
    /// </summary>
    /// <param name="end">The end date and time.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithEnd(DateTime? end)
    {
        End = end;
        return this;
    }

    /// <summary>
    /// Removes the end date and time, making it unspecified.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithUnspecifiedEnd()
    {
        return WithEnd(null);
    }

    /// <summary>
    /// Sets the weekdays included in the recurrence pattern.
    /// </summary>
    /// <param name="weekdays">The weekdays to include.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithWeekdays(Weekdays weekdays)
    {
        Weekdays = weekdays;
        return this;
    }

    /// <summary>
    /// Adds the specified weekdays to the recurrence pattern.
    /// </summary>
    /// <param name="weekdays">The weekdays to add.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddWeekdays(Weekdays weekdays)
    {
        Weekdays |= weekdays;
        return this;
    }

    /// <summary>
    /// Adds Monday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddMonday()
    {
        var monday = Weekdays.Monday;
        return AddWeekdays(monday);
    }

    /// <summary>
    /// Adds Tuesday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddTuesday()
    {
        var tuesday = Weekdays.Tuesday;
        return AddWeekdays(tuesday);
    }

    /// <summary>
    /// Adds Wednesday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddWednesday()
    {
        var wednesday = Weekdays.Wednesday;
        return AddWeekdays(wednesday);
    }

    /// <summary>
    /// Adds Thursday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddThursday()
    {
        var thursday = Weekdays.Thursday;
        return AddWeekdays(thursday);
    }

    /// <summary>
    /// Adds Friday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddFriday()
    {
        var friday = Weekdays.Friday;
        return AddWeekdays(friday);
    }

    /// <summary>
    /// Adds Saturday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddSaturday()
    {
        var saturday = Weekdays.Saturday;
        return AddWeekdays(saturday);
    }

    /// <summary>
    /// Adds Sunday to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddSunday()
    {
        var sunday = Weekdays.Sunday;
        return AddWeekdays(sunday);
    }

    /// <summary>
    /// Adds all workdays to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddWorkdays()
    {
        var workdays = Weekdays.Monday | Weekdays.Tuesday | Weekdays.Wednesday | Weekdays.Thursday | Weekdays.Friday;
        return AddWeekdays(workdays);
    }

    /// <summary>
    /// Adds weekend to the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddWeekend()
    {
        var weekend = Weekdays.Saturday | Weekdays.Sunday;
        return AddWeekdays(weekend);
    }

    /// <summary>
    /// Removes the specified weekdays from the recurrence pattern.
    /// </summary>
    /// <param name="weekdays">The weekdays to remove.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveWeekdays(Weekdays weekdays)
    {
        Weekdays &= ~weekdays;
        return this;
    }

    /// <summary>
    /// Removes Monday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveMonday()
    {
        var monday = Weekdays.Monday;
        return RemoveWeekdays(monday);
    }

    /// <summary>
    /// Removes Tuesday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveTuesday()
    {
        var tuesday = Weekdays.Tuesday;
        return RemoveWeekdays(tuesday);
    }

    /// <summary>
    /// Removes Wednesday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveWednesday()
    {
        var wednesday = Weekdays.Wednesday;
        return RemoveWeekdays(wednesday);
    }

    /// <summary>
    /// Removes Thursday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveThursday()
    {
        var thursday = Weekdays.Thursday;
        return RemoveWeekdays(thursday);
    }

    /// <summary>
    /// Removes Friday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveFriday()
    {
        var friday = Weekdays.Friday;
        return RemoveWeekdays(friday);
    }

    /// <summary>
    /// Removes Saturday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveSaturday()
    {
        var saturday = Weekdays.Saturday;
        return RemoveWeekdays(saturday);
    }

    /// <summary>
    /// Removes Sunday from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveSunday()
    {
        var sunday = Weekdays.Sunday;
        return RemoveWeekdays(sunday);
    }

    /// <summary>
    /// Removes all workdays from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveWorkdays()
    {
        var workdays = Weekdays.Monday | Weekdays.Tuesday | Weekdays.Wednesday | Weekdays.Thursday | Weekdays.Friday;
        return RemoveWeekdays(workdays);
    }

    /// <summary>
    /// Removes weekend from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveWeekend()
    {
        var weekend = Weekdays.Saturday | Weekdays.Sunday;
        return RemoveWeekdays(weekend);
    }

    /// <summary>
    /// Includes working holidays in the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddIncludeWorkingHolidays()
    {
        IncludeWorkingHolidays = true;
        ExcludeWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Removes include of working holidays from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveIncludeWorkingHolidays()
    {
        IncludeWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Includes non-working holidays in the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddIncludeNotWorkingHolidays()
    {
        IncludeNotWorkingHolidays = true;
        ExcludeNotWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Removes include of non-working holidays from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveIncludeNotWorkingHolidays()
    {
        IncludeNotWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Excludes working holidays in the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddExcludeWorkingHolidays()
    {
        ExcludeWorkingHolidays = true;
        IncludeWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Removes exclude of working holidays from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveExcludeWorkingHolidays()
    {
        ExcludeWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Excludes non-working holidays in the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder AddExcludeNotWorkingHolidays()
    {
        ExcludeNotWorkingHolidays = true;
        IncludeNotWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Removes exclude of non-working holidays from the recurrence pattern.
    /// </summary>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder RemoveExcludeNotWorkingHolidays()
    {
        ExcludeNotWorkingHolidays = false;
        return this;
    }

    /// <summary>
    /// Sets the exception dates to the recurrence pattern.
    /// </summary>
    /// <param name="exceptions">A collection of dates to exclude from the recurrence pattern.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithExceptions(IEnumerable<DateTime>? exceptions)
    {
        Exceptions = DateTimesToString(exceptions);
        return this;
    }

    /// <summary>
    /// Sets the inclusion dates to the recurrence pattern.
    /// </summary>
    /// <param name="inclusions">A collection of dates to include in the recurrence pattern.</param>
    /// <returns>The current instance of the <see cref="RecurrenceBuilder"/> for method chaining.</returns>
    public RecurrenceBuilder WithInclusions(IEnumerable<DateTime>? inclusions)
    {
        Inclusions = DateTimesToString(inclusions);
        return this;
    }

    /// <summary>
    /// Builds and returns the configured <see cref="Recurrence"/> object.
    /// </summary>
    /// <returns>A new <see cref="Recurrence"/> object based on the current configuration.</returns>
    public Recurrence Build()
    {
        return new Recurrence(
            Start,
            End,
            Weekdays,
            ExcludeWorkingHolidays,
            ExcludeNotWorkingHolidays,
            IncludeWorkingHolidays,
            IncludeNotWorkingHolidays,
            Exceptions,
            Inclusions
        );
    }

    /// <summary>
    /// Converts a single <see cref="DateTime"/> value to a string formatted as "yyyyMMdd".
    /// </summary>
    /// <param name="value">The <see cref="DateTime"/> value to convert.</param>
    /// <returns>A string representation of the <see cref="DateTime"/> in "yyyyMMdd" format.</returns>
    /// <remarks>
    /// This method ensures consistent formatting for dates used in the recurrence pattern,
    /// making them suitable for inclusion in serialized exception or inclusion strings.
    /// </remarks>
    private static string DateTimeToString(DateTime value)
    {
        return value.ToString("yyyyMMdd", CultureInfo.InvariantCulture);
    }

    /// <summary>
    /// Converts a collection of <see cref="DateTime"/> values to a single comma-separated string,
    /// with each date formatted as "yyyyMMdd".
    /// </summary>
    /// <param name="value">A collection of <see cref="DateTime"/> values to convert. Can be <c>null</c>.</param>
    /// <returns>
    /// A comma-separated string of formatted dates, or an empty string if the input is <c>null</c>.
    /// </returns>
    /// <remarks>
    /// This method is used to serialize lists of exception or inclusion dates into a format
    /// that is compatible with the recurrence pattern's string representation.
    /// </remarks>
    private static string DateTimesToString(IEnumerable<DateTime>? value)
    {
        if (value is null)
        {
            return string.Empty;
        }

        return string.Join(",", value.Select(DateTimeToString).ToArray());
    }
}
