﻿namespace AT.Translations.Formatting.Time;

[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public sealed class DateFormattingAttribute(DateFormattingType type) : Attribute
{
    public DateFormattingType Type { get; init; } = type;
}

// FUTURE: This shouldn't be in the Translations project.
/// <summary>
/// Locale-specific type of <see cref="DateOnly"/> formatting.
/// </summary>
public enum DateFormattingType
{
    /// <summary>
    /// E.g. "25. 7. 2025".
    /// </summary>
    Standard,

    /// <summary>
    /// E.g. "25.7.2025".
    /// </summary>
    Short,

    /// <summary>
    /// E.g. "07/25".
    /// </summary>
    MonthAndYear,
}
