﻿namespace AT.JobService;

using System.Collections.Generic;
using AristoTelosJobs.Jobs.Daktela;
using AristoTelosJobsService.Jobs;
using AristoTelosJobsService.Jobs.Albert;
using AristoTelosJobsService.Jobs.Astea;
using AristoTelosJobsService.Jobs.AxaKs;
using AristoTelosJobsService.Jobs.Calculations;
using AristoTelosJobsService.Jobs.CSOB;
using AristoTelosJobsService.Jobs.Daktela;
using AristoTelosJobsService.Jobs.Generic;
using AristoTelosJobsService.Jobs.Innogy;
using AristoTelosJobsService.Jobs.PayrollExport;
using AristoTelosJobsService.Jobs.TMobile;
using AristoTelosJobsService.Jobs.Uniqa;
using AristoTelosJobsService.Model;
using AT.Core.Jobs.JobService;
using AT.Core.Jobs.JobService.JobWrapper;
using AT.Primitives.Enums;
using AT.UseCases.Jobs;
using JobsService.Jobs.Albert;
using JobsService.Jobs.ClientDataImporter;
using JobsService.Jobs.ImportAgentStates;
using MSPS.AristoTelos.UI.Model.Jobs.Parameters;

public static class JobMapping
{
    public static readonly IReadOnlyCollection<JobSpecification> LegacyJobSpecifications =
    [
        //CreateLegacy<NullJob, NullJobParams>(JobType.NullJob),
        CreateLegacy<AlbertCalculateProductivityJob, AlbertCalculateProductivityJobParams>(
            JobType.AlbertCalculateProductivityJob
        ),
        // ImportAlbertEmployeesJob = 3 doesn't exist anymore
        CreateLegacy<ReportJob, ReportJobParams>(JobType.ReportJob),
        CreateLegacy<AgencyWorkersDeactivationJob, AgencyWorkersDeactivationJobParams>(
            JobType.AgencyWorkersDeactivationJob
        ),
        CreateLegacy<BreaksPlanningJob, BreaksPlanningJobParams>(JobType.BreaksPlanningJob),
        CreateLegacy<CsobGenesysImportVolumesJob, CsobGenesysImportVolumesJobParams>(
            JobType.CsobGenesysImportVolumesJob
        ),
        CreateLegacy<DaktelaImportVolumesRestJob, DaktelaImportJobParams>(JobType.DaktelaImportVolumesRestJob),
        CreateLegacy<DaktelaImportEmailsJob, DaktelaImportJobParams>(JobType.DaktelaImportEmailsJob),
        CreateLegacy<DaktelaImportActivitiesJob, DaktelaImportActivitiesJobParams>(JobType.DaktelaImportActivitiesJob),
        CreateLegacy<AxaKsImportEmployeeEntitlementsJob, AxaKsImportEmployeeEntitlementsJobParams>(
            JobType.AxaKsImportEmployeeEntitlementsJob
        ),
        CreateLegacy<PayrollExportJob, PayrollExportJobParams>(JobType.PayrollExportJob),
        CreateLegacy<CheckAgentStatesJob, CheckAgentStatesJobParams>(JobType.CheckAgentStatesJob),
        CreateLegacy<InnogyEmailsLoadVolumesJob, InnogyEmailsLoadVolumesJobParams>(JobType.InnogyEmailsLoadVolumesJob),
        CreateLegacy<DeactivateEmployeesJob, DeactivateEmployeesJobParams>(JobType.DeactivateEmployeesJob),
        CreateLegacy<InnogyImportMluviiChatsJob, InnogyImportMluviiChatsJobParams>(JobType.InnogyImportMluviiChatsJob),
        CreateLegacy<PlanningJob, PlanningJobParams>(JobType.PlanningJob),
        CreateLegacy<TMobileSynchronizationJob, TMobileSynchronizationJobParams>(JobType.TMobileSynchronizationJob),
        CreateLegacy<TMobileImportRosterItemsJob, TMobileImportRosterItemsJobParams>(
            JobType.TMobileImportRosterItemsJob
        ),
        CreateLegacy<ImportVolumesServiceNetRexJob, ImportVolumesServiceNetRexJobParams>(
            JobType.ImportVolumesServiceNetRexJob
        ),
        // EmployeeConfirmationsReportJob = 21 doesn't exist anymore.
        CreateLegacy<RosterItemPushNotificationsJob, RosterItemPushNotificationsJobParams>(
            JobType.RosterItemPushNotificationsJob
        ),
        CreateLegacy<UcsLoadVolumesJob, UcsLoadVolumesJobParams>(JobType.UcsLoadVolumesJob),
        CreateLegacy<StatesShiftUpdateJob, StatesShiftUpdateJobParams>(JobType.StatesShiftUpdateJob),
        CreateLegacy<DaktelaLoadAgentStatesJob, DaktelaLoadAgentStatesJobParams>(JobType.DaktelaLoadAgentStatesJob),
        CreateLegacy<ReportForManagerJob, ReportForManagerJobParams>(JobType.ReportForManagerJob),
        CreateLegacy<AutogenerateAndPublishShiftsJob, AutogenerateAndPublishShiftsJobParams>(
            JobType.AutogenerateAndPublishShiftsJob
        ),
        CreateLegacy<AsteaLoadAgentStatesJob, AsteaLoadAgentStatesJobParams>(JobType.AsteaLoadAgentStatesJob),
        CreateLegacy<AgentStatesConnectionCheckJob, AgentStatesConnectionCheckJobParams>(
            JobType.AgentStatesConnectionCheckJob
        ),
        CreateLegacy<SFApiExportJob, SFApiJobParams>(JobType.SFApiExportJob),
        CreateLegacy<ImportVolumesFromFileJob, ImportVolumesFromFileJobParams>(JobType.ImportVolumesFromFileJob),
        CreateLegacy<ImportAgentStatesFromFileJob, ImportAgentStatesFromFileJobParams>(
            JobType.ImportAgentStatesFromFileJob
        ),
        CreateLegacy<DownloadDataHubDataJob, DownloadDataHubDataJobParams>(JobType.DownloadDataHubDataJob),
        CreateLegacy<AlbertPositionsImportJob, AlbertPositionsImportJobParams>(JobType.AlbertPositionsImportJob),
        CreateLegacy<AlbertSitesImportJob, AlbertSitesImportJobParams>(JobType.AlbertSitesImportJob),
        CreateLegacy<AlbertPersonalDataImportJob, AlbertPersonalDataImportJobParams>(
            JobType.AlbertPersonalDataImportJob
        ),
        CreateLegacy<AlbertHourlyWagesImportJob, AlbertHourlyWagesImportJobParams>(JobType.AlbertHourlyWagesImportJob),
        CreateLegacy<AlbertAgencyEmployeesDeactivationJob, AlbertJobParams>(
            JobType.AlbertAgencyEmployeesDeactivationJob
        ),
        CreateLegacy<AlbertVolunteeringDayImportJob, AlbertVolunteeringDayImportJobParams>(
            JobType.AlbertVolunteeringDayImportJob
        ),
        CreateLegacy<PositionsDataImporterJob, ClientDataImporterJobParams>(JobType.PositionsDataImporterJob),
        CreateLegacy<OrganizationUnitsImporterJob, ClientDataImporterJobParams>(JobType.OrganizationUnitsImporterJob),
        CreateLegacy<PersonalDataImporterJob, ClientDataImporterJobParams>(JobType.PersonalDataImporterJob),
    ];

    public static readonly IReadOnlyCollection<JobSpecification> JobSpecifications =
    [
        Create<ModernNullJob, ModernNullJobParams>(JobType.NullJob),
    ];

    private static JobSpecification Create<TJob, TJobParams>(JobType jobType)
        where TJob : class, IATJob<TJobParams>
    {
        return JobSpecification.Create<TJob, TJobParams>(jobType);
    }

    private static JobSpecification CreateLegacy<TLegacyJob, TLegacyParams>(JobType jobType)
        where TLegacyJob : IAJob<TLegacyParams>
    {
        return JobSpecification.Create<LegacyATJobWrapper<TLegacyJob, TLegacyParams>, TLegacyParams>(jobType);
    }
}
