﻿namespace AT.Core.Domain.Entities;

public class RequestLimit
{
    public RequestLimitId Id { get; set; }

    public string Name { get; set; } = null!;

    public RosterRequestLimitPeriod Period { get; set; }

    public int? Limit { get; set; }

    public bool Disabled { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public int? AutoApproveLimit { get; set; }

    public OpenDateInterval TotalInterval { get; set; }

    public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();

    public virtual ICollection<RequestType> RequestTypes { get; set; } = new List<RequestType>();

    public virtual ICollection<ShiftTemplate> ShiftTemplates { get; set; } = new List<ShiftTemplate>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();

    public virtual ICollection<Team> Teams { get; set; } = new List<Team>();
}
