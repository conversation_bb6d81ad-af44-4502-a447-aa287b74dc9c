﻿namespace AT.Infrastructure.Utilities.DependencyInjection;

using Autofac;
using Autofac.Extensions.DependencyInjection;
using Autofac.Multitenant;
using Microsoft.Extensions.DependencyInjection;

// AutofacServiceProviderFactory doesn't support multitenancy and AutofacMultitenantServiceProviderFactory is specific for ASP.NET
// so we have a custom service provider factory.
public sealed class CustomMultitenantAutofacServiceProviderFactory(
    Func<IContainer, MultitenantContainer> _multitenantContainerAccessor,
    Action<ContainerBuilder> _configurationAction
) : IServiceProviderFactory<ContainerBuilder>
{
    /// <summary>
    /// Creates a container builder from an <see cref="IServiceCollection" />.
    /// </summary>
    public ContainerBuilder CreateBuilder(IServiceCollection services)
    {
        var builder = new ContainerBuilder();
        builder.Populate(services);
        _configurationAction(builder);

        return builder;
    }

    /// <summary>
    /// Creates an <see cref="IServiceProvider" /> from the container builder.
    /// </summary>
    public IServiceProvider CreateServiceProvider(ContainerBuilder containerBuilder)
    {
        MultitenantContainer multitenantContainer = null!;

        containerBuilder.Register(_ => multitenantContainer).AsSelf().ExternallyOwned();

        var rootContainer = containerBuilder.Build();
        multitenantContainer = _multitenantContainerAccessor(rootContainer);

        // NOTE: This is different from Autofac's AutofacMultitenantServiceProviderFactory
        // AutofacMultitenantServiceProviderFactory does resolving using MultitenantContainer
        // but we do default resolving using the root container, for resolution using MultitenantContainer
        // you have to first get IAppServiceProvider.
        return new AutofacServiceProvider(rootContainer);
    }
}
