﻿namespace AT.Primitives.Enums;

/// <summary>
/// When new value is added and this value must always correspond to specific property DataType value,
/// do not forget to set it at Property.cs -> GetDataType().
/// </summary>
public enum PropertyType
{
    None = 0,
    WorkingTimeBalance = 1,
    WorkingTimeBalancePeriod = 2,
    CostCenter = 3,
    DeskCount = 4,
    VacationBalanceSAP = 5,
    Performance = 6,
    ContractEvaluationPeriod = 7,
    PatternRotation = 8,
    PatternStart = 9,
    PlannedWorkingTimeBalance = 10,
    PlannedWorkingTime = 11,

    /// <summary>
    /// Remaining TimeOff entitlement for given year in days
    /// </summary>
    TimeOffBalanceDays = 12,

    /// <summary>
    /// Remaining TimeOff entitlement for given year in hours
    /// </summary>
    TimeOffBalanceHours = 13,

    /// <summary>
    /// Average shift length for given interval in hours
    /// </summary>
    AverageShiftLengthHours = 14,

    /// <summary>
    /// TimeOff entitlement used in given interval in hours
    /// </summary>
    TimeOffUsedHours = 15,

    /// <summary>
    /// TimeOff entitlement used in given interval in days
    /// </summary>
    TimeOffUsedDays = 16,

    /// <summary>
    /// TimeOff entitlement for a specific TimeOff.
    /// </summary>
    TimeOffEntitlement = 17,

    /// <summary>
    /// Additional TimeOff entitlement carried over from previous year/period/contract.
    /// </summary>
    TimeOffCarriedOver = 18,

    /// <summary>
    /// Initial value of used TimeOff for TimeOffUsedHours computation.
    /// </summary>
    [Obsolete]
    TimeOffInitialUsedHours = 19,

    /// <summary>
    /// For roster finishing (= WorkSheet confirmation) and roster blocking.
    /// Lock property on the property is used for Roster blockage.
    /// </summary>
    RosterFinish = 20, // REMOVED
    WorkTimeBalanceOverride = 21, // REMOVED
    WorkTimeBalanceComputed = 22, // REMOVED
    WorkTimeBalanceRefund = 23, // REMOVED
    WorkTimeBalanceFinalState = 24, // REMOVED
    MaxContract = 25,
    PropertiesJoin = 26,

    /// <summary>
    /// Remaining TimeOff current period (year) entitlement for given year in hours.
    /// </summary>
    TimeOffBalanceHoursCurrent = 27,

    /// <summary>
    /// Remaining TimeOff carried over entitlement for given year in hours.
    /// </summary>
    TimeOffBalanceHoursCarriedOver = 28,

    /// <summary>
    /// Remaining TimeOff current period (year) entitlement for given year in days.
    /// </summary>
    TimeOffBalanceDaysCurrent = 29,

    /// <summary>
    /// Remaining TimeOff carried over entitlement for given year in days.
    /// </summary>
    TimeOffBalanceDaysCarriedOver = 30,

    /// <summary>
    /// Used for shifts planning - people with the same value of this property are not going to be working in the same time.
    /// Example: parents of a young child cannot be both at work at the same time. One of them always has to be at home.
    /// </summary>
    OppositeShiftsGroup = 31,

    /// <summary>
    /// Used for shifts planning - people with the same value of this property have same/very similar shifts.
    /// Example: two people going to work with one car every day.
    /// </summary>
    SameShiftsGroup = 32,

    /// <summary>
    /// Limits the number of hours that a person can work inside a single week. Mainly for DPP/DPC purposes.
    /// </summary>
    MaxWeeklyContract = 33,

    /// <summary>
    /// Initial value to calculate yearly overtime from. In hours.
    /// </summary>
    InitialOvertime = 34,

    /// <summary>
    /// Used to confirm exceeding standard yearly overtime limit.
    /// Holds the confirmation timestamp. One property per employee per year.
    /// </summary>
    OvertimeAboveStandardConfirmation = 35,

    /// <summary>
    /// TimeOff entitlement used in given interval in number of shits
    /// </summary>
    TimeOffUsedShifts = 36,

    /// <summary>
    /// Remaining TimeOff entitlement for given year in number of shits
    /// </summary>
    TimeOffBalanceShifts = 37,

    /// <summary>
    /// Remaining TimeOff current period (year) entitlement for given year in number of shifts
    /// </summary>
    TimeOffBalanceShiftsCurrent = 38,

    /// <summary>
    /// Remaining TimeOff carried over entitlement for given year in number of shits
    /// </summary>
    TimeOffBalanceShiftsCarriedOver = 39,

    /// <summary>
    /// Initial paid overtime in hours that is used in RosterFinishFromProperties to initialize paid overtime for validation.
    /// </summary>
    InitialPaidOvertime = 40,

    WorkingTimeBalancePastCwt = 1001
}
