﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <WarningsAsErrors>Nullable</WarningsAsErrors>
        <Platforms>x64</Platforms>
        <RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Ardalis.Specification" />
      <PackageReference Include="FluentValidation" />
      <PackageReference Include="Vogen" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
        <ProjectReference Include="..\Primitives\Primitives.csproj" />
        <ProjectReference Include="..\DataStructures\DataStructures.csproj" />
        <ProjectReference Include="..\Utilities\Utilities.csproj" />
        <ProjectReference Include="..\Translations\Translations.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
    </ItemGroup>

</Project>
