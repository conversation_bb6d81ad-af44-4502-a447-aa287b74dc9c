﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Entities;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to notifications in the database.
/// </summary>
internal static class NotificationGroupModelBuilder
{
    public static void BuildNotificationGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<EmailMessage>(entity =>
        {
            entity.ToTable("EmailMessages");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.EventParameters).HasMaxLength(1024);
            entity.Property(e => e.Generated).IsStoredAsDateTime();
            entity.Property(e => e.SendTime).IsStoredAsDateTime();
            entity.Property(e => e.Subject).HasMaxLength(512);
        });

        modelBuilder.Entity<EmailMessageAttachment>(entity =>
        {
            entity.ToTable("EmailMessageAttachments");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.EmailMessageId, "IX_FK_EmailMessageEmailMessageAttachment");

            entity.Property(e => e.ContentType).HasMaxLength(64);
            entity.Property(e => e.FileName).HasMaxLength(128);

            entity
                .HasOne(d => d.EmailMessage)
                .WithMany(p => p.EmailMessageAttachments)
                .HasForeignKey(d => d.EmailMessageId)
                .HasConstraintName("FK_EmailMessageEmailMessageAttachment");
        });

        modelBuilder.Entity<EmailMessageRecipient>(entity =>
        {
            entity.ToTable("EmailMessageRecipients");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.EmailMessagesId, "IX_FK_EmailMessagesEmailMessageRecipient");

            entity.Property(e => e.Address).HasMaxLength(128);

            entity
                .HasOne(d => d.EmailMessage)
                .WithMany(p => p.EmailMessageRecipients)
                .HasForeignKey(d => d.EmailMessagesId)
                .HasConstraintName("FK_EmailMessagesEmailMessageRecipient");
        });

        modelBuilder.Entity<PushNotification>(entity =>
        {
            entity.ToTable("PushNotifications");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Body).HasMaxLength(4000);
            entity.Property(e => e.EventParameters).HasMaxLength(1024);
            entity.Property(e => e.Generated).IsStoredAsDateTime();
            entity.Property(e => e.ImageUrl).HasMaxLength(2000);
            entity.Property(e => e.SendTime).IsStoredAsDateTime();
            entity.Property(e => e.Title).HasMaxLength(512);
        });

        modelBuilder.Entity<PushNotificationRecipient>(entity =>
        {
            entity.ToTable("PushNotificationRecipients");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.PushNotificationId, "IX_FK_PushNotificationPushNotificationRecipient");

            entity
                .HasOne(d => d.PushNotification)
                .WithMany(p => p.PushNotificationRecipients)
                .HasForeignKey(d => d.PushNotificationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PushNotificationPushNotificationRecipient");
        });

        modelBuilder.Entity<PushNotificationToken>(entity =>
        {
            entity.ToTable("PushNotificationTokens");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.UserId, "IX_FK_PushNotificationTokensUser");

            entity.HasIndex(e => e.Token, "IX_NC_PushNotificationTokens_Token").IncludeProperties(e => e.UserId);

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Token).HasMaxLength(255);

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.PushNotificationTokens)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PushNotificationTokensUser");
        });

        modelBuilder.Entity<RemovedNotification>(entity =>
        {
            entity.ToTable("RemovedNotifications");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CalculationId, "IX_FK_CalculationRemovedNotifications");

            entity.HasIndex(e => e.ChangeRequestId, "IX_FK_RemovedEmployeeNotificationsChangeRequest");

            entity.HasIndex(e => e.ReservationId, "IX_FK_RemovedEmployeeNotificationsReservation");

            entity.HasIndex(e => e.TradeOfferId, "IX_FK_RemovedEmployeeNotificationsTradeOffer");

            entity.HasIndex(e => e.UserId, "IX_FK_RemovedEmployeeNotificationsUser");

            entity.HasIndex(e => e.EmployeePropertyId, "IX_FK_RemovedNotificationsEmployeeProperty");

            entity.HasIndex(e => e.RequestId, "IX_FK_RemovedNotificationsRequest");

            entity.HasIndex(e => e.RoleDelegationId, "IX_FK_RemovedNotificationsRoleDelegation");

            entity.HasIndex(e => e.TaskId, "IX_FK_RemovedNotificationsTask");

            entity.HasIndex(e => e.RequestPropertyId, "IX_FK_RequestPropertyRemovedNotifications");

            entity.HasIndex(e => e.ApprovalHistoryId, "IX_FK_RemovedNotificationsApprovalHistory");

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.ShiftConfirmationDate).IsStoredAsDateTime();

            entity
                .HasOne(d => d.Calculation)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.CalculationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_CalculationRemovedNotifications");

            entity
                .HasOne(d => d.ChangeRequest)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.ChangeRequestId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedEmployeeNotificationsChangeRequest");

            entity
                .HasOne(d => d.EmployeeProperty)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.EmployeePropertyId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedNotificationsEmployeeProperty");

            entity
                .HasOne(d => d.Request)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.RequestId)
                .HasConstraintName("FK_RemovedNotificationsRequest");

            entity
                .HasOne(d => d.RequestProperty)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.RequestPropertyId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RequestPropertyRemovedNotifications");

            entity
                .HasOne(d => d.Reservation)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.ReservationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedEmployeeNotificationsReservation");

            entity
                .HasOne(d => d.RoleDelegation)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.RoleDelegationId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedNotificationsRoleDelegation");

            entity
                .HasOne(d => d.Task)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedNotificationsTask");

            entity
                .HasOne(d => d.TradeOffer)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.TradeOfferId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedEmployeeNotificationsTradeOffer");

            entity
                .HasOne(d => d.ApprovalHistory)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.ApprovalHistoryId)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK_RemovedNotificationsApprovalHistory");

            entity
                .HasOne(d => d.User)
                .WithMany(p => p.RemovedNotifications)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RemovedEmployeeNotificationsUser");
        });

        modelBuilder.Entity<SentNotification>(entity =>
        {
            entity.ToTable("SentNotifications");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.EventParameters).HasMaxLength(1024);
            entity.Property(e => e.SendTime).IsStoredAsDateTime();
            entity.Property(e => e.Title).HasMaxLength(512);
        });

        modelBuilder.Entity<SentNotificationRecipient>(entity =>
        {
            entity.ToTable("SentNotificationRecipients");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.SentNotificationId, "IX_FK_SentNotificationRecipientSentNotification");

            entity.Property(e => e.Identifier).HasMaxLength(256);

            entity
                .HasOne(d => d.SentNotification)
                .WithMany(p => p.SentNotificationRecipients)
                .HasForeignKey(d => d.SentNotificationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SentNotificationRecipientSentNotification");
        });
    }
}
