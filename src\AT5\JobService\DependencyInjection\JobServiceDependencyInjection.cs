﻿namespace AT.JobService.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Linq;
using AT.Core.Jobs.JobService.ReloadLoop;
using AT.Core.Loops;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.Database;
using AT.Infrastructure.DependencyInjection;
using AT.Infrastructure.DependencyInjection.Extensions;
using AT.Infrastructure.Jobs.DependencyInjection;
using AT.Infrastructure.Loops;
using AT.Infrastructure.Utilities.DependencyInjection;
using AT.Infrastructure.Utilities.DependencyInjection.Extensions;
using AT.UseCases.Jobs;
using AT.Utilities.Exceptions;
using AT.Utilities.Logging;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Autofac.Multitenant;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

public static class JobServiceDependencyInjection
{
    /// <summary>
    /// MultitenantContainerFactory has already been registered. This method instructs how exactly should the MultitenantContainer be created.
    /// </summary>
    public static MultitenantContainer CreateMultitenantContainer(IContainer rootContainer)
    {
        var strategy = new DummyMultitenantResolutionStrategy();
        var mtc = new MultitenantContainer(strategy, rootContainer);

        // In here, we can register tenant-specific implementations via mtc.ConfigureTenant(tenantId, () => { }),
        // where tenantId can be `new DefaultTenantId().ToString()` to register implementations for the default tenant.

        return mtc;
    }

    /// <summary>
    /// Registrations from builder.Services were already added. Now, add AutoFac registrations to the container.
    /// </summary>
    public static void Populate(ContainerBuilder containerBuilder, IConfiguration configuration)
    {
        PopulateNonAutofac(containerBuilder, configuration);

        containerBuilder.AddCustomMultitenantServiceProviders();

        containerBuilder.AddCurrentTime();

        containerBuilder.AddSimpleJobScheduling();

        // Tenant info
        // TenantIdProvider is configured by AppServiceProvider
        containerBuilder
            .Register(
                (TenantIdProvider tenantIdProvider, IOrganizationInfoQuery orgInfoQuery) =>
                {
                    if (tenantIdProvider.OrganizationId is null)
                    {
                        return OrganizationInfo.DefaultTenantOrg;
                    }

                    return orgInfoQuery.GetOrganizationInfoOrThrow(tenantIdProvider.OrganizationId.Value);
                }
            )
            .InstancePerTenant();
        containerBuilder
            .Register((OrganizationInfo orgInfo) => new FullOrgId(orgInfo.Id, orgInfo.DatabaseName))
            .InstancePerTenant();
        containerBuilder
            .Register((OrganizationInfo orgInfo) => new OrgDbName(orgInfo.DatabaseName))
            .InstancePerTenant(); // Necessary for Organization DbContext

        // Org DB Access.
        containerBuilder.AddOrgDbContextServicesFromOrganizationDbTemplate(configuration);
        containerBuilder.AddRepositories();
        containerBuilder.AddQueries();

        containerBuilder.AddJobService();

        containerBuilder.RegisterJobs();

        containerBuilder.RegisterType<LoopFactory>().As<ILoopFactory>().InstancePerTenant();

        containerBuilder.RegisterType<JobReloadLoopIteration>();
    }

    private static ContainerBuilder RegisterJobs(this ContainerBuilder containerBuilder)
    {
        foreach (var jobSpecification in JobMapping.LegacyJobSpecifications.Concat(JobMapping.JobSpecifications))
        {
            containerBuilder.RegisterType(jobSpecification.JobImplementation);
        }

        return containerBuilder;
    }

    private static void PopulateNonAutofac(ContainerBuilder containerBuilder, IConfiguration configuration)
    {
        var serviceCollection = new ServiceCollection();

        serviceCollection.AddHostedService<JobsWorker>();

        serviceCollection.AddLogger(configuration);

        serviceCollection.AddParsers(configuration);

        serviceCollection.AddQuartzCustom();

        serviceCollection.AddMasterDatabaseServices(configuration);

        // Configurations need to be listed here. Should we make this more automatic?
        serviceCollection.Configure<GeneralConfig>(configuration.GetSection(nameof(GeneralConfig)));

        containerBuilder.Populate(serviceCollection);
    }

    public static IServiceProvider CreateSingleOrganizationServiceProviderForCliFx(
        IReadOnlyList<Type> commandTypes,
        IConfiguration configuration
    )
    {
        // Build standard multitenant service provider.
        var serviceProviderFactory = new CustomMultitenantAutofacServiceProviderFactory(
            CreateMultitenantContainer,
            containerBuilder =>
            {
                Populate(containerBuilder, configuration);

                // Add CLI commands
                foreach (var commandType in commandTypes)
                {
                    // Do not inject required properties of commands. Commands properties are parameters filled by CliFx.
                    // https://autofac.readthedocs.io/en/latest/register/prop-method-injection.html
                    containerBuilder
                        .RegisterType(commandType)
                        .AsSelf()
                        .InstancePerDependency()
                        .ResolveRequiredCliFxParametersWithNull();
                }
            }
        );

        var containerBuilder = serviceProviderFactory.CreateBuilder(new ServiceCollection());
        var rootServiceProvider = serviceProviderFactory.CreateServiceProvider(containerBuilder);

        // Get lifetime scope for the organization specified in configuration.
        var dbName = configuration.GetSection($"{nameof(GeneralConfig)}:{nameof(GeneralConfig.DbName)}").Get<string>()!;
        if (dbName is null)
        {
            throw new InvalidConfigurationFileException("Single DbName has to be specified in order to run commands.");
        }

        var fullOrgIdsQuery = rootServiceProvider.GetRequiredService<IFullOrgIdsQuery>();
        var fullOrgId = fullOrgIdsQuery.GetOrgIds().FirstOrDefault(x => x.DatabaseName == dbName);
        if (fullOrgId is null)
        {
            throw new InvalidConfigurationFileException($"Organization with DbName {dbName} not found.");
        }

        var appServiceProvider = rootServiceProvider.GetRequiredService<AppServiceProvider>();
        var orgLifetimeScope = appServiceProvider.GetTenantLifetimeScope(fullOrgId.Id);

        // Commands are resolved in a scope inside a single organization.
        return new AutofacServiceProvider(orgLifetimeScope.BeginLifetimeScope());
    }
}
