﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class RequestSyncState
{
    public RequestSyncStateId Id { get; set; }

    public RequestId RequestId { get; set; }

    public SyncStateType Type { get; set; }

    public DateTime Created { get; set; }

    public DateTime Changed { get; set; }

    public string Data { get; set; } = null!;

    public virtual Request Request { get; set; } = null!;
}
