﻿namespace AT.NotificationsService.PushNotifications;

public record PushNotificationSendToMultipleResult(
    int SuccessCount,
    int FailureCount,
    IReadOnlyCollection<Exception> Exceptions
)
{
    public bool Success => FailureCount == 0;

    public bool PartialSuccess => FailureCount > 0 && SuccessCount > 0;
}

public record PushNotificationSendToSingleResult(
    bool Success,
    bool TokenIsInvalid = false,
    Exception? Exception = null
);
