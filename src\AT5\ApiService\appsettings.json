{
  "ConnectionStrings": {
    "MasterDb": "Data Source=.\\;Initial Catalog=AristoTelos;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;",
    "OrgDbTemplate": "Data Source=.\\;Persist Security Info=True;Integrated Security=True;MultipleActiveResultSets=True;Encrypt=false;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  // Some settings that are not explicitly stated in here are added to the serilog in SerilogLoggerCreator.cs.
  "Serilog": {
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/ApiService_.log",
          "fileSizeLimitBytes": 31457280
        }
      }
    ]
  }
}
