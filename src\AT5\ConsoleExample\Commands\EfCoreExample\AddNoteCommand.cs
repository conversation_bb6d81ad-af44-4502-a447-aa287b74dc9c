namespace AT.ConsoleExample.Commands.EfCoreExample;

using AT.PrimitivesAT5.Ids;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Repositories;

[Command("ef add-note", Description = "Adds a note to a user")]
public class AddNoteCommand(IUserNoteEditor _userNoteEditor) : ICommand
{
    [CommandParameter(0, Description = "The ID of the user")]
    public required UserId UserId { get; set; }

    [CommandParameter(1, Description = "The note text")]
    public required string NoteText { get; set; }

    public async ValueTask ExecuteAsync(IConsole console)
    {
        await _userNoteEditor.AddNoteAsync(UserId, NoteText);
    }
}
