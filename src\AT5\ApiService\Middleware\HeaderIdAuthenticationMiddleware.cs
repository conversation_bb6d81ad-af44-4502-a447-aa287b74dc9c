﻿namespace AT.ApiService.Middleware;

using System.Security.Claims;
using System.Threading.Tasks;
using AT.ApiService.Authentication;

/// <summary>
/// Extract claims from request headers. Inspired by https://medium.com/@neer.s/how-to-create-custom-authentication-middleware-in-net-core-7-1a7ef57dbf0c
/// </summary>
public class HeaderIdAuthenticationMiddleware : IMiddleware
{
    private const string tenantHeaderName = "tenant-id";
    private const string userHeaderName = "user-id";

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        // For now we always create identity with tenant claim if available and user claim if available.

        var requestHeaders = context.Request.Headers;

        var claims = new List<Claim>();

        // Extract tenant header.
        bool hasTenantHeader = requestHeaders.TryGetValue(tenantHeaderName, out var tenantHeaderValues);
        if (hasTenantHeader && tenantHeaderValues is [{ } tenantHeaderValue])
        {
            var tenantIdClaim = new Claim(AristoTelosClaimTypes.TenantId, tenantHeaderValue);
            claims.Add(tenantIdClaim);
        }

        // Extract user header.
        bool hasUserHeader = requestHeaders.TryGetValue(userHeaderName, out var userHeaderValues);
        if (hasUserHeader && userHeaderValues is [{ } userHeaderValue])
        {
            var userIdClaim = new Claim(AristoTelosClaimTypes.UserId, userHeaderValue);
            claims.Add(userIdClaim);
        }

        // Create identity and principal.
        var identity = new ClaimsIdentity(claims);
        context.User = new ClaimsPrincipal(identity);

        await next.Invoke(context);
    }
}
