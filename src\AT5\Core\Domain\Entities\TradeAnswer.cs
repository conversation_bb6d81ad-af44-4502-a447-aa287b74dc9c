﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using Base;

public class TradeAnswer
{
    public TradeAnswerId Id { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public TradeOfferId TradeOfferId { get; set; }

    public DateTime Created { get; set; }

    public virtual RosterItem RosterItem { get; set; } = null!;

    public virtual TradeOffer TradeOffer { get; set; } = null!;
}
