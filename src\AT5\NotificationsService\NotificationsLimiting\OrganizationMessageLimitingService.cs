﻿namespace AT.NotificationsService.NotificationsLimiting;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.ConfigurationParameters;
using AT.NotificationsService.NotificationsLimiting.Interfaces;
using AT.Primitives.Enums;
using AT.Utilities.Collections;
using AT.Utilities.Logging;
using AT.Utilities.Task;

public class OrganizationMessageLimitingService(
    ILogger<OrganizationMessageLimitingService> _logger,
    ISentNotificationService _sentNotificationService
) : IOrganizationMessageLimitingService
{
    public async Task<IEnumerable<EmailMessage>> ApplyLimitingAsync(
        NotificationsRateLimitingConfig rateLimitingConfig,
        IReadOnlyCollection<EmailMessage> emailMessages,
        CancellationToken cancellationToken = default
    )
    {
        if (rateLimitingConfig.Emails.IsNullOrEmpty() || emailMessages.Count == 0)
        {
            return emailMessages;
        }

        var sentEmailMessages = await _sentNotificationService
            .GetSentEmailMessagesAsync(cancellationToken)
            .ToListAsync();
        if (sentEmailMessages.Count == 0)
        {
            return emailMessages;
        }

        var now = DateTime.Now;
        var configsPerType = rateLimitingConfig.Emails.GroupBy(c => c.Type).ToDictionary(g => g.Key, g => g.ToList());

        var result = new List<EmailMessage>(emailMessages.Count);
        var rateLimitingSentNotificationsPerEmailMessage =
            new Dictionary<EmailMessage, IReadOnlyCollection<SentNotification>>();

        foreach (var email in emailMessages)
        {
            if (!configsPerType.TryGetValue(email.EmailType, out var typeConfigs))
            {
                result.Add(email);
                continue;
            }

            var rateLimitingSentNotifications = GetRateLimitingSentNotificationsForEmailMessage(
                email,
                typeConfigs!,
                sentEmailMessages,
                now
            );
            if (rateLimitingSentNotifications.Count > 0)
            {
                rateLimitingSentNotificationsPerEmailMessage.Add(email, rateLimitingSentNotifications);
                continue;
            }

            result.Add(email);
        }

        foreach (
            (var filteredOutEmail, var rateLimitingSentNotifications) in rateLimitingSentNotificationsPerEmailMessage
        )
        {
            var recipientsOfRateLimitingSentNotifications = rateLimitingSentNotifications
                .SelectMany(sn => sn.SentNotificationRecipients)
                .ToList();
            var newRecipients = filteredOutEmail
                .EmailMessageRecipients.Where(r =>
                    recipientsOfRateLimitingSentNotifications.None(snr =>
                        snr.IdentifierType == SentNotificationIdentifierType.EmailAddress && snr.Identifier == r.Address
                        || snr.IdentifierType == SentNotificationIdentifierType.UserId
                            && r.UserId is not null
                            && snr.Identifier == r.UserId.Value.ToString()
                    )
                )
                .ToHashSet();

            if (newRecipients.Count > 0)
            {
                var emailMessageCopy = filteredOutEmail.Clone(withRecipients: false);
                emailMessageCopy.EmailMessageRecipients = newRecipients.Select(r => r.Clone()).ToArray();
                result.Add(emailMessageCopy);
            }
        }

        return result;
    }

    public async Task StoreAsSentNotificationsAsync(
        NotificationsRateLimitingConfig rateLimitingConfig,
        IReadOnlyDictionary<EmailMessage, IReadOnlyCollection<EmailMessageRecipient>> sentEmailMessages,
        CancellationToken cancellationToken = default
    )
    {
        if (rateLimitingConfig.Emails.IsNullOrEmpty() || sentEmailMessages.Count == 0)
        {
            return;
        }

        try
        {
            var rateLimitingConfigEmailTypes = rateLimitingConfig.Emails.Select(e => e.Type).ToHashSet();
            var rateLimitingEmailMessages = sentEmailMessages
                .Where(p => rateLimitingConfigEmailTypes.Contains(p.Key.EmailType))
                .ToDictionary();

            await _sentNotificationService.StoreEmailMessagesAsSentNotificationsAsync(
                rateLimitingEmailMessages,
                cancellationToken
            );
        }
        catch (Exception ex)
        {
            _logger.Error(
                ex,
                "The following email messages were not saved into SentNotifications: {TrackingIds}",
                string.Join(", ", sentEmailMessages.Select(p => p.Key.TrackingId))
            );
        }
    }

    public Task<NotificationsRateLimitingConfig?> LoadRateLimitingConfigAsync(
        CancellationToken cancellationToken = default
    )
    {
        return _sentNotificationService.GetRateLimitingConfigAsync(cancellationToken);
    }

    private static List<SentNotification> GetRateLimitingSentNotificationsForEmailMessage(
        EmailMessage email,
        IEnumerable<EmailMessageRateLimitingConfig> rateLimitingConfigs,
        IReadOnlyCollection<SentNotification> sentEmailMessages,
        DateTime now
    )
    {
        // For now, we consider two emails to be the same if they have the same EntityType, EntityId, and parameters.
        // In the future, we might wanna consider email's body and subject.
        var sameEntitySentEmails = sentEmailMessages
            .Where(n =>
                (int)email.EmailType == n.NotificationEntityType
                && email.EntityId == n.EntityId
                && email.EventParameters == n.EventParameters
            )
            .ToList();

        var limitingSentNotifications = new List<SentNotification>();

        foreach (var rateLimitingConfig in rateLimitingConfigs)
        {
            switch (rateLimitingConfig.IntervalType)
            {
                case RateLimitingByType.Day:
                    limitingSentNotifications.AddRange(
                        sameEntitySentEmails.Where(e => e.SendTime!.Value.Date == now.Date)
                    );
                    break;
            }
        }

        return limitingSentNotifications;
    }
}
