﻿namespace AT.Infrastructure.Queries;

using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using AT.Core.Domain.EmployeeRosterAggregate;
using AT.Core.Domain.EmployeeRosterAggregate.Queries;
using AT.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;

internal sealed class EmployeeRosterFinishQuery(IDataSource<EmployeeRosterFinish> _employeeRosterFinishDataSource)
    : IEmployeeRosterFinishQuery
{
    public async Task<List<EmployeeRosterFinishInfo>> GetEmployeeRosterFinishesAsync(
        UserId employeeId,
        CancellationToken cancellationToken = default(CancellationToken)
    )
    {
        return await _employeeRosterFinishDataSource
            .Data.Where(x => x.EmployeeId == employeeId)
            .Select(x => new EmployeeRosterFinishInfo(x.EmployeeId, x.DateInterval, x.State, x.Locked))
            .ToListAsync(cancellationToken);
    }

    public async Task<List<EmployeeRosterFinishInfo>> GetEmployeeRosterFinishesAsync(
        IReadOnlyCollection<UserId> employeeIds,
        CancellationToken cancellationToken = default
    )
    {
        return await _employeeRosterFinishDataSource
            .Data.Where(x => employeeIds.Contains(x.EmployeeId))
            .Select(x => new EmployeeRosterFinishInfo(x.EmployeeId, x.DateInterval, x.State, x.Locked))
            .ToListAsync(cancellationToken);
    }
}
