﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeeTimePreference
{
    public EmployeeTimePreferenceId Id { get; set; }

    public IntervalPreferenceType Type { get; set; }

    public string? Description { get; set; }

    public TimeInterval TimeInterval { get; set; }

    public int? Min { get; set; }

    public int? Max { get; set; }

    public double Priority { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public UserId EmployeeId { get; set; }

    public TimePreferenceWeeks Weeks { get; set; }

    public virtual Employee Employee { get; set; } = null!;
}
