﻿namespace AT.DataStructures.Time;

using System.Runtime.Serialization;

/// <summary>
/// Represents a validity-aware wrapper for an <see cref="OpenDateTimeInterval"/>.
/// This struct stores an interval and tracks whether it is considered valid.
/// When marked as invalid, the original interval is preserved for user interaction purposes,
/// allowing it to be remembered after being set to invalid.
///
/// <para>
/// This type should only be used in contexts involving user interaction where preserving
/// the stored interval is necessary. In service layers or business logic, it should be
/// converted to <see cref="OpenDateTimeInterval"/> using the implicit conversion or the
/// <see cref="ToOpenDateTimeInterval"/> method.
/// </para>
/// </summary>
[DataContract]
public readonly record struct TimeValidity
{
    /// <summary>
    /// Gets the stored interval represented by this instance.
    /// </summary>
    [DataMember]
    public OpenDateTimeInterval Interval { get; }

    /// <summary>
    /// Gets a value indicating whether the interval is marked as invalid.
    /// </summary>
    [DataMember]
    public bool IsInvalid { get; }

    /// <summary>
    /// Gets a value indicating whether the interval is considered valid.
    /// </summary>
    public bool IsValid => !IsInvalid;

    /// <summary>
    /// Gets the start date and time of the interval.
    /// </summary>
    public DateTime? Start => Interval.Start;

    /// <summary>
    /// Gets the end date and time of the interval.
    /// </summary>
    public DateTime? End => Interval.End;

    /// <summary>
    /// Initializes a new valid instance of the <see cref="TimeValidity"/> struct with an empty interval.
    /// </summary>
    public TimeValidity()
        : this(default(DateTime?), default(DateTime?)) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="TimeValidity"/> struct with the specified start
    /// and end dates and times and an optional validity state.
    /// </summary>
    /// <param name="start">The start date and time of the interval.</param>
    /// <param name="end">The end date and time of the interval.</param>
    /// <param name="isInvalid">A value indicating whether the interval should be marked as invalid. Defaults to <c>false</c>.</param>
    public TimeValidity(DateTime? start, DateTime? end, bool isInvalid = false)
        : this(new OpenDateTimeInterval(start, end), isInvalid) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="TimeValidity"/> struct with the specified interval
    /// and an optional validity state.
    /// </summary>
    /// <param name="interval">The interval to wrap in the <see cref="TimeValidity"/> struct.</param>
    /// <param name="isInvalid">A value indicating whether the interval should be marked as invalid. Defaults to <c>false</c>.</param>
    public TimeValidity(OpenDateTimeInterval interval, bool isInvalid = false)
    {
        Interval = interval;
        IsInvalid = isInvalid;
    }

    /// <summary>
    /// Implicitly converts a <see cref="TimeValidity"/> instance to an <see cref="OpenDateTimeInterval"/> using <see cref="ToOpenDateTimeInterval"/>.
    /// If the interval is invalid, an empty <see cref="OpenDateInterval"/> is returned.
    /// </summary>
    /// <param name="validity">The <see cref="TimeValidity"/> instance to convert.</param>
    public static implicit operator OpenDateTimeInterval(TimeValidity validity)
    {
        return validity.ToOpenDateTimeInterval();
    }

    /// <summary>
    /// Converts the current instance to an <see cref="OpenDateTimeInterval"/>.
    /// If the interval is invalid, an empty <see cref="OpenDateTimeInterval"/> is returned.
    /// </summary>
    /// <returns>The stored interval if valid; otherwise, an empty <see cref="OpenDateTimeInterval"/>.</returns>
    public OpenDateTimeInterval ToOpenDateTimeInterval()
    {
        if (IsInvalid)
        {
            return new OpenDateTimeInterval();
        }

        return Interval;
    }
}
