﻿namespace AT.ApiService.Endpoints;

using System.Threading;
using System.Threading.Tasks;
using AT.Core.Domain.UserAggregate;
using AT.Shared.Models;
using FastEndpoints;

public class ForecastEndpoint(ILogger<ForecastEndpoint> _logger) : EndpointWithoutRequest<ForecastResponse>
{
    private static readonly string[] s_summaries =
    [
        "Freezing",
        "Bracing",
        "Chilly",
        "Cool",
        "Mild",
        "Warm",
        "<PERSON><PERSON>y",
        "Hot",
        "Sweltering",
        "Scorching"
    ];

    public override void Configure()
    {
        Get("/api/forecast");
        AllowAnonymous();
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        _logger.Info($"{nameof(ForecastEndpoint)} was called");

        var forecast = Enumerable
            .Range(1, 5)
            .Select(index => new Forecast(
                DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                Random.Shared.Next(-20, 55),
                s_summaries[Random.Shared.Next(s_summaries.Length)]
            ))
            .ToArray();

        await SendAsync(new ForecastResponse() { Forecasts = forecast, }, cancellation: ct);
    }
}
