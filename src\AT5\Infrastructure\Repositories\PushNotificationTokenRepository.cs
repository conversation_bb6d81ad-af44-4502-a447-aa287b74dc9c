﻿namespace AT.Infrastructure.Repositories;

using Core.Domain.PushNotificationTokenAggregate;
using DataAccess;
using Database;

public sealed class PushNotificationTokenRepository(OrganizationDbContext _dbContext)
    : EfRepository<PushNotificationToken>(_dbContext),
        IPushNotificationTokenRepository,
        IRepositoryWithFactoryMethod<IPushNotificationTokenRepository>
{
    public static new IPushNotificationTokenRepository Create(OrganizationDbContext dbContext)
    {
        return new PushNotificationTokenRepository(dbContext);
    }
}
