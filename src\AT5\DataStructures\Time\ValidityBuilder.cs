﻿namespace AT.DataStructures.Time;

/// <summary>
/// A builder for creating and configuring instances of <see cref="Validity"/>.
/// </summary>
public class ValidityBuilder
{
    /// <summary>
    /// Gets the start date of the validity interval being built.
    /// </summary>
    public DateOnly? Start { get; private set; }

    /// <summary>
    /// Gets the end date of the validity interval being built.
    /// </summary>
    public DateOnly? End { get; private set; }

    /// <summary>
    /// Gets a value indicating whether the interval being built is marked as invalid.
    /// </summary>
    public bool IsInvalid { get; private set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="ValidityBuilder"/> class with default values.
    /// </summary>
    public ValidityBuilder()
        : this(new Validity()) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="ValidityBuilder"/> class using an existing <see cref="Validity"/> instance.
    /// </summary>
    /// <param name="validity">The <see cref="Validity"/> instance to initialize the builder with.</param>
    public ValidityBuilder(Validity validity)
    {
        Start = validity.Start;
        End = validity.End;
        IsInvalid = validity.IsInvalid;
    }

    /// <summary>
    /// Sets the start date and time for the interval being built.
    /// </summary>
    /// <param name="date">The start date to set. Can be <c>null</c> for an unlimited start.</param>
    /// <returns>The current instance of the <see cref="ValidityBuilder"/> for method chaining.</returns>
    public ValidityBuilder WithStart(DateOnly? date)
    {
        Start = date;
        return this;
    }

    /// <summary>
    /// Removes the start date, making the interval's start unlimited.
    /// </summary>
    /// <returns>The current instance of the <see cref="ValidityBuilder"/> for method chaining.</returns>
    public ValidityBuilder WithUnlimitedStart()
    {
        return WithStart(null);
    }

    /// <summary>
    /// Sets the end date for the interval being built.
    /// </summary>
    /// <param name="date">The end date to set. Can be <c>null</c> for an unlimited start.</param>
    /// <returns>The current instance of the <see cref="ValidityBuilder"/> for method chaining.</returns>
    public ValidityBuilder WithEnd(DateOnly? date)
    {
        End = date;
        return this;
    }

    /// <summary>
    /// Removes the end date, making the interval's start unlimited.
    /// </summary>
    /// <returns>The current instance of the <see cref="ValidityBuilder"/> for method chaining.</returns>
    public ValidityBuilder WithUnlimitedEnd()
    {
        return WithEnd(null);
    }

    /// <summary>
    /// Marks the validity being built as valid.
    /// </summary>
    /// <returns>The current instance of the <see cref="ValidityBuilder"/> for method chaining.</returns>
    public ValidityBuilder AsValid()
    {
        IsInvalid = false;
        return this;
    }

    /// <summary>
    /// Marks the validity being built as invalid.
    /// </summary>
    /// <returns>The current instance of the <see cref="ValidityBuilder"/> for method chaining.</returns>
    public ValidityBuilder AsInvalid()
    {
        IsInvalid = true;
        return this;
    }

    /// <summary>
    /// Builds and returns a new <see cref="Validity"/> instance based on the current configuration.
    /// </summary>
    /// <returns>A new <see cref="Validity"/> instance.</returns>
    public Validity Build()
    {
        return new Validity(Start, End, IsInvalid);
    }
}
