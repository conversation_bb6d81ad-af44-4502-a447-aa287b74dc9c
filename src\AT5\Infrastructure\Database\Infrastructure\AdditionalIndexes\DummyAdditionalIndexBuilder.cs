﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System.Linq.Expressions;

public class DummyAdditionalIndexBuilder<T> : ITypedAdditionalIndexBuilder<T>
    where T : class
{
    public ITypedAdditionalIndexBuilder<T> IncludeProperties(Expression<Func<T, object?>> includeExpression)
    {
        return this;
    }

    public ITypedAdditionalIndexBuilder<T> IncludeProperties(IEnumerable<string> columnNames)
    {
        return this;
    }

    public ITypedAdditionalIndexBuilder<T> WithSettings(object settings)
    {
        return this;
    }

    public string Build()
    {
        throw new NotImplementedException();
    }
}
