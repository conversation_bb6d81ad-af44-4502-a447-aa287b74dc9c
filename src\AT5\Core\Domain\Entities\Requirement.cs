﻿namespace AT.Core.Domain.Entities;

public class Requirement
{
    public RequirementId Id { get; set; }

    public RosterItemPartTypeId RosterItemPartTypeId { get; set; }

    public SkillId? SkillId { get; set; }

    public string? Description { get; set; }

    public TimeInterval TimeInterval { get; set; }

    public int? Minimum { get; set; }

    public int? Maximum { get; set; }

    public string? PrioritySteps { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public NeedReservation ReservationsAllowed { get; set; }

    public TimeOnly? AllowedShiftExtension { get; set; }

    public TimeOnly AllowedShiftShortening { get; set; }

    public double? FillPriority { get; set; }

    public NeedType Type { get; set; }

    public string? Parameters { get; set; }

    public SiteId SiteId { get; set; }

    public OpenDateInterval TotalInterval { get; set; }

    public virtual ICollection<Reservation> Reservations { get; set; } = new List<Reservation>();

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;

    public virtual Skill? Skill { get; set; }

    public virtual ICollection<Employee> AllowedEmployees { get; set; } = new List<Employee>();

    public virtual ICollection<Team> AllowedTeams { get; set; } = new List<Team>();

    public virtual ICollection<Location> Locations { get; set; } = new List<Location>();

    public virtual ICollection<RosterItemPartType> SatisfactoryRosterItemPartTypes { get; set; } =
        new List<RosterItemPartType>();

    public virtual ICollection<ShiftTemplate> SatisfactoryShiftTemplates { get; set; } = new List<ShiftTemplate>();

    public virtual ICollection<ShiftTemplate> ShiftTemplates { get; set; } = new List<ShiftTemplate>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
