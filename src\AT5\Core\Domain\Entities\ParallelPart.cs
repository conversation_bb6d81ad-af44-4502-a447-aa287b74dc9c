﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;

public class ParallelPart
{
    public ParallelPartId Id { get; set; }

    public DateTimeInterval Interval { get; set; }

    public RosterItemPartTypeId RosterItemPartTypeId { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public virtual RosterItem RosterItem { get; set; } = null!;

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;
}
