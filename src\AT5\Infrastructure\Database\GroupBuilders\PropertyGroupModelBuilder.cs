﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Property"/> in the database.
/// </summary>
internal static class PropertyGroupModelBuilder
{
    public static void BuildPropertyGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Property>(entity =>
        {
            entity.ToTable("Properties");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.CategoryId, "IX_FK_PropertyCategoryProperty");

            entity.Property(e => e.Code).HasMaxLength(10);
            entity.Property(e => e.DefaultValue).HasMaxLength(4000);
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(255);

            entity
                .HasOne(d => d.Category)
                .WithMany(p => p.Properties)
                .HasForeignKey(d => d.CategoryId)
                .HasConstraintName("FK_PropertyCategoryProperty");

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.AllowedProperties)
                .UsingEntity<Dictionary<string, object>>(
                    "PropertySite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PropertySite_Site"),
                    l =>
                        l.HasOne<Property>()
                            .WithMany()
                            .HasForeignKey("AllowedPropertiesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_PropertySite_Property"),
                    j =>
                    {
                        j.HasKey("AllowedPropertiesId", "SitesId");
                        j.ToTable("PropertySite");
                        j.HasIndex(["SitesId"], "IX_FK_PropertySite_Site");
                        j.IndexerProperty<PropertyId>("AllowedPropertiesId").HasColumnName("AllowedProperties_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<PropertyCategory>(entity =>
        {
            entity.ToTable("PropertyCategories");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parameters).HasMaxLength(1024);
        });

        modelBuilder.Entity<PropertyFilter>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("PropertyFilters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.FilterId).HasColumnOrder(iota);
            entity.HasIndex(e => e.FilterId, "IX_FK_PropertyFilterFilter2");

            entity.Property(e => e.PropertyId).HasColumnOrder(iota);
            entity.HasIndex(e => e.PropertyId, "IX_FK_PropertyFilterProperty");

            entity.HasValidity(e => e.Validity, iota);

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.PropertyFilters)
                .HasForeignKey(d => d.FilterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PropertyFilterFilter2");

            entity
                .HasOne(d => d.Property)
                .WithMany(p => p.PropertyFilters)
                .HasForeignKey(d => d.PropertyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PropertyFilterProperty");
        });
    }
}
