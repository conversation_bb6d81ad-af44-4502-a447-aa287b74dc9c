﻿namespace AT.Core.Jobs.JobService;

using System.Collections.Immutable;
using AT.Core.Domain.JobsAggregate;
using AT.Core.Jobs.DurableJobs;
using AT.Core.Jobs.DurableJobs.Identifiers;
using AT.Core.MasterDomain;
using AT.Primitives.Enums;
using AT.Utilities.Collections;
using AT.Utilities.Logging;
using AT.Utilities.Parsing;
using AT.Utilities.Task;

/// <summary>
/// Service for running user defined jobs.
/// </summary>
public class JobService(
    ILogger<JobService> _logger,
    IDurableJobSeriesScheduler _jobSeriesScheduler,
    FullOrgId _fullOrgId,
    IJsonParser _jsonParser,
    string? _aristoTelosWebUrl,
    IImmutableDictionary<JobType, JobSpecification> _jobSpecifications
) : IJobService
{
    private readonly AsyncLock _asyncLock = new();

    private readonly List<StoredJobSeries> _storedJobSeries = [];
    private readonly List<StoredTrigger> _storedTriggers = [];

    public IReadOnlyCollection<StoredJobSeries> StoredJobSeriesCollection => _storedJobSeries;

    public IReadOnlyCollection<StoredTrigger> StoredTriggersCollection => _storedTriggers;

    public async Task RunAsync(CancellationToken ct)
    {
        _logger.Info(
            "Starting job service for {OrganizationName} ({OrganizationId}).",
            _fullOrgId.DatabaseName,
            _fullOrgId.Id
        );

        try
        {
            await Task.Delay(Timeout.Infinite, ct);
        }
        catch (OperationCanceledException)
        {
            // Expected when cancelled.
        }

        _logger.Info(
            "Stopping job service for {OrganizationName} ({OrganizationId}).",
            _fullOrgId.DatabaseName,
            _fullOrgId.Id
        );
    }

    public async Task UpdateJobs(IEnumerable<JobSeriesInfo> jobSeries, CancellationToken ct)
    {
        using (await _asyncLock.EnterScope(ct))
        {
            _logger.Info("Updating jobs.");
            _logger.Info(
                "JobService status before update: {JobsCount} jobs, {TriggersCount} triggers.",
                _storedJobSeries.Count,
                _storedTriggers.Count
            );

            var currentJobs = jobSeries.ToList();
            var currentTriggers = jobSeries.SelectMany(x => x.Triggers).ToList();

            var triggersDiff = CollectionUtils.CompareCollections(
                _storedTriggers.Select(x => x.JobTriggerInfo),
                currentTriggers,
                x => x.Id,
                AreTriggersSame
            );
            var jobSeriesDiff = CollectionUtils.CompareCollections(
                _storedJobSeries.Select(x => x.JobSeriesInfo),
                currentJobs,
                x => x.Key,
                AreJobSeriesSame
            );

            _logger.Info(
                "Triggers diff: {AddedTriggersCount} added, {RemovedTriggersCount} removed, {ChangedTriggersCount} changed.",
                triggersDiff.Added.Count,
                triggersDiff.Removed.Count,
                triggersDiff.Changed.Count
            );
            _logger.Info(
                "Triggers diff: {AddedTriggersCount} added, {RemovedTriggersCount} removed, {ChangedTriggersCount} changed.",
                triggersDiff.Added.Count,
                triggersDiff.Removed.Count,
                triggersDiff.Changed.Count
            );

            await DeleteTriggers(triggersDiff, ct);
            await DeleteJobSeries(jobSeriesDiff, ct);

            await CreateJobSeries(jobSeriesDiff, ct);
            await CreateTriggers(triggersDiff, jobSeriesDiff, ct);

            _logger.Info(
                "JobService status after update: {JobsCount} jobs, {TriggersCount} triggers.",
                _storedJobSeries.Count,
                _storedTriggers.Count
            );
        }
    }

    private async Task DeleteTriggers(CollectionDiffResult<JobTriggerInfo> triggersDiff, CancellationToken ct)
    {
        var triggersToDelete = GetTriggersToDelete(triggersDiff);
        _logger.Info("Deleting {TriggersCount} triggers.", triggersToDelete.Length);

        bool deleteSuccessful = await _jobSeriesScheduler.UnscheduleDurableJobSeries(triggersToDelete, ct);
        if (!deleteSuccessful)
        {
            _logger.Error("Some of the triggers weren't deleted.");
        }

        _storedTriggers.RemoveAll(x => triggersToDelete.Contains(x.TriggerIdentifier));
    }

    private async Task DeleteJobSeries(CollectionDiffResult<JobSeriesInfo> jobSeriesDiff, CancellationToken ct)
    {
        var jobsToDelete = GetJobsToDelete(jobSeriesDiff);
        _logger.Info("Deleting {JobsCount} jobs.", jobsToDelete.Length);

        bool deleteSuccessful = await _jobSeriesScheduler.DeleteDurableJobSeries(jobsToDelete, ct);
        if (!deleteSuccessful)
        {
            _logger.Error("Some of the triggers weren't deleted.");
        }

        _storedJobSeries.RemoveAll(x => jobsToDelete.Contains(x.JobIdentifier));
    }

    private async Task CreateJobSeries(CollectionDiffResult<JobSeriesInfo> jobSeriesDiff, CancellationToken ct)
    {
        var jobSeriesToCreate = GetJobSeriesToCreate(jobSeriesDiff);
        await CreateJobSeries(jobSeriesToCreate, ct);
    }

    private async Task CreateJobSeries(JobSeriesInfo[] jobSeriesToCreate, CancellationToken ct)
    {
        foreach (var jobSeries in jobSeriesToCreate)
        {
            var jobSeriesName = string.Join('_', jobSeries.JobSeries.Select(x => $"{x.Name} ({x.Id})"));

            _logger.Info("Creating job series {JobSeriesName} ({JobSeriesKey}).", jobSeriesName, jobSeries.Key);

            var jobsDefinitions = new List<JobDefinition>();
            foreach (var job in jobSeries.JobSeries)
            {
                var jobName = job.Name;
                var jobId = job.Id;
                var jobType = job.JobType;
                if (!_jobSpecifications.TryGetValue(jobType, out var specification))
                {
                    _logger.Warn(
                        "Unspecified job type {JobType} for job {JobName} ({JobId}).",
                        jobType,
                        jobName,
                        jobId
                    );
                    break;
                }

                if (
                    !_jsonParser.TryDeserialize(
                        job.Parameters,
                        specification.JobParamsType,
                        out var jobParsedParameters
                    ) || jobParsedParameters is null
                )
                {
                    _logger.Warn(
                        "Failed to parse parameters of job {JobName} ({JobId}) '{JobParameters}' into type {JobParametersType}.",
                        [jobName, jobId, job.Parameters, specification.JobParamsType]
                    );
                    break;
                }

                var jobDefinition = new JobDefinition(
                    jobId,
                    jobName,
                    job.SuccessEmails,
                    job.ErrorEmails,
                    _aristoTelosWebUrl,
                    specification.JobImplementation,
                    jobParsedParameters
                );
                jobsDefinitions.Add(jobDefinition);
            }

            bool areAllJobsDefined = jobsDefinitions.Count == jobSeries.JobSeries.Count;
            if (!areAllJobsDefined)
            {
                _logger.Warn(
                    "Cannot create job series {JobSeriesName} ({JobSeriesKey}).",
                    jobSeriesName,
                    jobSeries.Key
                );
                continue;
            }

            var jobSeriesDefinition = new JobSeriesDefinition(jobSeriesName, jobsDefinitions);
            var jobIdentifier = await _jobSeriesScheduler.AddDurableJobSeries(jobSeriesDefinition, ct);

            _logger.Info(
                "Job series {JobSeriesName} ({JobSeriesKey}) created with identifier {JobIdentifier}.",
                jobSeries,
                jobSeries.Key,
                jobIdentifier
            );

            _storedJobSeries.Add(new StoredJobSeries(jobIdentifier, jobSeries));
        }
    }

    private async Task CreateTriggers(
        CollectionDiffResult<JobTriggerInfo> triggersDiff,
        CollectionDiffResult<JobSeriesInfo> jobsDiff,
        CancellationToken ct
    )
    {
        var triggersToCreate = GetTriggersToCreate(triggersDiff, jobsDiff);
        await CreateTriggers(triggersToCreate, ct);
    }

    private async Task CreateTriggers(JobTriggerInfo[] triggersToCreate, CancellationToken ct)
    {
        foreach (var trigger in triggersToCreate)
        {
            var triggerName = trigger.Name;
            var triggerId = trigger.Id;

            _logger.Info("Creating trigger {TriggerName} ({TriggerId}).", triggerName, triggerId);

            var triggerType = trigger.TriggerType;
            if (triggerType != JobTriggerType.Cron)
            {
                _logger.Warn(
                    "Unsupported trigger type {TriggerType} for trigger {TriggerName} ({TriggerId}).",
                    triggerType,
                    triggerName,
                    triggerId
                );
                continue;
            }

            await CreateSingleJobTrigger(trigger, ct);
        }
    }

    private async Task CreateSingleJobTrigger(JobTriggerInfo trigger, CancellationToken ct)
    {
        var triggerName = trigger.Name;
        var triggerId = trigger.Id;

        _logger.Info("Creating single job trigger {TriggerName} ({TriggerId}).", triggerName, triggerId);

        var triggeredJobSeriesKey = trigger.TriggeredJobSeriesKey;
        var jobIdentifier = _storedJobSeries
            .FirstOrDefault(j => j.JobSeriesInfo.Key == triggeredJobSeriesKey)
            ?.JobIdentifier;
        if (jobIdentifier is null)
        {
            _logger.Error(
                "Job {JobId} triggered by trigger {TriggerName} ({TriggerId}) wasn't found.",
                triggeredJobSeriesKey,
                triggerName,
                triggerId
            );
            return;
        }

        var triggerIdentifier = await _jobSeriesScheduler.ScheduleDurableJobSeries(
            jobIdentifier,
            $"{triggerName}_{triggerId}",
            trigger.Cron,
            ct
        );

        if (triggerIdentifier is null)
        {
            _logger.Error("Trigger {TriggerName} ({TriggerId}) couldn't be created.", triggerName, triggerId);
            return;
        }

        _logger.Info(
            "Trigger {TriggerName} ({TriggerId}) created with identifier {TriggerIdentifier}.",
            triggerName,
            triggerId,
            triggerIdentifier
        );

        _storedTriggers.Add(new StoredTrigger(triggerIdentifier, trigger));
    }

    private ITriggerIdentifier[] GetTriggersToDelete(CollectionDiffResult<JobTriggerInfo> triggersDiff)
    {
        var triggersToDelete = triggersDiff
            .Removed.Concat(triggersDiff.Changed.Select(x => x.From))
            .Select(x => x.Id)
            .ToHashSet();
        return _storedTriggers
            .Where(x => triggersToDelete.Contains(x.JobTriggerInfo.Id))
            .Select(x => x.TriggerIdentifier)
            .ToArray();
    }

    private IJobIdentifier[] GetJobsToDelete(CollectionDiffResult<JobSeriesInfo> jobSeriesDiff)
    {
        var jobsToDelete = jobSeriesDiff
            .Removed.Concat(jobSeriesDiff.Changed.Select(x => x.From))
            .Select(x => x.Key)
            .ToHashSet();
        return _storedJobSeries
            .Where(x => jobsToDelete.Contains(x.JobSeriesInfo.Key))
            .Select(x => x.JobIdentifier)
            .ToArray();
    }

    private static JobSeriesInfo[] GetJobSeriesToCreate(CollectionDiffResult<JobSeriesInfo> jobSeriesDiff)
    {
        return [.. jobSeriesDiff.Added, .. jobSeriesDiff.Changed.Select(x => x.To)];
    }

    private static JobTriggerInfo[] GetTriggersToCreate(
        CollectionDiffResult<JobTriggerInfo> triggersDiff,
        CollectionDiffResult<JobSeriesInfo> jobsDiff
    )
    {
        // NOTE: We need to recreate the triggers of the jobs that were changed and thus deleted.
        return
        [
            .. triggersDiff.Added,
            .. triggersDiff.Changed.Select(x => x.To),
            .. jobsDiff.Changed.Select(x => x.To).SelectMany(x => x.Triggers)
        ];
    }

    private static bool AreJobSeriesSame(JobSeriesInfo first, JobSeriesInfo second)
    {
        // NOTE: We consider jobs different only when they need to be recreated.
        // Disabled is already handled when loading from the database.

        if (first.JobSeries.Count != second.JobSeries.Count)
        {
            return false;
        }

        foreach (var (firstJob, secondJob) in first.JobSeries.Zip(second.JobSeries))
        {
            bool areSame = true;
            areSame = areSame && firstJob.Name == secondJob.Name;
            areSame = areSame && firstJob.JobType == secondJob.JobType;
            areSame = areSame && firstJob.Parameters == secondJob.Parameters;

            if (!areSame)
            {
                return false;
            }
        }

        return true;
    }

    private static bool AreTriggersSame(JobTriggerInfo first, JobTriggerInfo second)
    {
        // NOTE: We consider job triggers different only when they need to be recreated.
        // Disabled is already handled when loading from the database.
        bool areSame = true;
        areSame = areSame && first.Name == second.Name;
        areSame = areSame && first.TriggerType == second.TriggerType;
        areSame = areSame && first.Cron == second.Cron;
        areSame = areSame && first.TriggeredJobSeriesKey == second.TriggeredJobSeriesKey;
        return areSame;
    }

    public sealed record StoredJobSeries(IJobIdentifier JobIdentifier, JobSeriesInfo JobSeriesInfo);

    public sealed record StoredTrigger(ITriggerIdentifier TriggerIdentifier, JobTriggerInfo JobTriggerInfo);
}
