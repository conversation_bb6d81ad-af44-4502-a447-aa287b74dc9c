namespace AT.Infrastructure.Services;

using AT.Utilities.Logging;
using Core.Services;
using UserName = Core.Domain.ExampleEntities.UserName;

public class LoggingNotifier(ILogger<LoggingNotifier> _logger) : INotifier
{
    public Task SendNotificationAsync(UserName user, NotificationMessage message)
    {
        _logger.Info("{user}: {message}", user, message);

        return Task.CompletedTask;
    }
}
