﻿namespace AT.ApiClient;

using System.Text.Json;

public partial class ApiClient
{
    static partial void UpdateJsonSerializerSettings(JsonSerializerOptions settings)
    {
        // FastEndpoints creates JSON responses with camelCase property names. Then, ApiClient tries to parse the JSON into the original
        // model object. Without setting this to true, the parsing fails.
        settings.PropertyNameCaseInsensitive = true;
    }

    // NOTE: This should be handy when we start using reverse proxy for communicating with ApiService.
    //partial void PrepareRequest(System.Net.Http.HttpClient client, System.Net.Http.HttpRequestMessage request, System.Text.StringBuilder urlBuilder)
    //{
    //    // For using reverse proxy on the Web.Server.
    //    urlBuilder.Insert(0, "api/");
    //}
}
