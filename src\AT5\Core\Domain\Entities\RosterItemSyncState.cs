﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using Base;
using Primitives.Enums;

public class RosterItemSyncState
{
    public RosterItemSyncStateId Id { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public SyncStateType Type { get; set; }

    public DateTime Created { get; set; }

    public DateTime Changed { get; set; }

    public string Data { get; set; } = null!;

    public virtual RosterItem RosterItem { get; set; } = null!;
}
