﻿namespace AT.Core.Domain.Entities;

using Base;

public class BudgetActivity
{
    public BudgetActivityId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public RosterItemPartTypeId ActivityId { get; set; }

    public SkillId? SkillId { get; set; }

    public virtual ActivityType Activity { get; set; } = null!;

    public virtual ICollection<BudgetHoursAllowance> BudgetHoursAllowances { get; set; } =
        new List<BudgetHoursAllowance>();

    public virtual Skill? Skill { get; set; }
}
