﻿namespace AT.Utilities.Time;

public static class DbDateLimits
{
    /// <summary>
    /// Minimum date to be stored to database (may differ for different databases).
    /// We use start of 1900 here, even though the MS SQL allows less.
    /// </summary>
    public static readonly DateTime Min = new DateTime(1900, 1, 1);

    /// <summary>
    /// Maximum date to be stored to database (may differ for different databases).
    /// We use start of 9000-01-01 here, even though the MS SQL allows more. This way we can both add and subtract from it.
    /// </summary>
    public static readonly DateTime Max = new DateTime(9000, 1, 1);
}
