namespace AT.Translations.Generator;

using AT.Core;
using AT.Shared;
using AT.SharedResources;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

[Command("ResxGenerator", Description = "")]
public class ResxGeneratorCommand() : ICommand
{
    [CommandParameter(0, Description = "The name of the project for which translations are generated.")]
    public required string Project { get; set; }

    public async ValueTask ExecuteAsync(IConsole console)
    {
        var targetAssemblyType = Project switch
        {
            nameof(SharedResources) => typeof(SharedResourcesAssembly),
            nameof(Core) => typeof(CoreAssembly),
            nameof(Shared) => typeof(SharedAssembly),

            _ => throw new NotImplementedException("Include the target project's assembly type in this switch")
        };

        var generator = new ResxTranslationsGenerator();

        await generator.GenerateAsync(targetAssemblyType);
    }
}
