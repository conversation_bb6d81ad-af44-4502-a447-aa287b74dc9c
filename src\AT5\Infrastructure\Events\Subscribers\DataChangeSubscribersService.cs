﻿namespace AT.Infrastructure.Events.Subscribers;

using System;
using System.Collections.Generic;
using System.Linq;
using AT.Infrastructure.Events.Events;

public class DataChangeSubscribersService : IDataChangeSubscribersService
{
    private readonly List<IDataChangeSubscriber> _subscribers = new();
    private readonly Lock _subscribersLock = new();

    public Guid AddSubscriber<TMessage>(Action<TMessage> messageHandler)
        where TMessage : DataChangeEvent
    {
        var id = Guid.NewGuid();
        var subscriber = new DataChangeSubscriber<TMessage>(id, messageHandler);

        lock (_subscribersLock)
        {
            _subscribers.Add(subscriber);
        }

        return id;
    }

    public IReadOnlyList<IDataChangeSubscriber> GetSubscribers()
    {
        lock (_subscribersLock)
        {
            return _subscribers.ToList();
        }
    }
}
