﻿namespace AT.Shared.Formatting.Number.Formatters;

using System.Globalization;
using AT.Translations;

public sealed class IntegerFormatter(ITranslator _translator)
{
    public string Format(int value, Language? language = null)
    {
        var format = _translator.Translate<NumberFormatTranslations.Integer>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }
}
