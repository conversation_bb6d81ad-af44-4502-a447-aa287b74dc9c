﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class Contract
{
    public ContractId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public ContractType ContractType { get; set; }

    public bool IsGlobal { get; set; }

    public string? Code { get; set; }

    public virtual ICollection<EmployeeContract> EmployeeContracts { get; set; } = new List<EmployeeContract>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
