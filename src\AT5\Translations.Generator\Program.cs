﻿using CliFx;
using Microsoft.Extensions.DependencyInjection;

await new CliApplicationBuilder()
    .AddCommandsFromThisAssembly()
    .UseTypeActivator(commandTypes =>
    {
        var serviceCollection = new ServiceCollection();

        // Add CLI commands
        foreach (var commandType in commandTypes)
        {
            serviceCollection.AddTransient(commandType);
        }

        return serviceCollection.BuildServiceProvider();
    })
    .Build()
    .RunAsync();