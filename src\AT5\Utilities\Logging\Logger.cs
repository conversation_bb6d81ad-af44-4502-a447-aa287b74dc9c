﻿namespace AT.Utilities.Logging;

using System;
using System.Runtime.CompilerServices;
using Microsoft.Extensions.Logging;

[System.Diagnostics.CodeAnalysis.SuppressMessage(
    "Usage",
    "CA2254:Template should be a static expression",
    Justification = "The message template includes dynamic caller information (e.g., memberName, sourceFilePath, and sourceLineNumber), which requires runtime string interpolation."
)]
[System.Diagnostics.CodeAnalysis.SuppressMessage(
    "Usage",
    "S2629:Don't use string interpolation in logging message templates.",
    Justification = "The message template includes dynamic caller information (e.g., memberName, sourceFilePath, and sourceLineNumber), which requires runtime string interpolation."
)]
public class Logger<TCategoryName>(Microsoft.Extensions.Logging.ILogger<TCategoryName> _logger) : ILogger<TCategoryName>
{
    private const string callerInfo = "({MemberName} in {SourceFilePath}:{SourceLineNumber})";

    public IDisposable? BeginScopeWithProperties(params (string Key, object Value)[] properties)
    {
        var propertiesDictionary = properties.ToDictionary(pair => pair.Key, kv => kv.Value);

        return _logger.BeginScope(propertiesDictionary);
    }

    public void Critical(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogCritical($"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Critical(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogCritical($"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Critical(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogCritical(exception, $"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Critical(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogCritical(
            exception,
            $"{message} {callerInfo}",
            [.. args, memberName, sourceFilePath, sourceLineNumber]
        );
    }

    public void Debug(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogDebug($"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Debug(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogDebug($"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Error(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogError($"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Error(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogError($"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Error(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogError(exception, $"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Error(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogError(exception, $"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Info(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogInformation($"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Info(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogInformation($"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Trace(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogTrace($"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Trace(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogTrace($"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Warn(
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogWarning($"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Warn(
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogWarning($"{message} {callerInfo}", [.. args, memberName, sourceFilePath, sourceLineNumber]);
    }

    public void Warn(
        Exception? exception,
        string message,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogWarning(exception, $"{message} {callerInfo}", memberName, sourceFilePath, sourceLineNumber);
    }

    public void Warn(
        Exception? exception,
        string message,
        Span<object?> args,
        ParamsStopType stop = default,
        [CallerMemberName] string memberName = "",
        [CallerFilePath] string sourceFilePath = "",
        [CallerLineNumber] int sourceLineNumber = 0
    )
    {
        _logger.LogWarning(
            exception,
            $"{message} {callerInfo}",
            [.. args, memberName, sourceFilePath, sourceLineNumber]
        );
    }
}
