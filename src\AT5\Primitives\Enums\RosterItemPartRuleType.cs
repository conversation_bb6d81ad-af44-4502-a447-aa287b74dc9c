﻿namespace AT.Primitives.Enums;

public enum RosterItemPartRuleType
{
    Undefined = 0,

    [Obsolete("Just for migration")]
    AllowAllDay = 1,
    ModelSplittable = 2,
    ModelUseBreaks = 3,
    CanBeJustBefore = 4,
    CanBeJustAfter = 5,
    CanBeBefore = 6,
    CanBeAfter = 7,
    CanBeParallelWith = 8,
    CanBeWith = 9,
    CanNotBeJustBefore = 10,
    CanNotBeJustAfter = 11,
    CanNotBeBefore = 12,
    CanNotBeAfter = 13,
    CanNotBeParallelWith = 14,
    CanNotBeWith = 15,
    Duration = 16,
    ActivityMode = 17,
    CanOverwrite = 18,
    Uninterruptible = 19,
    IsSafetyBreak = 20,
    BoundToEmployee = 21,
    PreserveParallelTime = 22,
    ContinuesAbsence = 23,
    MaxConsecutiveDays = 24,
    TimeOffDurations = 25,
    ShiftTemplateBehaviourRule = 26,
    AllowedActivityLevelsRule = 27,
    UserActionsVisibilityRule = 28,
    ParallelPartMultiplicity = 29,
    ChangeRequestRoles = 30,
    ForceValidateAsWorkingTime = 31,
    EditorRule = 32,
    RecurrenceIntervalEvaluationRule = 33,
    AllowChangeProductiveInShiftRule = 34,
    OvertimeTypeRule = 35,
    DurationQuickActionsRule = 36,
}
