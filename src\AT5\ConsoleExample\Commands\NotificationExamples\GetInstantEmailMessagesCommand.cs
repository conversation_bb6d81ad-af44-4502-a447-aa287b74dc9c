namespace AT.ConsoleExample.Commands.NotificationExamples;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate.Specifications;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;

[Command("emailMessages getInstant", Description = "Get all instant email messages")]
public class GetInstantEmailMessagesCommand(IEmailMessageRepository _emailMessageRepository) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        var instantMessagesSpec = new InstantEmailMessagesSpec();
        var emailMessages = await _emailMessageRepository.ListAsync(instantMessagesSpec);

        await console.Output.WriteLineAsync($"Loaded {emailMessages.Count} instant messages");
    }
}
