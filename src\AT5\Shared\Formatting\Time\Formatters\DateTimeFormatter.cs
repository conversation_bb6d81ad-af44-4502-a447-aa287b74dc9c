﻿namespace AT.Shared.Formatting.Time.Formatters;

using System;
using System.Globalization;
using AT.Translations;
using AT.Translations.Formatting.Time;

public sealed class DateTimeFormatter(ITranslator _translator)
{
    public string Format(DateTime value, DateTimeFormattingType type, Language? language = null)
    {
        return type switch
        {
            DateTimeFormattingType.Standard => FormatStandard(value, language),
            DateTimeFormattingType.Short => FormatShort(value, language),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {type} is unknown"),
#else
            _ => FormatStandard(value, language)
#endif
        };
    }

    public string FormatStandard(DateTime value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.DateTime>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }

    public string FormatShort(DateTime value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.DateTimeShort>(language);

        return value.ToString(format.Value, CultureInfo.InvariantCulture);
    }
}
