﻿namespace AT.ConsoleExample.Commands.ComplexTypesExample;

using AT.DataStructures.Time;
using AT.Infrastructure.Database;
using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Domain.Entities;

[Command("ct add-event", Description = "Adds event with specified data")]
public class AddEventCommand(OrganizationDbContext _dbContext) : ICommand
{
    [CommandParameter(0, Description = "The start of the event period")]
    public required DateTime Start { get; set; }

    [CommandParameter(1, Description = "The end of the event period")]
    public required DateTime End { get; set; }

    public async ValueTask ExecuteAsync(IConsole console)
    {
        var eventToAdd = new Event() { Period = new DateTimeInterval(Start, End), };

        await _dbContext.AddAsync(eventToAdd);
        await _dbContext.SaveChangesAsync();
    }
}
