﻿namespace AT.DataStructures.Time;

using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using AT.Utilities.Time.Utils;

/// <summary>
/// Represents an interval with a <see cref="DateOnly"/> start and end.
/// </summary>
[DataContract]
public readonly partial record struct DateInterval
{
    /// <summary>
    /// Gets the start of the interval.
    /// </summary>
    [DataMember]
    public DateOnly Start { get; }

    /// <summary>
    /// Gets the end of the interval.
    /// </summary>
    [DataMember]
    public DateOnly End { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="DateInterval"/> struct with the specified start and end.
    /// </summary>
    /// <param name="start">The start of the interval.</param>
    /// <param name="end">The end of the interval.</param>
    [JsonConstructor]
    public DateInterval(DateOnly start, DateOnly end)
    {
        Start = start;
        End = end;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="DateInterval"/> struct which represents a single day.
    /// </summary>
    /// <param name="day">The day of the interval.</param>
    public DateInterval(DateOnly day)
    {
        Start = day;
        End = day;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="DateInterval"/> struct with an empty interval.
    /// </summary>
    public DateInterval()
    {
        Start = DateOnly.MaxValue;
        End = DateOnly.MinValue;
    }

    /// <summary>
    /// Creates a <see cref="DateInterval"/> representing the current day based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="DateInterval"/> representing today's date.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static DateInterval CreateToday(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        return new DateInterval(DateOnly.FromDateTime(today));
    }

    /// <summary>
    /// Creates a <see cref="DateInterval"/> representing the current week (Monday to Sunday) based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="DateInterval"/> representing the current week.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static DateInterval CreateCurrentWeek(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        var start = today.AddDays(-1 * (today.DayOfWeek - DayOfWeek.Monday));
        var end = start.AddDays(6);
        return new DateInterval(DateOnly.FromDateTime(start), DateOnly.FromDateTime(end));
    }

    /// <summary>
    /// Creates a <see cref="DateInterval"/> representing the current month based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="DateInterval"/> representing the current month.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static DateInterval CreateCurrentMonth(TimeProvider timeProvider)
    {
        var now = timeProvider.GetLocalNow().DateTime;
        var start = new DateTime(now.Year, now.Month, 1);
        var end = start.AddMonths(1).AddDays(-1);
        return new DateInterval(DateOnly.FromDateTime(start), DateOnly.FromDateTime(end));
    }

    /// <summary>
    /// Gets a value indicating whether the interval is empty.
    /// </summary>
    public bool IsEmpty => Start > End;

    /// <summary>
    /// Gets a value indicating whether the interval is not empty.
    /// </summary>
    public bool IsNotEmpty => !IsEmpty;

    /// <summary>
    /// Determines whether the specified <see cref="DateTime"/> is within the interval.
    /// </summary>
    /// <param name="dateTime">The date and time to check.</param>
    /// <returns><c>true</c> if the interval contains the date and time; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTime dateTime)
    {
        var date = DateOnly.FromDateTime(dateTime);
        return Contains(date);
    }

    /// <summary>
    /// Determines whether the day specified by <see cref="DateOnly"/> is within the interval.
    /// </summary>
    /// <param name="date">The date to check.</param>
    /// <returns><c>true</c> if the interval contains the date; otherwise, <c>false</c>.</returns>
    public bool Contains(DateOnly date)
    {
        return (Start <= date) && (End >= date);
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return ((Start <= otherStart)) && ((otherEnd <= End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTimeInterval other)
    {
        var otherStart = other.Start.ToDateOnly();
        var otherEnd = other.End.ToDateOnly();
        return ((Start <= otherStart)) && ((otherEnd <= End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return ((otherStart is not null && Start <= otherStart)) && ((otherEnd is not null && otherEnd <= End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateTimeInterval other)
    {
        var otherStart = other.Start.ToDateOnly();
        var otherEnd = other.End.ToDateOnly();
        return ((otherStart is not null && Start <= otherStart)) && ((otherEnd is not null && otherEnd <= End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateInterval"/> representing the intersection.</returns>
    public DateInterval Intersection(DateInterval other)
    {
        return new DateInterval(DateOnlyUtils.Max(Start, other.Start), DateOnlyUtils.Min(End, other.End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(DateTimeInterval other)
    {
        var thisStart = Start.ToDateTime(TimeOnly.MinValue);
        var thisEnd = End.ToDateTime(TimeOnly.MinValue).AddDays(1);
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateInterval"/> representing the intersection.</returns>
    public DateInterval Intersection(OpenDateInterval other)
    {
        return new DateInterval(DateOnlyUtils.Max(Start, other.Start), DateOnlyUtils.Min(End, other.End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(OpenDateTimeInterval other)
    {
        var thisStart = Start.ToDateTime(TimeOnly.MinValue);
        var thisEnd = End.ToDateTime(TimeOnly.MinValue).AddDays(1);
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }
}

/// <summary>
/// Represents an interval with a <see cref="DateTime"/> start and end.
/// </summary>
[DataContract]
public readonly partial record struct DateTimeInterval
{
    /// <summary>
    /// Gets the start of the interval.
    /// </summary>
    [DataMember]
    public DateTime Start { get; }

    /// <summary>
    /// Gets the end of the interval.
    /// </summary>
    [DataMember]
    public DateTime End { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="DateTimeInterval"/> struct with the specified start and end.
    /// </summary>
    /// <param name="start">The start of the interval.</param>
    /// <param name="end">The end of the interval.</param>
    [JsonConstructor]
    public DateTimeInterval(DateTime start, DateTime end)
    {
        Start = start;
        End = end;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="DateTimeInterval"/> struct which represents a single day.
    /// </summary>
    /// <param name="day">The day of the interval.</param>
    public DateTimeInterval(DateOnly day)
    {
        Start = day.ToDateTime(TimeOnly.MinValue);
        End = day.ToDateTime(TimeOnly.MaxValue).AddTicks(1);
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="DateTimeInterval"/> struct with an empty interval.
    /// </summary>
    public DateTimeInterval()
    {
        Start = DateTime.MaxValue;
        End = DateTime.MinValue;
    }

    /// <summary>
    /// Creates a <see cref="DateTimeInterval"/> representing the current day based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="DateTimeInterval"/> representing today's date.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static DateTimeInterval CreateToday(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        return new DateTimeInterval(DateOnly.FromDateTime(today));
    }

    /// <summary>
    /// Creates a <see cref="DateTimeInterval"/> representing the current week (Monday to Sunday) based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="DateTimeInterval"/> representing the current week.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static DateTimeInterval CreateCurrentWeek(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        var start = today.AddDays(-1 * (today.DayOfWeek - DayOfWeek.Monday));
        var end = start.AddDays(6);
        return new DateTimeInterval(start, end.AddDays(1));
    }

    /// <summary>
    /// Creates a <see cref="DateTimeInterval"/> representing the current month based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="DateTimeInterval"/> representing the current month.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static DateTimeInterval CreateCurrentMonth(TimeProvider timeProvider)
    {
        var now = timeProvider.GetLocalNow().DateTime;
        var start = new DateTime(now.Year, now.Month, 1);
        var end = start.AddMonths(1).AddDays(-1);
        return new DateTimeInterval(start, end.AddDays(1));
    }

    /// <summary>
    /// Gets a value indicating whether the interval is empty.
    /// </summary>
    public bool IsEmpty => Start >= End;

    /// <summary>
    /// Gets a value indicating whether the interval is not empty.
    /// </summary>
    public bool IsNotEmpty => !IsEmpty;

    /// <summary>
    /// Determines whether the specified <see cref="DateTime"/> is within the interval.
    /// </summary>
    /// <param name="dateTime">The date and time to check.</param>
    /// <returns><c>true</c> if the interval contains the date and time; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTime dateTime)
    {
        return (Start <= dateTime) && (End > dateTime);
    }

    /// <summary>
    /// Determines whether the day specified by <see cref="DateOnly"/> is within the interval.
    /// </summary>
    /// <param name="date">The date to check.</param>
    /// <returns><c>true</c> if the interval contains the date; otherwise, <c>false</c>.</returns>
    public bool Contains(DateOnly date)
    {
        var dateInterval = new DateInterval(date);
        return Contains(dateInterval);
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateInterval other)
    {
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue).AddDays(1);
        return ((Start <= otherStart)) && ((otherEnd < End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTimeInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return ((Start <= otherStart)) && ((otherEnd < End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateInterval other)
    {
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue)?.AddDays(1);
        return ((otherStart is not null && Start <= otherStart)) && ((otherEnd is not null && otherEnd < End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateTimeInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return ((otherStart is not null && Start <= otherStart)) && ((otherEnd is not null && otherEnd < End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(DateInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue).AddDays(1);
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(DateTimeInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(OpenDateInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue)?.AddDays(1);
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(OpenDateTimeInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }
}

/// <summary>
/// Represents an interval with a <see cref="DateOnly?"/> start and end. Null represents infinity.
/// </summary>
[DataContract]
public readonly partial record struct OpenDateInterval
{
    /// <summary>
    /// Gets the start of the interval. Null represents infinity.
    /// </summary>
    [DataMember]
    public DateOnly? Start { get; }

    /// <summary>
    /// Gets the end of the interval. Null represents infinity.
    /// </summary>
    [DataMember]
    public DateOnly? End { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="OpenDateInterval"/> struct with the specified start and end.
    /// </summary>
    /// <param name="start">The start of the interval.</param>
    /// <param name="end">The end of the interval.</param>
    [JsonConstructor]
    public OpenDateInterval(DateOnly? start, DateOnly? end)
    {
        Start = start;
        End = end;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="OpenDateInterval"/> struct which represents a single day.
    /// </summary>
    /// <param name="day">The day of the interval.</param>
    public OpenDateInterval(DateOnly day)
    {
        Start = day;
        End = day;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="OpenDateInterval"/> struct with an empty interval.
    /// </summary>
    public OpenDateInterval()
    {
        Start = DateOnly.MaxValue;
        End = DateOnly.MinValue;
    }

    /// <summary>
    /// Creates a <see cref="OpenDateInterval"/> representing the current day based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="OpenDateInterval"/> representing today's date.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static OpenDateInterval CreateToday(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        return new OpenDateInterval(DateOnly.FromDateTime(today));
    }

    /// <summary>
    /// Creates a <see cref="OpenDateInterval"/> representing the current week (Monday to Sunday) based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="OpenDateInterval"/> representing the current week.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static OpenDateInterval CreateCurrentWeek(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        var start = today.AddDays(-1 * (today.DayOfWeek - DayOfWeek.Monday));
        var end = start.AddDays(6);
        return new OpenDateInterval(DateOnly.FromDateTime(start), DateOnly.FromDateTime(end));
    }

    /// <summary>
    /// Creates a <see cref="OpenDateInterval"/> representing the current month based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="OpenDateInterval"/> representing the current month.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static OpenDateInterval CreateCurrentMonth(TimeProvider timeProvider)
    {
        var now = timeProvider.GetLocalNow().DateTime;
        var start = new DateTime(now.Year, now.Month, 1);
        var end = start.AddMonths(1).AddDays(-1);
        return new OpenDateInterval(DateOnly.FromDateTime(start), DateOnly.FromDateTime(end));
    }

    /// <summary>
    /// Gets a value indicating whether the interval is empty.
    public bool IsEmpty => Start is not null && End is not null && Start > End;

    /// <summary>
    /// Gets a value indicating whether the interval is not empty.
    /// </summary>
    public bool IsNotEmpty => !IsEmpty;

    /// <summary>
    /// Determines whether the specified <see cref="DateTime"/> is within the interval.
    /// </summary>
    /// <param name="dateTime">The date and time to check.</param>
    /// <returns><c>true</c> if the interval contains the date and time; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTime dateTime)
    {
        var date = DateOnly.FromDateTime(dateTime);
        return Contains(date);
    }

    /// <summary>
    /// Determines whether the day specified by <see cref="DateOnly"/> is within the interval.
    /// </summary>
    /// <param name="date">The date to check.</param>
    /// <returns><c>true</c> if the interval contains the date; otherwise, <c>false</c>.</returns>
    public bool Contains(DateOnly date)
    {
        return (Start is null || Start <= date) && (End is null || End >= date);
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return (Start is null || (Start <= otherStart)) && (End is null || (otherEnd <= End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTimeInterval other)
    {
        var otherStart = other.Start.ToDateOnly();
        var otherEnd = other.End.ToDateOnly();
        return (Start is null || (Start <= otherStart)) && (End is null || (otherEnd <= End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return (Start is null || (otherStart is not null && Start <= otherStart))
            && (End is null || (otherEnd is not null && otherEnd <= End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateTimeInterval other)
    {
        var otherStart = other.Start.ToDateOnly();
        var otherEnd = other.End.ToDateOnly();
        return (Start is null || (otherStart is not null && Start <= otherStart))
            && (End is null || (otherEnd is not null && otherEnd <= End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateInterval"/> representing the intersection.</returns>
    public DateInterval Intersection(DateInterval other)
    {
        return new DateInterval(DateOnlyUtils.Max(Start, other.Start), DateOnlyUtils.Min(End, other.End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(DateTimeInterval other)
    {
        var thisStart = Start.ToDateTime(TimeOnly.MinValue);
        var thisEnd = End.ToDateTime(TimeOnly.MinValue)?.AddDays(1);
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="OpenDateInterval"/> representing the intersection.</returns>
    public OpenDateInterval Intersection(OpenDateInterval other)
    {
        return new OpenDateInterval(DateOnlyUtils.Max(Start, other.Start), DateOnlyUtils.Min(End, other.End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="OpenDateTimeInterval"/> representing the intersection.</returns>
    public OpenDateTimeInterval Intersection(OpenDateTimeInterval other)
    {
        var thisStart = Start.ToDateTime(TimeOnly.MinValue);
        var thisEnd = End.ToDateTime(TimeOnly.MinValue)?.AddDays(1);
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new OpenDateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }
}

/// <summary>
/// Represents an interval with a <see cref="DateTime?"/> start and end. Null represents infinity.
/// </summary>
[DataContract]
public readonly partial record struct OpenDateTimeInterval
{
    /// <summary>
    /// Gets the start of the interval. Null represents infinity.
    /// </summary>
    [DataMember]
    public DateTime? Start { get; }

    /// <summary>
    /// Gets the end of the interval. Null represents infinity.
    /// </summary>
    [DataMember]
    public DateTime? End { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="OpenDateTimeInterval"/> struct with the specified start and end.
    /// </summary>
    /// <param name="start">The start of the interval.</param>
    /// <param name="end">The end of the interval.</param>
    [JsonConstructor]
    public OpenDateTimeInterval(DateTime? start, DateTime? end)
    {
        Start = start;
        End = end;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="OpenDateTimeInterval"/> struct which represents a single day.
    /// </summary>
    /// <param name="day">The day of the interval.</param>
    public OpenDateTimeInterval(DateOnly day)
    {
        Start = day.ToDateTime(TimeOnly.MinValue);
        End = day.ToDateTime(TimeOnly.MaxValue).AddTicks(1);
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="OpenDateTimeInterval"/> struct with an empty interval.
    /// </summary>
    public OpenDateTimeInterval()
    {
        Start = DateTime.MaxValue;
        End = DateTime.MinValue;
    }

    /// <summary>
    /// Creates a <see cref="OpenDateTimeInterval"/> representing the current day based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="OpenDateTimeInterval"/> representing today's date.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static OpenDateTimeInterval CreateToday(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        return new OpenDateTimeInterval(DateOnly.FromDateTime(today));
    }

    /// <summary>
    /// Creates a <see cref="OpenDateTimeInterval"/> representing the current week (Monday to Sunday) based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="OpenDateTimeInterval"/> representing the current week.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static OpenDateTimeInterval CreateCurrentWeek(TimeProvider timeProvider)
    {
        var today = timeProvider.GetLocalNow().DateTime.Date;
        var start = today.AddDays(-1 * (today.DayOfWeek - DayOfWeek.Monday));
        var end = start.AddDays(6);
        return new OpenDateTimeInterval(start, end.AddDays(1));
    }

    /// <summary>
    /// Creates a <see cref="OpenDateTimeInterval"/> representing the current month based on the provided <see cref="TimeProvider"/>.
    /// </summary>
    /// <param name="timeProvider">The time provider used to determine the current date.</param>
    /// <returns>A <see cref="OpenDateTimeInterval"/> representing the current month.</returns>
    /// <remarks>This should be moved to a service.</remarks>
    public static OpenDateTimeInterval CreateCurrentMonth(TimeProvider timeProvider)
    {
        var now = timeProvider.GetLocalNow().DateTime;
        var start = new DateTime(now.Year, now.Month, 1);
        var end = start.AddMonths(1).AddDays(-1);
        return new OpenDateTimeInterval(start, end.AddDays(1));
    }

    /// <summary>
    /// Gets a value indicating whether the interval is empty.
    /// </summary>
    public bool IsEmpty => Start is not null && End is not null && Start >= End;

    /// <summary>
    /// Gets a value indicating whether the interval is not empty.
    /// </summary>
    public bool IsNotEmpty => !IsEmpty;

    /// <summary>
    /// Determines whether the specified <see cref="DateTime"/> is within the interval.
    /// </summary>
    /// <param name="dateTime">The date and time to check.</param>
    /// <returns><c>true</c> if the interval contains the date and time; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTime dateTime)
    {
        return (Start is null || Start <= dateTime) && (End is null || End > dateTime);
    }

    /// <summary>
    /// Determines whether the day specified by <see cref="DateOnly"/> is within the interval.
    /// </summary>
    /// <param name="date">The date to check.</param>
    /// <returns><c>true</c> if the interval contains the date; otherwise, <c>false</c>.</returns>
    public bool Contains(DateOnly date)
    {
        var dateInterval = new DateInterval(date);
        return Contains(dateInterval);
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateInterval other)
    {
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue).AddDays(1);
        return (Start is null || (Start <= otherStart)) && (End is null || (otherEnd < End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="DateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(DateTimeInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return (Start is null || (Start <= otherStart)) && (End is null || (otherEnd < End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateInterval other)
    {
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue)?.AddDays(1);
        return (Start is null || (otherStart is not null && Start <= otherStart))
            && (End is null || (otherEnd is not null && otherEnd < End));
    }

    /// <summary>
    /// Determines whether the specified <see cref="OpenDateTimeInterval"/> is within the interval.
    /// </summary>
    /// <param name="other">The interval to check.</param>
    /// <returns><c>true</c> if this interval contains the given interval; otherwise, <c>false</c>.</returns>
    public bool Contains(OpenDateTimeInterval other)
    {
        var otherStart = other.Start;
        var otherEnd = other.End;
        return (Start is null || (otherStart is not null && Start <= otherStart))
            && (End is null || (otherEnd is not null && otherEnd < End));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(DateInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue).AddDays(1);
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="DateTimeInterval"/> representing the intersection.</returns>
    public DateTimeInterval Intersection(DateTimeInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new DateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="OpenDateTimeInterval"/> representing the intersection.</returns>
    public OpenDateTimeInterval Intersection(OpenDateInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start.ToDateTime(TimeOnly.MinValue);
        var otherEnd = other.End.ToDateTime(TimeOnly.MinValue)?.AddDays(1);
        return new OpenDateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Computes the intersection of this interval with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to intersect with.</param>
    /// <returns>A new <see cref="OpenDateTimeInterval"/> representing the intersection.</returns>
    public OpenDateTimeInterval Intersection(OpenDateTimeInterval other)
    {
        var thisStart = Start;
        var thisEnd = End;
        var otherStart = other.Start;
        var otherEnd = other.End;
        return new OpenDateTimeInterval(DateTimeUtils.Max(thisStart, otherStart), DateTimeUtils.Min(thisEnd, otherEnd));
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="DateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(DateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }

    /// <summary>
    /// Determines whether this interval overlaps with another <see cref="OpenDateTimeInterval"/>.
    /// </summary>
    /// <param name="other">The other interval to check overlap with.</param>
    /// <returns><c>true</c> if the intervals overlap; otherwise, <c>false</c>.</returns>
    public bool Overlaps(OpenDateTimeInterval other)
    {
        return Intersection(other).IsNotEmpty;
    }
}
