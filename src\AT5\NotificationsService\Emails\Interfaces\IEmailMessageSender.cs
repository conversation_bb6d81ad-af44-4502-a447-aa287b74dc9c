﻿namespace AT.NotificationsService.Emails.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.NotificationsService.General;

public interface IEmailMessageSender
{
    /// <param name="recipients">If <see langword="null"/>, uses recipients from <paramref name="emailMessage"/>.</param>
    Task<OperationResult> SendEmailMessageAsync(
        EmailMessage emailMessage,
        IEnumerable<EmailMessageRecipient>? recipients = null,
        CancellationToken cancellationToken = default
    );
}
