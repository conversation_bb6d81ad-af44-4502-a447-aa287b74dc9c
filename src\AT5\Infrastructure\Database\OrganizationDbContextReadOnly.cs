﻿namespace AT.Infrastructure.Database;

using System;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Organization DbContext for data reading only. Whoever uses this DbContext cannot make any changes.
/// </summary>
public class OrganizationDbContextReadOnly : OrganizationDbContext
{
    public OrganizationDbContextReadOnly(DbContextOptions<OrganizationDbContext> options)
        : base(options)
    {
        base.SavingChanges += OnBeforeSavingChanges;
    }

    private static void OnBeforeSavingChanges(object? sender, SavingChangesEventArgs e)
    {
        throw new InvalidOperationException(nameof(OrganizationDbContextReadOnly) + " forbids saving changes.");
    }
}
