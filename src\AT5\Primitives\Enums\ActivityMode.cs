﻿namespace AT.Primitives.Enums;

/// <summary>
/// Whether a given RosterItemPartType can be used as a type for parallel parts of a RosterItem/>.
/// </summary>
public enum ActivityMode
{
    /// <summary>
    /// Can be used on normal (non-parallel) parts only.
    /// </summary>
    Normal = 0,

    /// <summary>
    /// Cannot be used as type of both normal and parallel parts.
    /// </summary>
    NormalOrParallel = 1,

    /// <summary>
    /// Can be used on parallel parts only.
    /// </summary>
    ParallelOnly = 2,

    Label = 3,

    Overtime = 4,

    Emergency = 5,
}

public static class ActivityModeExtensions
{
    public static bool IsAllowedOnParallelParts(this ActivityMode activityMode)
    {
        return activityMode is ActivityMode.NormalOrParallel or ActivityMode.ParallelOnly;
    }
}
