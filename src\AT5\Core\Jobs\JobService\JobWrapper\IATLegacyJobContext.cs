﻿namespace AT.Core.Jobs.JobService.JobWrapper;

// These parameters should only be used in legacy jobs. Remove when possible.
public interface IATLegacyJobContext
{
    JobId JobId { get; }

    string JobName { get; }

    string JobKey { get; }

    string TriggerKey { get; }

    // Consider if this parameter should be in non-legacy job context.
    string? AristoTelosWebUrl { get; }

    IFireTimeParameters FireTimeParameters { get; }
}
