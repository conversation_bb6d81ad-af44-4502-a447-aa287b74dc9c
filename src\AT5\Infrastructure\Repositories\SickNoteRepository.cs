﻿namespace AT.Infrastructure.Repositories;

using AT.Core.Domain.SickNotesAggregate;
using AT.Infrastructure.DataAccess;
using AT.Infrastructure.Database;

public class SickNoteRepository(OrganizationDbContext _dbContext)
    : EfRepository<SickNote>(_dbContext),
        ISickNoteRepository,
        IRepositoryWithFactoryMethod<ISickNoteRepository>
{
    public static new ISickNoteRepository Create(OrganizationDbContext dbContext)
    {
        return new SickNoteRepository(dbContext);
    }
}
