﻿namespace AT.Translations;

/// <summary>
/// Marker interface for translations. The name of the specific record/class
/// that implements this interface is the translation's key in the resx file.
/// Individual translations should never implement this interface. Instead,
/// they should implement either <see cref="ITranslationWithParameters"/>
/// or <see cref="ITranslationNoParameters"/>.
/// </summary>
public interface ITranslation
{ }