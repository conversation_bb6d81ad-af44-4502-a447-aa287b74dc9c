﻿namespace AT.DataStructures.Time;

using System.Runtime.Serialization;
using AT.Primitives.Enums;

/// <summary>
/// Represents a recurrence pattern with configurable start and end dates, weekdays, holidays, and custom exceptions and inclusions.
/// </summary>
[DataContract]
public record Recurrence
{
    /// <summary>
    /// Gets the start date and time of the recurrence pattern.
    /// </summary>
    [DataMember]
    public DateTime? Start { get; }

    /// <summary>
    /// Gets the end date and time of the recurrence pattern.
    /// </summary>
    [DataMember]
    public DateTime? End { get; }

    /// <summary>
    /// Gets the weekdays included in the recurrence pattern.
    /// </summary>
    [DataMember]
    public Weekdays Weekdays { get; }

    /// <summary>
    /// Gets a value indicating whether working holidays are excluded from the recurrence.
    /// </summary>
    [DataMember]
    public bool ExcludeWorkingHolidays { get; }

    /// <summary>
    /// Gets a value indicating whether non-working holidays are excluded from the recurrence.
    /// </summary>
    [DataMember]
    public bool ExcludeNotWorkingHolidays { get; }

    /// <summary>
    /// Gets a value indicating whether working holidays are included in the recurrence.
    /// </summary>
    [DataMember]
    public bool IncludeWorkingHolidays { get; }

    /// <summary>
    /// Gets a value indicating whether non-working holidays are included in the recurrence.
    /// </summary>
    [DataMember]
    public bool IncludeNotWorkingHolidays { get; }

    /// <summary>
    /// Gets a comma-separated string of exception dates that are excluded from the recurrence.
    /// </summary>
    [DataMember]
    public string Exceptions { get; }

    /// <summary>
    /// Gets a comma-separated string of inclusion dates that are explicitly included in the recurrence.
    /// </summary>
    [DataMember]
    public string Inclusions { get; }

    /// <summary>
    /// Initializes a new instance of the <see cref="Recurrence"/> class with default values.
    /// </summary>
    public Recurrence()
        : this(
            default(DateTime?),
            default(DateTime?),
            Weekdays.None,
            default(bool),
            default(bool),
            default(bool),
            default(bool),
            string.Empty,
            string.Empty
        ) { }

    /// <summary>
    /// Initializes a new instance of the <see cref="Recurrence"/> class with the specified parameters.
    /// </summary>
    /// <param name="start">The start date and time of the recurrence.</param>
    /// <param name="end">The end date and time of the recurrence.</param>
    /// <param name="weekdays">The weekdays included in the recurrence.</param>
    /// <param name="excludeWorkingHolidays">A value indicating whether working holidays are excluded.</param>
    /// <param name="excludeNotWorkingHolidays">A value indicating whether non-working holidays are excluded.</param>
    /// <param name="includeWorkingHolidays">A value indicating whether working holidays are included.</param>
    /// <param name="includeNotWorkingHolidays">A value indicating whether non-working holidays are included.</param>
    /// <param name="exceptions">A comma-separated string of exception dates.</param>
    /// <param name="inclusions">A comma-separated string of inclusion dates.</param>
    public Recurrence(
        DateTime? start,
        DateTime? end,
        Weekdays weekdays,
        bool excludeWorkingHolidays,
        bool excludeNotWorkingHolidays,
        bool includeWorkingHolidays,
        bool includeNotWorkingHolidays,
        string exceptions,
        string inclusions
    )
    {
        Start = start;
        End = end;
        Weekdays = weekdays;
        ExcludeWorkingHolidays = excludeWorkingHolidays;
        ExcludeNotWorkingHolidays = excludeNotWorkingHolidays;
        IncludeWorkingHolidays = includeWorkingHolidays;
        IncludeNotWorkingHolidays = includeNotWorkingHolidays;
        Exceptions = exceptions;
        Inclusions = inclusions;
    }
}
