﻿namespace AT.Infrastructure.Jobs.QuatzWrappers;

using AT.Utilities.Logging;
using AT.Utilities.Task;
using Quartz;

public class QuartzSchedulerProvider(ILogger<QuartzSchedulerProvider> _logger, ISchedulerFactory _schedulerFactory)
    : IQuartzSchedulerProvider
{
    private readonly AsyncLock _asyncLock = new();

    public async Task<IScheduler> GetStartedScheduler(CancellationToken ct)
    {
        var scheduler = await _schedulerFactory.GetScheduler(ct);
        await MakeSureSchedulerIsStarted(scheduler, ct);
        return scheduler;
    }

    private async Task MakeSureSchedulerIsStarted(IScheduler quartzScheduler, CancellationToken ct)
    {
        if (quartzScheduler.IsStarted)
        {
            return;
        }

        // Not sure if IScheduler.Start is thread safe, so locking around it.
        _logger.Info("Quartz scheduler not started yet, starting...");
        using (await _asyncLock.EnterScope(ct))
        {
            if (!quartzScheduler.IsStarted)
            {
                await quartzScheduler.Start(ct);
            }
        }
    }
}
