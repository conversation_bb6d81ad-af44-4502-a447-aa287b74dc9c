﻿namespace AT.Infrastructure.DataAccess;

using AT.Core.DataAccess;
using AT.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

public class EfRepositoryFactory<TRepo, TRepoImpl>(IDbContextFactory<OrganizationDbContext> _dbContextFactory)
    : IRepositoryFactory<TRepo>
    where TRepo : class, IDisposable
    where TRepoImpl : class, TRepo, IRepositoryWithFactoryMethod<TRepo>
{
    public TRepo Create()
    {
        return TRepoImpl.Create(_dbContextFactory.CreateDbContext());
    }
}
