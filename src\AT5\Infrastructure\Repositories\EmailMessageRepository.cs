﻿namespace AT.Infrastructure.Repositories;

using Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using DataAccess;
using Database;

public sealed class EmailMessageRepository(OrganizationDbContext _dbContext)
    : EfRepository<EmailMessage>(_dbContext),
        IEmailMessageRepository,
        IRepositoryWithFactoryMethod<IEmailMessageRepository>
{
    public static new IEmailMessageRepository Create(OrganizationDbContext dbContext)
    {
        return new EmailMessageRepository(dbContext);
    }

    public override void AddForDelete(EmailMessage emailMessage)
    {
        // FUTURE: cascading delete on EmailMessageRecipients?
        foreach (var recipient in emailMessage.EmailMessageRecipients)
        {
            DbContext.Remove(recipient);
        }

        DbContext.Remove(emailMessage);
    }
}
