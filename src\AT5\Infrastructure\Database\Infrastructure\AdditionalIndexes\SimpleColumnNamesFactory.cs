﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal sealed class SimpleColumnNamesFactory<T>(IEnumerable<string> _columnNames) : IColumnNamesFactory<T>
    where T : class
{
    public IEnumerable<string> MakeColumnNames(EntityTypeBuilder<T> entityTypeBuilder)
    {
        return _columnNames;
    }
}
