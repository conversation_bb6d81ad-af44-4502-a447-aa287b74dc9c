﻿namespace AT.Infrastructure.Utilities.AzureMonitor;

using System.Diagnostics;
using OpenTelemetry;
using OpenTelemetry.Trace;

public class AristotelosSampler(double _samplingRatio) : Sampler
{
    private readonly Sampler _innerSampler = new TraceIdRatioBasedSampler(_samplingRatio);

    public override SamplingResult ShouldSample(in SamplingParameters samplingParameters)
    {
        var parentContext = samplingParameters.ParentContext;

        if (parentContext.IsValid() && parentContext.TraceFlags.HasFlag(ActivityTraceFlags.Recorded))
        {
            return new SamplingResult(SamplingDecision.RecordAndSample);
        }

        return _innerSampler.ShouldSample(samplingParameters);
    }
}
