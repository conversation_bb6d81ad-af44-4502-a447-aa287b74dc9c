﻿namespace AT.Core.MasterDomain;

public sealed record OrganizationInfo(
    OrganizationId Id,
    string Name,
    string UrlId,
    string DatabaseName,
    string HeaderTitle,
    string? HeaderColor
)
{
    public const string NoOrgName = "__NO_ORG__";
    public const string NoOrgDbName = "__NO_ORG_DB__";

    public static readonly OrganizationInfo DefaultTenantOrg =
        new(
            Id: OrganizationId.From(-1),
            Name: NoOrgName,
            UrlId: NoOrgName,
            DatabaseName: NoOrgDbName, // Never use this as a DB name.
            HeaderTitle: NoOrgName,
            HeaderColor: null
        );
}
