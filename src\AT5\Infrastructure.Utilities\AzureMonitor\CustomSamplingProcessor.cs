﻿namespace AT.Infrastructure.Utilities.AzureMonitor;

using System.Diagnostics;
using AT.Utilities.Logging;
using OpenTelemetry;

// A custom procesor is necessary to ensure that requests are sent when an exception occurs; otherwise, there is no guarantee that the request associated with the exception won't be discarded
// https://github.com/Azure/azure-sdk-for-net/issues/43985
public class CustomSamplingProcessor : BaseProcessor<Activity>
{
    public override void OnEnd(Activity activity)
    {
        bool alwaysInclude = StructuredLoggingUtils.IsActivityMarkedAsAlwaysInclude(activity);

        if (alwaysInclude)
        {
            activity.IsAllDataRequested = true;
            activity.ActivityTraceFlags |= ActivityTraceFlags.Recorded;
        }
    }
}
