﻿namespace AT.Core.Domain.Entities;

public class LicenceRule
{
    public LicenceRuleId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public LicenceId LicenceId { get; set; }

    public Filter2Id? FilterId { get; set; }

    public Validity Validity { get; set; }

    public int Priority { get; set; }

    public bool ValidContractOnly { get; set; }

    public bool IsGlobal { get; set; }

    public virtual Filter2? Filter { get; set; }

    public virtual Licence Licence { get; set; } = null!;

    public virtual ICollection<Role> Roles { get; set; } = new List<Role>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
