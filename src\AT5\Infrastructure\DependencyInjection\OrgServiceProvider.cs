﻿namespace AT.Infrastructure.DependencyInjection;

using Autofac;

internal sealed class OrgServiceProvider(ILifetimeScope _orgLifetimeScope) : IOrgServiceProvider
{
    public TService Resolve<TService>()
        where TService : class
    {
        return _orgLifetimeScope.Resolve<TService>();
    }

    public IOrgServiceProviderScope BeginScope()
    {
        return new OrgServiceProviderScope(_orgLifetimeScope.BeginLifetimeScope());
    }
}
