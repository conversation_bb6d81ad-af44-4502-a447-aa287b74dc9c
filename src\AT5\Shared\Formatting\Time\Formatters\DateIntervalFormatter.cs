﻿namespace AT.Shared.Formatting.Time.Formatters;

using System;
using System.Globalization;
using AT.DataStructures.Time;
using AT.Translations;
using AT.Translations.Formatting.Time;
using SmartFormat;

public sealed class DateIntervalFormatter(ITranslator _translator)
{
    public string Format(DateInterval value, DateIntervalFormattingType type, Language? language = null)
    {
        return type switch
        {
            DateIntervalFormattingType.Standard => FormatStandard(value, language),
            DateIntervalFormattingType.Short => FormatShort(value, language),

#if DEBUG
            _ => throw new ArgumentOutOfRangeException($"Value {type} is unknown"),
#else
            _ => FormatStandard(value, language)
#endif
        };
    }

    public string FormatStandard(DateInterval value, Language? language = null)
    {
        var format = _translator.Translate<TimeFormatTranslations.Date>(language);

        string start = value.Start.ToString(format.Value, CultureInfo.InvariantCulture);
        string end = value.End.ToString(format.Value, CultureInfo.InvariantCulture);

        return Smart.Format(CultureInfo.InvariantCulture, "{start} - {end}", start, end);
    }

    public string FormatShort(DateInterval value, Language? language = null)
    {
        if (value.Start == value.End)
        {
            var format = _translator.Translate<TimeFormatTranslations.Date>(language);

            return Smart.Format(CultureInfo.InvariantCulture, format.Value, value.Start);
        }

        return FormatStandard(value, language);
    }
}
