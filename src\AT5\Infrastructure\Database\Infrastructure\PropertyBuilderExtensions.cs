﻿namespace AT.Infrastructure.Database.Infrastructure;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal static class PropertyBuilderExtensions
{
    private const string dateTimeColumnType = "datetime";

    /// <summary>
    /// Configures the column order for a property using an <see cref="Iota"/> instance.
    /// <para>
    /// If <paramref name="iota"/> is <c>null</c>, this method does nothing and returns the original builder.
    /// Otherwise, it retrieves the next column order value from the <paramref name="iota"/> instance and applies it to the property.
    /// </para>
    /// </summary>
    /// <typeparam name="T">The type of the property being configured.</typeparam>
    /// <param name="propertyBuilder">The <see cref="PropertyBuilder{T}"/> instance to configure.</param>
    /// <param name="iota">An <see cref="Iota"/> instance used to determine the column order, or <c>null</c> to skip configuration.</param>
    /// <returns>The configured <see cref="PropertyBuilder{T}"/> instance.</returns>
    public static PropertyBuilder<T> HasColumnOrder<T>(this PropertyBuilder<T> propertyBuilder, Iota? iota)
    {
        if (iota is null)
        {
            return propertyBuilder;
        }

        int columnOrder = iota.Next();
        propertyBuilder.HasColumnOrder(columnOrder);
        return propertyBuilder;
    }

    public static PropertyBuilder<DateOnly> IsStoredAsDateTime(this PropertyBuilder<DateOnly> propertyBuilder)
    {
        return propertyBuilder
            .HasColumnType(dateTimeColumnType)
            .HasConversion(
                dateOnly => dateOnly.ToDateTime(TimeOnly.MinValue),
                dateTime => DateOnly.FromDateTime(dateTime)
            );
    }

    public static PropertyBuilder<DateOnly?> IsStoredAsDateTime(this PropertyBuilder<DateOnly?> propertyBuilder)
    {
        return propertyBuilder
            .HasColumnType(dateTimeColumnType)
            .HasConversion(
                dateOnly => dateOnly.HasValue ? dateOnly.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null,
                dateTime => dateTime.HasValue ? DateOnly.FromDateTime(dateTime.Value) : (DateOnly?)null
            );
    }

    public static PropertyBuilder<DateTime> IsStoredAsDateTime(this PropertyBuilder<DateTime> propertyBuilder)
    {
        return propertyBuilder.HasColumnType(dateTimeColumnType);
    }

    public static PropertyBuilder<DateTime?> IsStoredAsDateTime(this PropertyBuilder<DateTime?> propertyBuilder)
    {
        return propertyBuilder.HasColumnType(dateTimeColumnType);
    }
}
