var builder = WebApplication.CreateBuilder(args).AddServiceDefaults(); // Add service defaults & Aspire client integrations.

//services.AddRazorComponents()
//    .AddInteractiveWebAssemblyComponents()
//    .AddRazorPages();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// Map endpoints for health checking and similar.
//app.MapRazorPages();
//app.UseHttpsRedirection();

//app.UseRouting();
//app.UseAntiforgery();

// Use this instead of `UseStaticFiles` because:
// Map Static Assets is optimized for serving the assets from known locations in the app at build and publish time. If the app serves assets
// from other locations, such as disk or embedded resources, UseStaticFiles should be used.
// Source: https://learn.microsoft.com/en-us/aspnet/core/blazor/fundamentals/static-files?view=aspnetcore-9.0#static-asset-delivery-in-server-side-blazor-apps
app.MapStaticAssets();

app.MapFallbackToFile("index.html");

//app.MapRazorComponents<AT.Web.Client.App>()
//    .AddInteractiveWebAssemblyRenderMode()
//    .AddAdditionalAssemblies(typeof(AT.Web.Client._Imports).Assembly);

await app.RunAsync();
