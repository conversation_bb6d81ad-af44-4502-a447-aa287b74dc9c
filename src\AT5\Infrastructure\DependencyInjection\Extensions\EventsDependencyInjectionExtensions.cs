﻿namespace AT.Infrastructure.DependencyInjection.Extensions;

using AT.Infrastructure.Events;
using AT.Infrastructure.Events.Subscribers;
using Autofac;
using Autofac.Multitenant;

public static class EventsDependencyInjectionExtensions
{
    public static ContainerBuilder AddEventsServices(this ContainerBuilder containerBuilder)
    {
        containerBuilder
            .RegisterType<EventsService>()
            .As<IEventsPublisher>()
            .As<IEventsSubscriber>()
            .InstancePerTenant();
        containerBuilder
            .RegisterType<DataChangeSubscribersService>()
            .As<IDataChangeSubscribersService>()
            .InstancePerTenant();
        containerBuilder
            .RegisterType<DataChangeEventsService>()
            .As<IDataChangeEventsPublisher>()
            .As<IDataChangeEventsSubscriber>();

        return containerBuilder;
    }
}
