﻿namespace AT.NotificationsService.PushNotifications.Tokens;

using System.Collections.Concurrent;
using System.Threading;
using AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;
using AT.Core.Domain.PushNotificationTokenAggregate;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Primitives.Enums;
using AT.Utilities.Logging;
using Microsoft.Extensions.Options;

public class PushNotificationTokensCleanupService(
    IPushNotificationSender _pushNotificationSendingService,
    IPushNotificationTokenUpdateService _pushNotificationTokenService,
    IOptionsMonitor<PushNotificationConfig> _pushNotificationConfigMonitor,
    ILogger<PushNotificationTokensCleanupService> _logger
) : IPushNotificationTokensCleanupService
{
    private readonly BlockingCollection<UserPnToken> _possiblyInvalidTokensQueue = [];

    private readonly PushNotification _dummyNotification =
        new()
        {
            Title = "Test title",
            Body = "Test body",
            Type = PushNotificationType.BreakStart,
            Generated = DateTime.Now,
        };

    public async Task RunAsync(CancellationToken cancellationToken)
    {
        _logger.Info("Starting push notification tokens cleanup service...");

        while (!_possiblyInvalidTokensQueue.IsCompleted && !cancellationToken.IsCancellationRequested)
        {
            try
            {
                await RunSingleCleanupCycle(cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.Info("Cancellation requested. Terminating...");
                return;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "An exception occurred");

                try
                {
                    var config = _pushNotificationConfigMonitor.CurrentValue.TokensCleanupConfig;
                    await Task.Delay(config.PauseAfterException, cancellationToken);
                    _logger.Info("Wait-time after an exception completed.");
                }
                catch (TaskCanceledException)
                {
                    _logger.Info("Cancellation requested while waiting after an exception. Terminating...");
                    return;
                }
            }
        }
    }

    public void EnqueueTokensToDeleteFromDatabaseIfInvalid(IEnumerable<UserPnToken> tokens)
    {
        foreach (var token in tokens)
        {
            _possiblyInvalidTokensQueue.TryAdd(token);
        }
    }

    private async Task RunSingleCleanupCycle(CancellationToken cancellationToken)
    {
        var config = _pushNotificationConfigMonitor.CurrentValue.TokensCleanupConfig;

        var tokens = await ReadTokensFromQueue(config, cancellationToken);
        var invalidTokens = await FilterInvalidTokens(tokens, cancellationToken);

        if (invalidTokens.Count == 0)
        {
            await Task.Delay(config.PauseAfterCleanupIteration, cancellationToken);
            return;
        }

        var success = await _pushNotificationTokenService.DeleteTokens(invalidTokens.Select(t => t.Token));

        if (!success)
        {
            _logger.Error("Failed to delete {InvalidTokensCount} invalid tokens", invalidTokens.Count);
        }
        else
        {
            foreach (var token in invalidTokens)
            {
                _logger.Info("Deleted token {Token} of user {UserId}", token.Token, token.UserId);
            }
        }

        await Task.Delay(config.PauseAfterCleanupIteration, cancellationToken);
    }

    private async Task<List<UserPnToken>> ReadTokensFromQueue(
        PushNotificationTokensCleanupConfig config,
        CancellationToken cancellationToken
    )
    {
        // Blocking call.
        var firstQueueToken = _possiblyInvalidTokensQueue.Take(cancellationToken);
        List<UserPnToken> tokens = [firstQueueToken];

        // A small optimization in here -- if one token was taken from the queue, it is likely that
        // more tokens will be added immediately after via queue.TryAdd. In that case, we can take
        // all (or most) of them from the queue and process them in a single iteration.
        await Task.Delay(config.PauseAfterTakeFromQueue, cancellationToken);

        var tokensInQueueCount = _possiblyInvalidTokensQueue.Count;
        for (int i = 0; i < tokensInQueueCount; i++)
        {
            if (i >= config.MaxTokensToValidateInOneIteration)
            {
                break;
            }

            var queueToken = _possiblyInvalidTokensQueue.Take(cancellationToken);
            tokens.Add(queueToken);
        }

        return tokens;
    }

    private async Task<List<UserPnToken>> FilterInvalidTokens(
        IReadOnlyList<UserPnToken> tokens,
        CancellationToken cancellationToken
    )
    {
        var taskTokenMap = new Dictionary<Task<PushNotificationSendToSingleResult>, UserPnToken>();
        foreach (var token in tokens)
        {
            var task = _pushNotificationSendingService.SendPushNotificationToSingleAsync(
                token.Token,
                _dummyNotification,
                dryRun: true,
                cancellationToken: cancellationToken
            );

            taskTokenMap.Add(task, token);
        }

        var invalidTokens = new List<UserPnToken>();
        while (taskTokenMap.Count > 0)
        {
            var completedTask = await Task.WhenAny(taskTokenMap.Keys);
            var result = await completedTask;

            if (result.TokenIsInvalid)
            {
                invalidTokens.Add(taskTokenMap[completedTask]);
            }

            taskTokenMap.Remove(completedTask);
        }

        return invalidTokens;
    }
}
