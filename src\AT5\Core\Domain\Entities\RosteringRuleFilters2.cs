﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class RosteringRuleFilters2
{
    public RosteringRuleFilters2Id Id { get; set; }

    public Filter2Id FilterId { get; set; }

    public RosteringRuleId RosteringRuleId { get; set; }

    public RosteringRuleFilterCategory Category { get; set; }

    public string Parameters { get; set; } = null!;

    public virtual Filter2 Filter { get; set; } = null!;

    public virtual RosteringRule RosteringRule { get; set; } = null!;
}
