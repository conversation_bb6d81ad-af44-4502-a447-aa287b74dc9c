﻿namespace AT.Primitives.Enums;

using AT.Primitives.Attributes;

public enum PermissionsEnum
{
    Unknown = 0,

    // Roles permissions
    [Classification(PermissionsClassification.Roles)]
    Employee = 1,

    [Classification(PermissionsClassification.Roles)]
    Manager = 2,

    [Classification(PermissionsClassification.Roles)]
    Supervisor = 3,

    [Classification(PermissionsClassification.Roles)]
    Guest = 4,
    SkillsConfiguration = 11,

    OwnTestPasswordSetup = 12,

    // Administration permissions
    AccessToAdministration = 101, // Site comnfiguration parameters view and edit - some entities.
    ApplicationConfigurationEdit = 102, // Global comnfiguration parameters view and edit.
    AdministrateGlobalEntities = 103, // Administration of global entities.
    SectionConfigurationEdit = 104, // Site configuration parameters view and edit.
    EntityItemsViewAndEdit = 105, // Administration of site entities - some entities.

    ImportConfiguration = 110,
    ConfigurationSetup = 111,
    SiteSetup = 112,
    RoleSetup = 113,
    RightsSetup = 114,
    ReportsSetup = 115,
    RosteringRulesSetup = 116,
    UsersAdministration = 117,
    EmployeeEdit = 118,
    Tasks = 119,
    MyTasks = 120,
    SpecialAction_Synchronization = 121,
    UseAdvancedSpecialActions = 122, // Advanced special actions, which are hidden for normal administrators.
    SpecialAction_GdprUsersAnonymization = 123,
    ClientAppsAdministration = 124,
    EmployeeSkillEditInRosterGrid = 125,
    ShopPrimarySectionEdit = 126,

    StatusPartsEdit = 127,

    EmployeeWorkingTimeModelEdit = 128,

    AlbertManagerOPS = 129,
    SiteTypeEdit = 130,
    EmployeeActivityEditInRoster = 131,

    EmployeeView = 132,

    OrganizationStructureView = 133,
    BudgetActivityEdit = 134,

    TeamsEdit = 135,

    // Shiftplanning permissions
    [Classification(PermissionsClassification.Shiftplanning)]
    PlanningPeriodEdit = 201,

    [Classification(PermissionsClassification.Shiftplanning)]
    ShiftPlanning = 202,

    [Classification(PermissionsClassification.Shiftplanning)]
    AccessToShiftPlanning = 203,

    [Classification(PermissionsClassification.Shiftplanning)]
    AccessToEmployeeShiftPlan = 204,

    CalculationsDashboard = 210,

    SimulateOnRosterGrid = 220,

    // Roster grid permissions
    RosterItemsView = 301,

    /// <summary>
    /// View own input and public roster items.
    /// </summary>
    OwnRosterItemsView = 303,
    RequestsView = 304,

    /// <summary>
    /// Allows to view own requests and create requests for self, but only in planning periods open fo requests
    /// and in future, where there are no planning periods yet.
    /// </summary>
    OwnRequestsCreate = 306, // model 29: renamed, meaning changed a little OwnRequestsView -> OwnRequestCreate

    /// <summary>
    /// Allows to administrate rosters - create/rename/delete, select working roster. Not roster items.
    /// </summary>
    RostersEdit = 307, // model 29: now only allows for Rosters edit: create/rename/.. Doesn't allow RosterItems edit.

    /// <summary>
    /// View and edit public roster items except ownt items.
    /// </summary>
    PublicRosterItemsEdit = 308,

    RosterCheck = 311,
    OwnRosterCheck = 313,

    /// <summary>
    /// Allows to process ChangeRequests of other employees, not for self.
    /// </summary>
    ChangeRequestsProcess = 314, // model 29: renamed
    ShiftTrades = 316,
    ViewTeams = 317,
    ViewContracts = 318,
    ViewStatistics = 319,
    SelectedActivitiesView = 320,
    ExportToExcel = 321,

    /// <summary>
    /// Allows to process requests of other employees, but not own.
    /// </summary>
    RequestsProcess = 322,
    RequestsEdit = 324,
    OwnRequestsEdit = 326,
    ItemsHistoryView = 327,
    OwnItemsHistoryView = 329,
    NeedsEdit = 330,
    NeedsView = 331,

    /// <summary>
    /// Allows to pre-process requests of other employees, but not own.
    /// </summary>
    RequestsPreProcess = 334, // model 29: renamed

    /// <summary>
    /// Creation of change request on own roster items.
    /// </summary>
    CreateChangeRequest = 336,
    ManagersNotes = 337,
    ContactCenter = 339,

    /// <summary>
    /// View and edit input roster items except own items.
    /// </summary>
    InputRosterItemsEdit = 341,

    /// <summary>
    /// Allows to pre-process ChangeRequests of other employees, not for self.
    /// </summary>
    ChangeRequestsPreProcess = 342,
    PublicInsertRosterItemPart = 344,
    ChangeRequestsView = 345,
    FreeShifts = 346,
    SimpleNeedsEdit = 347,
    CreateSimpleRequest = 348,

    /// <summary>
    /// Locks roster for all other users, the user who made the lock can still make changes normally.
    /// This is starting to be obsolete and should not be used if not necessary.
    /// </summary>
    TemporarilyLockRosterForOthers = 349,
    AllowUndoOtherUserChanges = 350,

    /// <summary>
    /// Creation of change request on other user's roster items.
    /// </summary>
    CreateOtherChangeRequest = 351,
    ViewSites = 352,
    ViewSkills = 353,
    ViewUsernames = 354,
    ViewPersonalNumber = 355,
    ViewEmail = 356,
    ViewLocations = 357,
    FreeShiftsReservation = 358,
    BatchLockAttendance = 359,
    FinishedRostersEdit = 360,

    BatchUnlockAttendance = 368,

    // New model 29:

    // TODO: check these
    /// <summary>
    /// Allows for creation of shift for current employee through special simple form.
    /// Only allows for the simple edit form - own item edit form is needed to allow the edit itself.
    /// </summary>
    OwnSimpleRosterItemEdit = 370,

    /// <summary>
    /// Allows processing of own requests.
    /// </summary>
    OwnRequestsProcess = 373,

    /// <summary>
    /// Allows pre-processing of own requests.
    /// </summary>
    OwnRequestsPrePreprocess = 374,

    // New model 29 RosterItem rights.

    /// <summary>
    /// View and edit roster items from all rosters except own items.
    /// </summary>
    RosterItemsEdit = 375,

    /// <summary>
    /// View and edit pre-public roster items except ownt items.
    /// </summary>
    PrePublicRosterItemsEdit = 376,

    /// <summary>
    /// View and edit own roster items from all rosters.
    /// </summary>
    OwnRosterItemsEdit = 377,

    /// <summary>
    /// View and edit own roster items from all rosters - edit is controlled by OwnShiftsEditConfiguration configuration.
    /// </summary>
    OwnRosterItemsEditByConfig = 379,

    /// <summary>
    /// View and edit own roster items from pre-public rosters.
    /// </summary>
    OwnPrePublicRosterItemsEdit = 380,

    /// <summary>
    /// View and edit own roster items from pre-public rosters - edit is controlled by OwnShiftsEditConfiguration configuration.
    /// </summary>
    OwnPrePublicRosterItemsEditByConfig = 382,

    /// <summary>
    /// View and edit own roster items from input rosters.
    /// </summary>
    OwnInputRosterItemsEdit = 383,

    /// <summary>
    /// View and edit own roster items from input rosters - edit is controlled by OwnShiftsEditConfiguration configuration.
    /// </summary>
    OwnInputRosterItemsEditByConfig = 385,

    /// <summary>
    /// View and edit own roster items from public rosters.
    /// </summary>
    OwnPublicRosterItemsEdit = 386,

    /// <summary>
    /// View and edit own roster items from public rosters - edit is controlled by OwnShiftsEditConfiguration configuration.
    /// </summary>
    OwnPublicRosterItemsEditByConfig = 388,

    /// <summary>
    /// Edit of date notes in roster grid (per-site).
    /// </summary>
    DateNotesEdit = 390,

    ViewPhoneNumber = 391,

    ImportPrediction = 392,

    /// <summary>
    /// Allows to view and edit Request priority.
    /// </summary>
    RequestPriorityEdit = 393,

    /// <summary>
    /// Allows to view and edit Permissions configuration.
    /// </summary>
    PermissionsConfigurationEdit = 394,

    /// <summary>
    /// Allows to create/edit requests same way the <see cref="OwnRequestsCreate"/> does,
    /// but for other employees.
    /// </summary>
    OthersRequestsCreate = 395,

    /// <summary>
    /// View roster items.
    /// </summary>
    PrePublicRosterItemsView = 396,

    /// <summary>
    /// Batch processing of Requests from the RosterGrid menu.
    /// </summary>
    BatchRequestsProcess = 397,

    /// <summary>
    /// Batch preprocessing of Requests from the RosterGrid menu.
    /// </summary>
    BatchRequestsPreProcess = 398,

    // Email and Notification permissions

    /// <summary>
    /// User gets email that a roster of managed site has been published
    /// </summary>
    EmailRosterPublished = 401,

    /// <summary>
    /// User gets email that a subordinate employee's roster item was changed by an user
    /// </summary>
    RosterItemChangeEmail = 402, // renamed: SubordinateRosterItemChangeEmail -> RosterItemChangeEmail

    /// <summary>
    /// User gets notification that a subordinate employee's roster item was changed by an user
    /// TODO: not used now - should be?
    /// </summary>
    RosterItemChangeNotification = 403, // renamed: SubordinateRosterItemChangeNotification -> RosterItemChangeNotification

    /// <summary>
    /// User gets email that a request of employee in subordinate role should be processes
    /// </summary>
    RequestProcessEmail = 404,

    /// <summary>
    /// User gets notification that a request of employee in subordinate role should be processes
    /// </summary>
    RequestProcessNotification = 405,

    /// <summary>
    /// User gets email that a change request of employee in subordinate role should be processes
    /// </summary>
    ChangeRequestProcessEmail = 406,

    /// <summary>
    /// User gets notification that a change request of employee in subordinate role should be processes
    /// </summary>
    ChangeRequestProcessNotification = 407,

    /// <summary>
    /// Allows user to process requests through email.
    /// (Causes request Approve/Reject links to appear in email to manager.)
    /// </summary>
    RequestProcessingFromEmailAllowed = 412, // model 29: renamed SubordinateRequestProcessingFromEmailAllowed -> RequestProcessingFromEmailAllowed

    /// <summary>
    /// Allows user to process ChangeRequests through email.
    /// (Causes request Approve/Reject links to appear in email to manager.)
    /// </summary>
    ChangeRequestProcessingFromEmailAllowed = 413, // model 29: renamed SubordinateChangeRequestProcessingFromEmailAllowed -> ChangeRequestProcessingFromEmailAllowed

    /// <summary>
    /// Employee with this permissions gets info email about processing and cancelling his/her requests.
    /// </summary>
    OwnRequestInfoEmail = 414,

    /// <summary>
    /// Allows processing of requests through email links without user login.
    /// </summary>
    AllowRequestsEmailProcessingWithoutLogin = 415,

    /// <summary>
    /// Allows processing of change requests through email links without user login.
    /// </summary>
    AllowChangeRequestsEmailProcessingWithoutLogin = 416,

    /// <summary>
    /// Manager with this permission gets info email about processing and cancelling employees requests.
    /// </summary>
    RequestInfoEmail = 417,

    /// <summary>
    /// Triggering a AristoTelos to AristoTelos synchronization/export of Requirements.
    /// </summary>
    [Classification(PermissionsClassification.AtAtSynchronization)]
    AtAtRequirementsSynchronization = 418,

    /// <summary>
    /// Triggering a AristoTelos to AristoTelos synchronization/export of RosterItems.
    /// </summary>
    [Classification(PermissionsClassification.AtAtSynchronization)]
    AtAtRosterItemsSynchronization = 419,

    /// <summary>
    /// Makes the "Solution" column in CalculationsController:Manage visible
    /// </summary>
    ViewCalculationSolution = 420,

    LockAttendance = 421,
    UnlockAttendance = 422,
    WorkSheetView = 423,
    FinishAttendance = 424,
    BatchFinishAttendance = 425,

    OvertimeAdministration = 426,

    /// <summary>
    /// Allows user to view <see cref="WorkOrder"/> on RosterGrid.
    /// </summary>
    WorkOrderView = 427,

    /// <summary>
    /// Allows user to edit <see cref="WorkOrder"/> either on RosterGrid or through administration.
    /// </summary>
    WorkOrderEdit = 428,

    /// <summary>
    /// Allows user to view <see cref="WorkOrderPrediction"/> on RosterGrid.
    /// </summary>
    WorkOrderPredictionView = 429,

    /// <summary>
    /// Allows user to edit <see cref="WorkOrderPrediction"/> either on RosterGrid or through administration.
    /// </summary>
    WorkOrderPredictionEdit = 430,

    RosterPublishConfirmation = 431,
    RosterChangeConfirmation = 432,

    SubstituteActivityAdministration = 433,

    OwnWorkSheetView = 434,

    /// <summary>
    /// Confirmation of own worksheet by an employee - confirming the shifts they had. Not related to attendance finish.
    /// </summary>
    EmployeeWorkSheetConfirmation = 435,
    EmployeeRolesEdit = 436,

    WorkOrderProductivityEdit = 437,
    OperatingHoursEdit = 438,
    AgenciesEdit = 439,
    PreparationWayEdit = 440,

    /// <summary>
    /// Allows the user to change default calculation TimeLimit
    /// </summary>
    SetCalculationTimeLimit = 441,

    EmployeeTeamsEdit = 442,

    EmployeeQueuePropertyAdministration = 443,

    SiteSubtypeEdit = 444,

    // Permissions to view specific types of Needs on RosterGrid. Edit permissions are not split for now.
    Needs_OpeningHoursView = 445,
    Needs_OperatingHoursView = 446,
    Needs_ShiftIntervalView = 447,
    Needs_WorkHoursView = 448,
    Needs_NormalView = 449,
    Needs_AdditionalView = 450,

    // ?? Needs_OtherView = 451,

    FinishAttendanceSingleOnly = 452,

    CashRegisterOccupation = 453,

    ViewVersion = 454,
    ViewDetailedVersion = 455,

    EmployeeQueueProductivityView = 456,

    // Finish attendance even with errors.
    ForceFinishAttendance = 457,

    ImportAlbertDCBonuses = 458,

    // Permissions to allow extended options in calculation controller
    ExtendedViewOptionsForCalculations = 459,
    ExtendedEditOptionsForCalculations = 460,

    RosterPublish = 461,

    CreateAutoShiftsAndPublish = 462,
    CreateAutoShiftsAll = 463,

    // Permission to allow duplicating a calculation
    DuplicateOptionForCalculations = 464,

    RosterPrePublish = 465,

    NeedsCoverageChart = 466,

    FillInAutoShiftsAndPublish = 467,

    CreateAutoShifts = 468,
    FillInAutoShifts = 469,

    ViewEmployeeAutogenerateShifts = 470,
    FreeShiftsOffer = 471,
    OwnFreeShiftsOffer = 472,

    OpenShiftTakenNotification = 473,

    [Classification(PermissionsClassification.Api)]
    API_GenerateReports = 501,

    [Classification(PermissionsClassification.Api)]
    API_UpdateAgentStates = 502,

    [Classification(PermissionsClassification.Api)]
    API_RosterItems_Read = 503,

    [Classification(PermissionsClassification.Api)]
    API_RosterItems_Update = 504,

    [Classification(PermissionsClassification.Api)]
    API_Requirements_Read = 505,

    [Classification(PermissionsClassification.Api)]
    API_Requirements_Update = 506,

    [Classification(PermissionsClassification.Api)]
    API_Activity_Read = 507,

    [Classification(PermissionsClassification.Api)]
    API_TimeOff_Read = 508,

    [Classification(PermissionsClassification.Api)]
    API_Break_Read = 509,

    [Classification(PermissionsClassification.Api)]
    API_ExternalStatuses_Read = 510,

    [Classification(PermissionsClassification.Api)]
    API_Volumes_Update = 511,

    [Classification(PermissionsClassification.Api)]
    API_Volumes_Read = 512, // Not used for now, added as a complement of API_Volumes_Update.

    [Classification(PermissionsClassification.Api)]
    API_AvailabilityCheck = 513,

    [Classification(PermissionsClassification.Api)]
    API_Employees_Update = 515,

    [Classification(PermissionsClassification.Api)]
    API_Employees_Read = 516,

    [Classification(PermissionsClassification.SpecialActions)]
    SpecialAction_StatesCheck = 602,

    [Classification(PermissionsClassification.SpecialActions)]
    SpecialAction_StatesShiftUpdate = 603,

    AgencyHoursRosterGrid = 604,

    ManualO365Synchronization = 605,

    EmployeeStatesView = 606,

    EmployeeEntryDateView = 607,

    /// <summary>
    /// Allow creation and editing of requests limits.
    /// </summary>
    RequestLimitsConfig = 608,

    /// <summary>
    /// Manager permission - allows seeing request limits on roster grid
    /// </summary>
    RequestLimitsView = 609,

    /// <summary>
    /// Employee permission - allows seeing own request limits on roster grid
    /// </summary>
    OwnRequestLimitsView = 610,

    /// <summary>
    /// Permissions that allow editing of shifts
    /// </summary>
    PublicIndividualTypeRosterItemsEdit = 611,
    OwnPublicIndividualTypeRosterItemsEdit = 612,

    PublicRosterItemsView = 613,

    OwnPublicRosterItemsView = 614,

    OwnPrePublicRosterItemsView = 615,

    ViewShiftsCount = 616,

    ViewAllDayTimeOffsCount = 617,

    ViewDelegations = 618,

    EditDelegations = 619,

    CreateOwnDelegations = 620,

    /// <summary>
    /// Allows the user to create requests past the limit of a RequestLimit
    /// </summary>
    RequestLimitsBreach = 621,
    OwnRequestLimitsBreach = 622,

    RequestsAdministrationView = 623,
    RequestsAdministrationSendEmail = 624,
    RequestsAdministrationSendPushNotification = 625,
    RequestsAdministrationProcess = 626,

    [Classification(PermissionsClassification.SpecialActions)]
    SpecialAction_RosterFinishFromProperties = 627,

    /// <summary>
    /// Managers with this permission on employee can edit time preferences of that employee
    /// </summary>
    EditEmployeeTimePreferences = 630,

    /// <summary>
    /// Used for administration of property categories
    /// </summary>
    PropertyCategoryConfig = 631,

    /// <summary>
    /// Used in administration when filtering by ids of entities
    /// </summary>
    FilterByIds = 635,

    /// <summary>
    /// Used for managers to recieve notifications when employee rejects roster item change
    /// </summary>
    RosterItemChangeRejectedNotification = 636,

    /// <summary>
    /// Used for receiving only notifications instead of tasks when roster item is modified
    /// </summary>
    RosterChangeNotification = 637,

    /// <summary>
    /// Used for accessing licensing menu
    /// </summary>
    LicensingAdministration = 638,

    /// <summary>
    /// User with this permission is notified when the calculation they created is finished.
    /// </summary>
    CalculationNotification = 639,

    /// <summary>
    /// When a new employee is created, users with this permission will obtain a task to setup the new employee.
    /// </summary>
    SetupEmployeeTask = 640,

    /// <summary>
    /// Used for Jobs administration.
    /// </summary>
    JobsSetup = 641,

    NeedsCoverageChart2 = 642,

    /// <summary>
    /// Manager permission - allows seeing request limits on roster grid
    /// </summary>
    AutoApproveRequestLimitsView = 643,

    /// <summary>
    /// Employee permission - allows seeing own request limits on roster grid
    /// </summary>
    OwnAutoApproveRequestLimitsView = 644,

    /// <summary>
    /// Employee permission - allows editing phone number on roster grid
    /// </summary>
    PhoneNumberEdit = 645,

    CreatePrediction = 646,

    DirectSubordinateEmployee = 647,

    ViewCalculationFatalErrors = 648,

    ViewDelegationType = 649,

    EditDelegationType = 650,

    CacheInvalidate = 651,

    /// <summary>
    /// Defines beginning of custom permissions block <see cref="CustomPermissionsStart"/> - <see cref="CustomPermissionsEnd"/> where users can create organization-specific permissions.
    /// </summary>
    [Classification(PermissionsClassification.Custom)]
    CustomPermissionsStart = 1000000,

    /// <summary>
    /// Defines the end of custom permissions block <see cref="CustomPermissionsStart"/> - <see cref="CustomPermissionsEnd"/> where users can create organization-specific permissions.
    /// </summary>
    [Classification(PermissionsClassification.Custom)]
    CustomPermissionsEnd = 2000000,
}
