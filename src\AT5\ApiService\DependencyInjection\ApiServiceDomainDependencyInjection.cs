﻿namespace AT.ApiService.DependencyInjection;

using AT.ApiService.Authentication;
using AT.ApiService.Endpoints.Demo;
using AT.Core.Auth;
using Autofac;
using Autofac.Multitenant;

internal static class ApiServiceDomainDependencyInjection
{
    public static ContainerBuilder AddApiServiceDomainServices(this ContainerBuilder container)
    {
        container.RegisterType<UserAuthenticator>().As<IUserAuthenticator>().InstancePerLifetimeScope();

        // Add cities repository. This is just for testing.
        // FUTURE: Remove when possible or only register in certain cases. This is just for demo purposes.
        container.RegisterType<CitiesRepository>().InstancePerTenant();

        return container;
    }
}
