﻿namespace AT.Core.Domain.NotificationMessageAggregates.PushNotificationAggregate;

using AT.Core.Domain.NotificationMessageAggregates;
using Primitives.Enums;

public class PushNotification : IAggregateRoot, INotificationMessage
{
    public long Id { get; set; }

    public required PushNotificationType Type { get; set; }

    public int? EntityId { get; set; }

    public string? EventParameters { get; set; }

    public string Title { get; set; } = null!;

    public string Body { get; set; } = null!;

    public string? ImageUrl { get; set; }

    public required DateTime Generated { get; set; }

    public DateTime? SendTime { get; set; }

    public virtual ICollection<PushNotificationRecipient> PushNotificationRecipients { get; set; } =
        new List<PushNotificationRecipient>();

    public IEnumerable<INotificationMessageRecipient> Recipients => PushNotificationRecipients;
}
