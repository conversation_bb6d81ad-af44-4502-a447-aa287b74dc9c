﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;
using Primitives.Enums;

public class RequestType
{
    public RequestTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Abbreviation { get; set; } = null!;

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public RequestStatus CreateStatus { get; set; }

    public RequestStatuses AvailableStatuses { get; set; }

    public RequestIntervalTypes AvailableIntervalTypes { get; set; }

    public bool LongTerm { get; set; }

    public bool Disabled { get; set; }

    public bool IsGlobal { get; set; }

    public bool ShiftsSelection { get; set; }

    public bool ImplicitAssignment { get; set; }

    public bool AllPeriods { get; set; }

    public string? Parameters { get; set; }

    public PermissionsEnum? ProcessPermission { get; set; }

    public PermissionsEnum? PreProcessPermission { get; set; }

    public PermissionsEnum? CreatePermission { get; set; }

    public bool Anywhere { get; set; }

    public int? RequiredFutureDays { get; set; }

    public bool AllowDateTimeRange { get; set; }

    public bool AllowDateRange { get; set; }

    public virtual ICollection<EmployeeRequestType> EmployeeRequestTypes { get; set; } =
        new List<EmployeeRequestType>();

    public virtual ICollection<RequestTypeFilter> RequestTypeFilters { get; set; } = new List<RequestTypeFilter>();

    public virtual ICollection<Request> Requests { get; set; } = new List<Request>();

    public virtual ICollection<RequestLimit> Limits { get; set; } = new List<RequestLimit>();

    public virtual ICollection<Property> Properties { get; set; } = new List<Property>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
