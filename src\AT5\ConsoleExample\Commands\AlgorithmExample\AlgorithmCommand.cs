namespace AT.ConsoleExample.Commands.AlgorithmExample;

using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Services.Algorithm;
using Divident = Core.Domain.ExampleEntities.Divident;
using Divisor = Core.Domain.ExampleEntities.Divisor;
using UserName = Core.Domain.ExampleEntities.UserName;

[Command("algorithm", Description = "Invokes the algorithm for a given input.")]
public class AlgorithmCommand(IAlgorithmExecutor _executor) : ICommand
{
    [CommandParameter(order: 0, Description = "First argument of the algorithm.")]
    public required Divident Input1 { get; init; }

    [CommandParameter(order: 1, Description = "Second argument of the algorithm.")]
    public required Divisor Input2 { get; init; }

    [CommandOption("user", 'u', Description = "The user who executed the algorithm.")]
    public UserName User { get; init; } = UserName.From("Default");

    public async ValueTask ExecuteAsync(IConsole console)
    {
        await _executor.ExecuteAlgorithmAsync(User, new(Input1, Input2));
    }
}
