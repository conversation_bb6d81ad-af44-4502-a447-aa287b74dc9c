﻿namespace AT.Core.Domain.PushNotificationTokenAggregate.Queries;

public interface IPushNotificationTokenForUsersQuery
{
    /// <returns>Tokens per user. Users without any token are not contained in the resulting dictionary.</returns>
    Task<List<UserPnToken>> GetTokensPerUserId(
        IEnumerable<UserId> userIds,
        CancellationToken cancellationToken = default(CancellationToken)
    );
}
