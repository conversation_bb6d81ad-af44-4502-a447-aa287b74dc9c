﻿namespace AT.Core.Jobs.JobService;

using AT.Core.Jobs.JobService.JobWrapper;
using AT.Primitives.Enums;

public class JobSpecification
{
    public JobType JobType { get; init; }

    public Type JobImplementation { get; init; }

    public Type JobParamsType { get; init; }

    private JobSpecification(JobType jobType, Type jobImplementation, Type jobParamsType)
    {
        JobType = jobType;
        JobImplementation = jobImplementation;
        JobParamsType = jobParamsType;
    }

    public static JobSpecification Create<TJobImplementation, TJobParams>(JobType type)
        where TJobImplementation : class, IATJob<TJobParams>
    {
        return new JobSpecification(type, typeof(TJobImplementation), typeof(TJobParams));
    }
}
