﻿namespace AT.NotificationsService.General.Job;

using AT.Core.Domain.NotificationMessageAggregates;
using AT.Core.SimpleJobs;
using AT.NotificationsService.General.Interfaces;
using AT.Utilities.Collections;
using AT.Utilities.Logging;
using Microsoft.Extensions.Options;

public class ScheduledSendingJob<TMessageType, TMessageTypeRecipient>(
    ILogger<ScheduledSendingJob<TMessageType, TMessageTypeRecipient>> _logger,
    IMessageLogger<TMessageType, TMessageTypeRecipient> _messageLogger,
    IMessageService<TMessageType> _messageService,
    IMessageProvider<TMessageType> _messageProvider,
    IOptions<ScheduledSendingConfig> _config
) : ISimpleJob
    where TMessageType : INotificationMessage
    where TMessageTypeRecipient : INotificationMessageRecipient
{
    // NOTE: Get rid of operation result, let exceptions bubble up.
    public async Task Execute()
    {
        var now = DateTime.Now;
        var sendUntil = now.Add(_config.Value.SendInAdvanceTolerance);
        var deleteUntil = now.Add(-_config.Value.DeleteOnlyThreshold);

        _logger.Info("Loading messages until {SendUntil}...", sendUntil);

        var scheduledMessages = await _messageProvider.GetScheduledMessagesAsync(sendUntil);
        if (scheduledMessages.Count == 0)
        {
            _logger.Info("No scheduled messages were loaded. Terminating the job.");
            return;
        }

        scheduledMessages.ForEach(_messageLogger.LogMessageLoad);

        _logger.Info("Loaded {MessageCount} messages...", scheduledMessages.Count);

        var messagesByDeleteOnlyPredicate = scheduledMessages.SplitInTwo(pn => pn.SendTime < deleteUntil);

        var messagesToDeleteOnly = messagesByDeleteOnlyPredicate.HasFeature;
        var messagesToSend = messagesByDeleteOnlyPredicate.DoesntHaveFeature;

        _logger.Info(
            "Loaded {DeleteOnlyCount}/{TotalCount} messages to delete only.",
            messagesToDeleteOnly.Count,
            scheduledMessages.Count
        );
        _logger.Info(
            "Loaded {SendCount}/{TotalCount} messages to send.",
            messagesToSend.Count,
            scheduledMessages.Count
        );

        if (messagesToSend.Count > 0)
        {
            var sendMessagesResult = await _messageService.SendMessagesAsync(messagesToSend);
            if (!sendMessagesResult.Success)
            {
                _logger.Warn(
                    "SendMessages failed completely for {FailedMessagesCount} messages and for {PartiallyFailedMessagesCount} messages partially.",
                    sendMessagesResult.FailedMessages.Count,
                    sendMessagesResult.PartiallyFailedMessages.Count
                );
            }
        }

        if (messagesToDeleteOnly.Count > 0)
        {
            LogTooOldMessages(_logger, messagesToDeleteOnly, _messageLogger);
        }

        await _messageProvider.MarkAsProcessedAsync(scheduledMessages);

        scheduledMessages.ForEach(_messageLogger.LogMessageProcessed);

        string csvMessageIds = string.Join(", ", scheduledMessages.Select(pn => pn.Id));
        _logger.Info("The following scheduled messages were marked as processed: {MessageIds}", csvMessageIds);
    }

    private static void LogTooOldMessages(
        ILogger<ScheduledSendingJob<TMessageType, TMessageTypeRecipient>> logger,
        IReadOnlyList<TMessageType> tooOldMessages,
        IMessageLogger<TMessageType, TMessageTypeRecipient> messageLogger
    )
    {
        logger.Warn("There are {TooOldMessagesCount} too old messages which will not be sent.", tooOldMessages.Count);

        foreach (var message in tooOldMessages)
        {
            messageLogger.LogMessageExpired(message);
            logger.Warn("The following message is too old and will not be sent: {Message}", message);
        }
    }
}
