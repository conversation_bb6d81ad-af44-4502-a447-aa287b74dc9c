﻿namespace AT.Utilities.Collections;

using System.Collections.Generic;

public readonly struct CollectionKey<T>(IReadOnlyCollection<T> _collection) : IEquatable<CollectionKey<T>>
{
    public IReadOnlyCollection<T> Collection { get; } = _collection;

    public override string ToString()
    {
        if (Collection is null)
        {
            return "CollectionKey<null>";
        }

        var elements = string.Join(", ", Collection.Select(x => x?.ToString() ?? "null"));
        return $"CollectionKey[{elements}]";
    }

    public bool Equals(CollectionKey<T> other)
    {
        if (ReferenceEquals(Collection, other.Collection))
        {
            return true;
        }

        if (Collection.Count != other.Collection.Count)
        {
            return false;
        }

        return Collection.SequenceEqual(other.Collection);
    }

    public override bool Equals(object? obj)
    {
        return obj is CollectionKey<T> other && Equals(other);
    }

    public override int GetHashCode()
    {
        return Collection.Aggregate(17, (acc, val) => HashCode.Combine(acc, val));
    }

    public static bool operator ==(CollectionKey<T> left, CollectionKey<T> right) => left.Equals(right);

    public static bool operator !=(CollectionKey<T> left, CollectionKey<T> right) => !left.Equals(right);
}
