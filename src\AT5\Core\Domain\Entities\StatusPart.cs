﻿namespace AT.Core.Domain.Entities;

public class StatusPart
{
    public StatusPartId Id { get; set; }

    public DateTimeInterval Interval { get; set; }

    public UserId EmployeeId { get; set; }

    public StatusTypeId StatusTypeId { get; set; }

    public QueueId? QueueId { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual Queue? Queue { get; set; }

    public virtual StatusType StatusType { get; set; } = null!;
}
