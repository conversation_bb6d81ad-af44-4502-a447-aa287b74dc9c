﻿namespace AT.NotificationsService;

using AT.Core.MasterDomain;
using AT.Core.SimpleJobs;
using AT.NotificationsService.General;
using AT.NotificationsService.General.Job;
using AT.NotificationsService.NotificationsLimiting.CleanupJob;
using AT.NotificationsService.PushNotifications.Interfaces;
using AT.Utilities.Cron;
using Microsoft.Extensions.Options;

internal sealed class SingleOrganizationWorker(
    ILogger<SingleOrganizationWorker> _logger,
    FullOrgId _fullOrgId,
    ISimpleJobScheduler _jobScheduler,
    InstantSendingLoop<
        PushNotificationConfig,
        MessagesProcessor<PushNotification, PushNotificationRecipient>
    > _pushNotificationsLoop,
    IPushNotificationTokensCleanupService _pushNotificationTokensCleanupService,
    InstantSendingLoop<EmailConfig, MessagesProcessor<EmailMessage, EmailMessageRecipient>> _emailMessagesLoop,
    IOptions<RateLimitingConfig> _rateLimitingConfig
)
{
    public async Task<Task> StartAsync(CancellationToken ct)
    {
        using var _ = _logger.BeginScopeWithProperties(("Organization", _fullOrgId.DatabaseName));

        var pushNotificationsTask = await StartPushNotificationsAsync(ct);
        var emailMessagesTask = await StartEmailMessagesAsync(ct);

        await StartSentNotificationsCleanupJobAsync(ct);

        return Task.WhenAll(pushNotificationsTask, emailMessagesTask);
    }

    public async Task<Task> StartPushNotificationsAsync(CancellationToken ct)
    {
        _logger.Info($"{nameof(StartPushNotificationsAsync)} started");

        _logger.Info("Initiating push notifications job creation...");
        await _jobScheduler.ScheduleJob<ScheduledSendingJob<PushNotification, PushNotificationRecipient>>(
            new CronExpression("0 * * ? * * *"),
            "ScheduledPushNotifications",
            ct
        ); // Every minute at 00 seconds
        _logger.Info("Job push notifications and its trigger successfully added to the scheduler");

        var instantSendingTask = Task.Run(() => _pushNotificationsLoop.RunInstantSendingAsync(ct), ct);

        var tokensCleanupTask = Task.Run(() => _pushNotificationTokensCleanupService.RunAsync(ct), ct);

        _logger.Info($"{nameof(StartPushNotificationsAsync)} finished");

        return Task.WhenAll(instantSendingTask, tokensCleanupTask);
    }

    public async Task<Task> StartEmailMessagesAsync(CancellationToken ct)
    {
        _logger.Info($"{nameof(StartEmailMessagesAsync)} started");

        _logger.Info("Initiating email messages job creation...");
        await _jobScheduler.ScheduleJob<ScheduledSendingJob<EmailMessage, EmailMessageRecipient>>(
            new CronExpression("0 * * ? * * *"),
            "ScheduledEmailMessages",
            ct
        ); // Every minute at 00 seconds
        _logger.Info("Job email messages and its trigger successfully added to the scheduler");

        var instantSendingTask = Task.Run(() => _emailMessagesLoop.RunInstantSendingAsync(ct), ct);

        _logger.Info($"{nameof(StartEmailMessagesAsync)} finished");

        return instantSendingTask;
    }

    /// <summary>
    /// Whenever a message with rate-limiting setup is sent, we store a <see cref="SentNotification" /> record in the database, so that the
    /// next time NotificationsService is requested to send a message, it looks for a corresponding <see cref="SentNotification" /> in order to
    /// determine whether the message has already been sent. When a <see cref="SentNotification" /> is too far in the past,
    /// we want to delete it from the database because it is not relevant anymore, and will not be used ever again. This method schedules
    /// a job that periodically performs the clean-up of such irrelevant <see cref="SentNotification" /> entities.
    /// </summary>
    public async Task StartSentNotificationsCleanupJobAsync(CancellationToken ct = default)
    {
        _logger.Info("Initializing Quartz job for SentNotifications cleanup...");
        await _jobScheduler.ScheduleJob<SentNotificationsCleanupJob>(
            new CronExpression(_rateLimitingConfig.Value.CleanupJobTriggerCronExpression),
            "SentNotificationsCleanupJob",
            ct
        );
        _logger.Info("SentNotifications cleanup job inserted into scheduler.");
    }
}
