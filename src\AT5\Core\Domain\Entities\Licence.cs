﻿namespace AT.Core.Domain.Entities;

using Base;

public class Licence
{
    public LicenceId Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public virtual ICollection<LicencePrice> LicencePrices { get; set; } = new List<LicencePrice>();

    public virtual ICollection<LicenceRule> LicenceRules { get; set; } = new List<LicenceRule>();
}
