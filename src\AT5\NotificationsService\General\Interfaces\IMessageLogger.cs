﻿namespace AT.NotificationsService.General.Interfaces;

using AT.Core.Domain.NotificationMessageAggregates;

public interface IMessageLogger<in TMessageType, in TMessageTypeRecipient> : IDisposable
    where TMessageType : INotificationMessage
    where TMessageTypeRecipient : INotificationMessageRecipient
{
    void LogMessageLoad(TMessageType message);

    void LogMessageProcessed(TMessageType message);

    void LogMessageSendSuccessful(TMessageType message);

    void LogMessageSendSuccessfulForRecipients(TMessageType message, IEnumerable<TMessageTypeRecipient> recipients);

    void LogMessageSendFailed(TMessageType message, string? eventReason = null);

    void LogMessageSendFailedForRecipients(
        TMessageType message,
        IEnumerable<TMessageTypeRecipient> recipients,
        string? eventReason = null
    );

    void LogMessageExpired(TMessageType message);

    void LogMessageFilteredByRateLimiting(TMessageType message);

    void LogMessageRecipientsFilteredByRateLimiting(
        TMessageType message,
        IEnumerable<TMessageTypeRecipient> recipients
    );
}
