﻿namespace AT.Infrastructure.Events.Events;

using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using AT.Primitives.Enums;

public class DataChangeEventConverter : JsonConverter<DataChangeEvent>
{
    public override DataChangeEvent? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        using var document = JsonDocument.ParseValue(ref reader);

        if (
            !document.RootElement.TryGetProperty("Type", out var typeProperty)
            && !document.RootElement.TryGetProperty("type", out typeProperty)
        )
        {
            throw new JsonException("Missing 'Type' property");
        }

        var dataType = typeProperty.ValueKind switch
        {
            JsonValueKind.String => ParseDataChangeTypeFromString(typeProperty),
            JsonValueKind.Number => ParseDataChangeTypeFromNumber(typeProperty),
            _ => throw new JsonException($"Invalid JSON value kind for 'Type': {typeProperty.ValueKind}"),
        };

        var targetType = dataType switch
        {
            DataChangeType.RosterRange => typeof(RosterRangeDataChangeEvent),
            DataChangeType.NeedsRange => typeof(NeedsRangeDataChangeEvent),
            DataChangeType.Configuration => typeof(ConfigurationDataChangeEvent),
            DataChangeType.RosterItemTags => typeof(RosterItemTagsDataChangeEvent),
            _ => throw new JsonException($"Unsupported DataChangeType: {dataType}")
        };

        var json = document.RootElement.GetRawText();
        return (DataChangeEvent?)JsonSerializer.Deserialize(json, targetType, options);
    }

    private static DataChangeType ParseDataChangeTypeFromString(JsonElement typeProperty)
    {
        var typeValue = typeProperty.GetString();
        if (!Enum.TryParse<DataChangeType>(typeValue, ignoreCase: true, out var dataType))
        {
            throw new JsonException($"Unknown DataChangeType string: {typeValue}");
        }

        return dataType;
    }

    private static DataChangeType ParseDataChangeTypeFromNumber(JsonElement typeProperty)
    {
        if (!typeProperty.TryGetInt32(out var intVal))
        {
            throw new JsonException($"Invalid number value for DataChangeType.");
        }

        if (!Enum.IsDefined(typeof(DataChangeType), intVal))
        {
            throw new JsonException($"Unknown DataChangeType number: {intVal}");
        }

        var dataType = (DataChangeType)intVal;
        return dataType;
    }

    public override void Write(Utf8JsonWriter writer, DataChangeEvent value, JsonSerializerOptions options)
    {
        JsonSerializer.Serialize(writer, value, value.GetType(), options);
    }
}
