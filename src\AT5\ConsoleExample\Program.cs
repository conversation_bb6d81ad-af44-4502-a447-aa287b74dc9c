﻿using System.IO.Abstractions;
using AT.ConsoleExample.Commands.AppsettingsExample;
using AT.Core.Domain.ExampleEntities;
using AT.Core.Repositories;
using AT.Core.Services;
using AT.Core.Services.Algorithm;
using AT.Core.Validators;
using AT.Infrastructure.Database;
using AT.Infrastructure.DependencyInjection;
using AT.Infrastructure.DependencyInjection.Extensions;
using AT.Infrastructure.Repositories;
using AT.Infrastructure.Services;
using AT.Infrastructure.Utilities.DependencyInjection;
using AT.Infrastructure.Utilities.DependencyInjection.Extensions;
using AT.Infrastructure.Utilities.EntityFramework;
using AT.PrimitivesAT5.Ids;
using AT.Shared;
using AT.SharedResources.Resources;
using AT.UseCases;
using AT.Utilities.Exceptions;
using AT.Utilities.Logging;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using Autofac.Multitenant;
using CliFx;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

var configuration = new ConfigurationBuilder()
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.local.json", optional: true, reloadOnChange: true)
    .Build();

EntityFrameworkProfiler.InitializeIfDebugging();

await new CliApplicationBuilder()
    .AddCommandsFromThisAssembly()
    .UseTypeActivator(commandTypes =>
    {
        // Build standard multitenant service provider.
        var serviceProviderFactory = new CustomMultitenantAutofacServiceProviderFactory(
            (rootContainer) =>
            {
                var strategy = new DummyMultitenantResolutionStrategy();
                var mtc = new MultitenantContainer(strategy, rootContainer);
                return mtc;
            },
            containerBuilder =>
            {
                // Services added through ServiceCollection extensions.
                var serviceCollection = new ServiceCollection();
                serviceCollection
                    .AddLogger(configuration)
                    .AddTranslator(SharedResourcesResourceManagers.ResourceManagers)
                    // .AddMasterDatabaseServices(configuration)
                    .AddMemoryCache()
                    .AddParsers(configuration)
                    .AddSingleton<IFileSystem>(new FileSystem())
                    .Configure<AppSettingsExampleOptions>(
                        configuration.GetSection(AppSettingsExampleOptions.AppSettingsExample)
                    );

                containerBuilder.Populate(serviceCollection);
                containerBuilder.AddCustomMultitenantServiceProviders();
                containerBuilder.AddCurrentTime();

                // Org DB Access.
                containerBuilder.AddOrgDbContextServicesFromOrganizationDbTemplate(configuration);
                containerBuilder.AddRepositories();
                containerBuilder.AddQueries();

                // Example services
                containerBuilder.RegisterType<Algorithm>().As<IAlgorithm>().InstancePerDependency();
                containerBuilder.RegisterType<AlgorithmExecutor>().As<IAlgorithmExecutor>().InstancePerDependency();
                containerBuilder.RegisterType<LoggingNotifier>().As<INotifier>().InstancePerDependency();
                containerBuilder
                    .RegisterType<AlgorithmLogValidator>()
                    .As<IValidator<AlgorithmLog>>()
                    .InstancePerDependency();

                containerBuilder
                    .RegisterType<UserRepositoryExample>()
                    .As<IUserWriter>()
                    .As<IUserWithNotesReader>()
                    .As<IUserNoteEditor>()
                    .InstancePerDependency();

                // To be reevaluated in US #9514?
                // .AddTransientWithSettings<IAlgorithmLogRepository, FileAlgorithmLogRepository, FileAlgorithmLogRepositorySettings>(configuration)
                // .AddDecoratorWithSettings<IAlgorithmLogRepository, CachingAlgorithmLogRepositoryDecorator, CachingSettings>(configuration)

                // Get lifetime scope for the organization specified in configuration.
                var dbName = configuration.GetSection($"OrgDb").Get<string>()!;
                if (dbName is null)
                {
                    throw new InvalidConfigurationFileException(
                        "Single DbName has to be specified in OrgDb in configuration file in order to run commands."
                    );
                }

                // Just a single tenant is used in this application so we register DbName directly.
                containerBuilder.Register(c => new OrgDbName(dbName)).InstancePerTenant(); // Necessary for Organization DbContext

                // Add CLI commands
                foreach (var commandType in commandTypes)
                {
                    // Do not inject required properties of commands. Commands properties are parameters filled by CliFx.
                    // https://autofac.readthedocs.io/en/latest/register/prop-method-injection.html
                    containerBuilder
                        .RegisterType(commandType)
                        .AsSelf()
                        .InstancePerDependency()
                        .ResolveRequiredCliFxParametersWithNull();
                }
            }
        );

        // FUTURE: We build a service provider, but its validation and validation of configuration is not done, do it.
        var containerBuilder = serviceProviderFactory.CreateBuilder(new ServiceCollection());
        var rootServiceProvider = serviceProviderFactory.CreateServiceProvider(containerBuilder);
        var appServiceProvider = rootServiceProvider.GetRequiredService<AppServiceProvider>();
        var orgLifetimeScope = appServiceProvider.GetTenantLifetimeScope(OrganizationId.Zero);

        #region Prepare DB

        var dbContextFactory = orgLifetimeScope.Resolve<IDbContextFactory<OrganizationDbContext>>();
        using var dbContext = dbContextFactory.CreateDbContext();

        // If told via settings, ensure the DB will be created anew
        if (bool.TryParse(configuration["RecreateOrgDb"], out var recreateOrgDb) && recreateOrgDb)
        {
            dbContext.Database.EnsureDeleted();
        }

        dbContext.Database.EnsureCreated();

        #endregion

        // Commands are resolved in a scope inside a single organization.
        return new AutofacServiceProvider(orgLifetimeScope.BeginLifetimeScope());
    })
    .Build()
    .RunAsync();
