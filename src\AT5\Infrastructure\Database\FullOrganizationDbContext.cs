﻿namespace AT.Infrastructure.Database;

using AT.Infrastructure.Database.Infrastructure;
using AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// This is a variant of <see cref="OrganizationDbContext"/> that also contains additional indexes and can be used for full db creation.
/// </summary>
public class FullOrganizationDbContext(DbContextOptions<OrganizationDbContext> _options)
    : OrganizationDbContext(_options),
        IFullDbContext<OrganizationDbContext>
{
    private readonly AdditionalIndexCollection _indexCollection = new();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        _indexCollection.Clear();

        var context = new GroupModelBuilderContext()
        {
            ModelBuilder = modelBuilder,
            AdditionalIndexCollection = _indexCollection,
        };

        BuildModel(context);
    }

    public IEnumerable<FormattableString> GetAdditionalIndexes()
    {
        return _indexCollection.GetIndexes();
    }
}
