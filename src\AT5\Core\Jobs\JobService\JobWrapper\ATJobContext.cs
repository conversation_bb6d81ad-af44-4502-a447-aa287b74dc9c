﻿namespace AT.Core.Jobs.JobService.JobWrapper;

// FUTURE: Removed AT4 parameters.
// Note that this class implements both IATLegacyJobContext and IATJobContext.
// Legacy job wrapper knows this and unsafely casts it into IATLegacyJobContext to retrieve the legacy parameters.
public record ATJobContext(
    JobId JobId,
    string JobName,
    string JobKey,
    string TriggerKey,
    string[] SuccessEmails,
    string[] ErrorEmails,
    string? AristoTelosWebUrl,
    IFireTimeParameters FireTimeParameters
) : IATLegacyJobContext, IATJobContext;
