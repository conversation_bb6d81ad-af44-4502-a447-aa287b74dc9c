{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"https": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "https://localhost:5001", "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "applicationUrl": "https://localhost:5001", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}