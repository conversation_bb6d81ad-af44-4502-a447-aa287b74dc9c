namespace AT.PrimitivesAT5.Ids;

using AT.PrimitivesAT5.Ids.Interfaces;
using Vogen;

// A class that implements IComparable<T> or IComparable should override comparison operators
#pragma warning disable S1210
// Value Objects can have a method that normalizes (sanitizes) input
#pragma warning disable AddNormalizeInputMethod
// Value Objects can have validation
#pragma warning disable AddValidationMethod

[ValueObject<int>]
public readonly partial struct SiteRelationId : IId
{
    int IId.Id => Value;
}
