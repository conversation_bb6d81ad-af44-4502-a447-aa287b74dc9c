﻿namespace AT.Core.Domain.ApprovalsAggregate;

public class ApprovalTypeFilter : IEntity
{
    public ApprovalTypeFilterId Id { get; set; }

    public Filter2Id FilterId { get; set; }

    public ApprovalTypeId ApprovalTypeId { get; set; }

    public Validity Validity { get; set; }

    public virtual Filter2 Filter { get; set; } = null!;

    public virtual ApprovalType ApprovalType { get; set; } = null!;
}
