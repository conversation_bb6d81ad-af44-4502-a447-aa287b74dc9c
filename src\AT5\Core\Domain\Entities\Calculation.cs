﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;
using AT.Core.Domain.UserAggregate;
using Base;
using Primitives.Enums;

public class Calculation
{
    public CalculationId Id { get; set; }

    public string Name { get; set; } = null!;

    public CalculationStatus Status { get; set; }

    public CalculationSolution Solution { get; set; }

    public PlanningPeriodId PlanningPeriodId { get; set; }

    public string? Description { get; set; }

    public DateTime Submitted { get; set; }

    public DateTime? Started { get; set; }

    public DateTime? Finished { get; set; }

    public string ConfigurationParameters { get; set; } = null!;

    public UserId CreatedById { get; set; }

    public RosterId? InputRosterId { get; set; }

    public RosterId? OutputRosterId { get; set; }

    public CalculationTypeId CalculationTypeId { get; set; }

    public virtual ICollection<CalculationError> CalculationErrors { get; set; } = new List<CalculationError>();

    public virtual ICollection<CalculationLog> CalculationLogs { get; set; } = new List<CalculationLog>();

    public virtual CalculationType CalculationType { get; set; } = null!;

    public virtual ICollection<Change> Changes { get; set; } = new List<Change>();

    public virtual User CreatedBy { get; set; } = null!;

    public virtual Roster? InputRoster { get; set; }

    public virtual Roster? OutputRoster { get; set; }

    public virtual PlanningPeriod PlanningPeriod { get; set; } = null!;

    public virtual ICollection<RemovedNotification> RemovedNotifications { get; set; } =
        new List<RemovedNotification>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
