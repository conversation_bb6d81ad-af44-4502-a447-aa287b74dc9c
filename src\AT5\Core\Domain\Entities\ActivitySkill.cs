﻿namespace AT.Core.Domain.Entities;

public class ActivitySkill
{
    public ActivitySkillId Id { get; set; }

    public SkillId SkillId { get; set; }

    public RosterItemPartTypeId ActivityId { get; set; }

    public int Priority { get; set; }

    public int MinimalLevel { get; set; }

    public string? Parameters { get; set; }

    public virtual ActivityType Activity { get; set; } = null!;

    public virtual Skill Skill { get; set; } = null!;
}
