﻿namespace AT.Infrastructure.Queries;

using System.Threading;
using System.Threading.Tasks;
using AT.Core.Domain.UserAggregate;
using AT.Core.Domain.UserAggregate.Queries;
using AT.Infrastructure.DataAccess;
using Microsoft.EntityFrameworkCore;

public sealed class BasicUserInfoQuery(IDataSource<User> _userDataSource) : IBasicUserInfoQuery
{
    public async Task<BasicUserInfo> GetUserInfoAsync(UserId id, CancellationToken cancellationToken = default)
    {
        var userInfo = await _userDataSource
            .Data.Where(x => x.Id == id)
            .Select(x => new BasicUserInfo(x.Id, x.Username, x.FirstName, x.LastName))
            .FirstOrDefaultAsync(cancellationToken);

        if (userInfo is null)
        {
            throw new ArgumentException($"User with id {id} not found in database.");
        }

        return userInfo;
    }
}
