﻿namespace AT.Utilities.Logging;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog;

public static class LoggerDependencyInjection
{
    /// <summary>
    /// BEWARE: The project that calls this method must contain Serilog sink packages that are used in the configuration.
    /// E.g., if we are using "File" as a serilog output, then the project must have "Serilog.Sinks.File"
    /// nuget package referenced. The project can reference the Infrastructure.Utilities project which contains most of them.
    /// </summary>
    public static IServiceCollection AddLogger(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        // Initializes Serilog's default Logger instance.
        Log.Logger = SerilogLoggerCreator.Create(configuration);

        // AddSerilog extension method uses the default instance.
        serviceCollection.AddLogging(builder => builder.AddSerilog());

        // Register our custom ILogger interface to the wrapper around Microsoft's ILogger.
        serviceCollection.AddSingleton(typeof(ILogger<>), typeof(Logger<>));

        return serviceCollection;
    }
}