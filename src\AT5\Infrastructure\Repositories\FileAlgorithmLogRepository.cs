namespace AT.Infrastructure.Repositories;

using System.Collections.Immutable;
using System.IO.Abstractions;
using AT.Utilities.Logging;
using AT.Utilities.Parsing;
using Core.Domain.ExampleEntities;
using Core.Repositories.Example;
using FluentValidation;
using Microsoft.Extensions.Options;

public class FileAlgorithmLogRepository(
    IOptions<FileAlgorithmLogRepositorySettings> _settings,
    ILogger<FileAlgorithmLogRepository> _logger,
    IValidator<AlgorithmLog> _validator,
    IJsonParser _parser,
    IFileSystem _fileSystem
) : IAlgorithmLogRepository
{
    public async Task<ImmutableList<AlgorithmLog>> GetLogsAsync(CancellationToken token = default)
    {
        _logger.Info("Reading logs from {CacheFilePath}", _settings.Value.CacheFilePath);

        var jsonLogs = _fileSystem.File.Exists(_settings.Value.CacheFilePath)
            ? await _fileSystem.File.ReadAllTextAsync(_settings.Value.CacheFilePath, token)
            : "[]";

        if (!_parser.TryDeserialize<ImmutableList<AlgorithmLog>>(jsonLogs, out var logs) || logs is null)
        {
            throw new FormatException("Incorrect log file format");
        }

        _logger.Info("Found {Count} logs", logs.Count);

        return logs;
    }

    public async Task AddLogAsync(AlgorithmLog log, CancellationToken token = default)
    {
        // Validate the log
        await _validator.ValidateAndThrowAsync(log, token);

        // Get current logs + append the new one
        var logs = (await GetLogsAsync(token)).Add(log);

        // Serialize the current log
        var json = _parser.Serialize(logs);

        // Ensure the directory
        _fileSystem.Directory.CreateDirectory(_fileSystem.Path.GetDirectoryName(_settings.Value.CacheFilePath)!);

        // Rewrite the logs
        await _fileSystem.File.WriteAllTextAsync(_settings.Value.CacheFilePath, json, token);
    }

    public Task ClearAsync(CancellationToken token = default)
    {
        if (_fileSystem.File.Exists(_settings.Value.CacheFilePath))
        {
            _fileSystem.File.Delete(_settings.Value.CacheFilePath);
        }

        return Task.CompletedTask;
    }
}
