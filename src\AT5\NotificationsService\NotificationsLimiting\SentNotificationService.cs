﻿namespace AT.NotificationsService.NotificationsLimiting;

using AT.Core.DataAccess;
using AT.Core.Domain.ConfigurationParameterAggregate.Queries;
using AT.Core.Domain.NotificationMessageAggregates.EmailMessageAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.ConfigurationParameters;
using AT.Core.Domain.NotificationMessageAggregates.SentNotificationAggregate.Specifications;
using AT.Infrastructure.Utilities.Parsing;
using AT.Utilities.Logging;
using AT.Utilities.Parsing;
using NotificationsService.NotificationsLimiting.Interfaces;

internal class SentNotificationService(
    IRepositoryFactory<ISentNotificationRepository> _sentNotificationRepoFactory,
    IGlobalConfigParamQuery _globalConfigParamQuery,
    ILogger<SentNotificationService> _logger,
    SentNotificationsCache _sentNotificationsCache
) : ISentNotificationService
{
    private readonly IJsonParser _jsonSerializer = new SystemTextJsonParser(); // ISSUE: Remove when configuration param loading is replaced by an AT5 service.

    public async Task DeleteAsync(DateTime untilExclusive, CancellationToken cancellationToken = default)
    {
        using var sentNotificationRepository = _sentNotificationRepoFactory.Create();

        var sentNotifications = await sentNotificationRepository.ListAsync(
            new SentNotificationsSpec(untilExclusive),
            cancellationToken
        );

        if (sentNotifications.Count == 0)
        {
            _logger.Info("No SentNotifications to be deleted. Terminating...");
            return;
        }

        foreach (var sentNotification in sentNotifications)
        {
            sentNotificationRepository.AddForDelete(sentNotification);

            _logger.Info("Deleted SentNotification {SentNotification}", sentNotification);
        }

        await sentNotificationRepository.CommitAsync(cancellationToken);

        await _sentNotificationsCache.InvalidateAsync(cancellationToken);
    }

    public async Task<IEnumerable<SentNotification>> GetSentEmailMessagesAsync(
        CancellationToken cancellationToken = default
    )
    {
        return await _sentNotificationsCache.GetSentEmailMessagesAsync(_sentNotificationRepoFactory, cancellationToken);
    }

    public async Task StoreEmailMessagesAsSentNotificationsAsync(
        IReadOnlyDictionary<EmailMessage, IReadOnlyCollection<EmailMessageRecipient>> emailMessages,
        CancellationToken cancellationToken = default
    )
    {
        using var sentNotificationRepository = _sentNotificationRepoFactory.Create();
        var now = DateTime.Now; // FUTURE: Get the time from a time service.

        foreach (var (emailMessage, emailMessageRecipients) in emailMessages)
        {
            var sentNotification = SentNotification.CreateFromEmailMessage(emailMessage, emailMessageRecipients, now);

            sentNotificationRepository.AddForInsert(sentNotification);
        }

        await sentNotificationRepository.CommitAsync(cancellationToken);

        await _sentNotificationsCache.InvalidateAsync(cancellationToken);
    }

    public async Task<NotificationsRateLimitingConfig?> GetRateLimitingConfigAsync(
        CancellationToken cancellationToken = default
    )
    {
        var rateLimitingRawValue = await _globalConfigParamQuery.TryGetValue(
            "NotificationsRateLimiting",
            cancellationToken
        );

        if (string.IsNullOrEmpty(rateLimitingRawValue))
        {
            return null;
        }

        _ = _jsonSerializer.TryDeserialize<NotificationsRateLimitingConfig>(rateLimitingRawValue, out var result);

        return result;
    }
}
