﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.RosterItemAggregate;

public class RosterItemPart
{
    public RosterItemPartId Id { get; set; }

    public DateTimeInterval Interval { get; set; }

    public RosterItemPartTypeId RosterItemPartTypeId { get; set; }

    public RosterItemId RosterItemId { get; set; }

    public MeetingId? MeetingId { get; set; }

    public int Seconds { get; set; }

    public int Order { get; set; }

    public bool? Productive { get; set; }

    public virtual Meeting? Meeting { get; set; }

    public virtual RosterItem RosterItem { get; set; } = null!;

    public virtual RosterItemPartType RosterItemPartType { get; set; } = null!;
}
