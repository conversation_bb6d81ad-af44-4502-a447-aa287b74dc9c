﻿namespace AT.ApiService.Endpoints.Demo;

using AT.Shared.Models.Demo;

public class CitiesRepository
{
    private readonly Lock _repositoryLock = new();

    private int _currentId = 0;
    private readonly Dictionary<int, City> _cities = [];

    public City CreateCity(string name, int population)
    {
        lock (_repositoryLock)
        {
            var city = new City()
            {
                Id = _currentId,
                Name = name,
                Population = population,
            };

            _cities.Add(_currentId, city);
            _currentId++;
            return city;
        }
    }

    public void DeleteCity(int id)
    {
        lock (_repositoryLock)
        {
            _cities.Remove(id);
        }
    }

    public City? GetCity(int id)
    {
        lock (_repositoryLock)
        {
            return _cities.GetValueOrDefault(id);
        }
    }

    public City[] GetCities()
    {
        lock (_repositoryLock)
        {
            return _cities.Select(x => x.Value).ToArray();
        }
    }
}
