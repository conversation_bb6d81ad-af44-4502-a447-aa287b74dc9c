﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.UserAggregate;

public class EmployeeRequestType
{
    public EmployeeRequestTypeId Id { get; set; }

    public UserId EmployeeId { get; set; }

    public RequestTypeId RequestTypeId { get; set; }

    public SiteId SiteId { get; set; }

    public Validity Validity { get; set; }

    public virtual Employee Employee { get; set; } = null!;

    public virtual RequestType RequestType { get; set; } = null!;

    public virtual Site Site { get; set; } = null!;
}
