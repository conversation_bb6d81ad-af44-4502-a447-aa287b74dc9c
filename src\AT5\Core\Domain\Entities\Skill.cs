﻿namespace AT.Core.Domain.Entities;

using Base;

public class Skill
{
    public SkillId Id { get; set; }

    public string? CcId { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public int MinimalLevel { get; set; }

    public bool VisibleInRoster { get; set; }

    public string? Parameters { get; set; }

    public int Rank { get; set; }

    public bool ImplicitAssignment { get; set; }

    public string? Abbreviation { get; set; }

    public bool SynchronizeEmployeeSkills { get; set; }

    public virtual ICollection<ActivitySkill> ActivitySkills { get; set; } = new List<ActivitySkill>();

    public virtual ICollection<BudgetActivity> BudgetActivities { get; set; } = new List<BudgetActivity>();

    public virtual ICollection<EmployeeSkill> EmployeeSkills { get; set; } = new List<EmployeeSkill>();

    public virtual ICollection<Requirement> Requirements { get; set; } = new List<Requirement>();

    public virtual ICollection<Skill> ChildSkills { get; set; } = new List<Skill>();

    public virtual ICollection<Skill> ParentSkills { get; set; } = new List<Skill>();
}
