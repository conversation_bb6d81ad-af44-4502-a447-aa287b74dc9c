﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using AT.Core.Domain.RosterItemAggregate;

public class ShiftTemplate
{
    public ShiftTemplateId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Abbreviation { get; set; } = null!;

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public TimeInterval TimeInterval { get; set; }

    public Recurrence Recurrence { get; set; } = null!;

    public string? ExportCode { get; set; }

    public bool Disabled { get; set; }

    public bool IsGlobal { get; set; }

    public bool ImplicitAssignment { get; set; }

    public bool AutoGenerateBreaks { get; set; }

    public string? Parameters { get; set; }

    public bool ApplyExact { get; set; }

    public virtual ICollection<BreakTemplate> BreakTemplates { get; set; } = new List<BreakTemplate>();

    public virtual ICollection<EmployeeShiftTemplate> EmployeeShiftTemplates { get; set; } =
        new List<EmployeeShiftTemplate>();

    public virtual ICollection<RosterItem> RosterItems { get; set; } = new List<RosterItem>();

    public virtual ICollection<ShiftTemplateFilter> ShiftTemplateFilters { get; set; } =
        new List<ShiftTemplateFilter>();

    public virtual ICollection<WorkingTime> WorkingTimes { get; set; } = new List<WorkingTime>();

    public virtual ICollection<RequestLimit> RequestLimits { get; set; } = new List<RequestLimit>();

    public virtual ICollection<Request> RequestShiftTemplateShiftTemplates { get; set; } = new List<Request>();

    public virtual ICollection<Requirement> Requirements { get; set; } = new List<Requirement>();

    public virtual ICollection<Requirement> RequirementsToSatisfies { get; set; } = new List<Requirement>();

    public virtual ICollection<ShiftSystemDay> ShiftSystemDays { get; set; } = new List<ShiftSystemDay>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
