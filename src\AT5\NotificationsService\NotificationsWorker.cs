namespace AT.NotificationsService;

using System.Collections.Generic;
using System.Threading;
using AT.Core.MasterDomain;
using AT.Core.MasterDomain.Queries;
using AT.Infrastructure.DependencyInjection;
using AT.Utilities.Exceptions;
using AT.Utilities.Logging;
using Microsoft.Extensions.Options;

public class NotificationsWorker(
    ILogger<NotificationsWorker> _logger,
    IOptions<GeneralConfig> _configOptions,
    IFullOrgIdsQuery _fullOrgIdsQuery,
    IAppServiceProvider _appServiceProvider
) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            var organizationIdsToRun = await _fullOrgIdsQuery.GetOrgIdsAsync(stoppingToken);
            var config = _configOptions.Value;

            if (!config.IsMultitenant)
            {
                organizationIdsToRun = organizationIdsToRun.Where(x => x.DatabaseName == config.DbName).ToList();
                if (organizationIdsToRun.Count != 1)
                {
                    throw new InvalidConfigurationFileException(
                        $"Organization with the DatabaseName '{config.DbName}' not identified"
                            + $" in the master database (Count: {organizationIdsToRun.Count})."
                    );
                }
            }

            await RunProcessingForOrganizations(organizationIdsToRun, stoppingToken);

            _logger.Info("Cancellation requested. Terminating...");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"{nameof(ExecuteAsync)} failed with exception");
            throw;
        }
    }

    private async Task RunProcessingForOrganizations(
        IEnumerable<FullOrgId> organizationIds,
        CancellationToken stoppingToken
    )
    {
        List<Task> processingTasks = [];

        foreach (var fullOrgId in organizationIds)
        {
            var orgResolver = _appServiceProvider.GetOrgServiceProvider(fullOrgId.Id);
            var organizationWorker = orgResolver.Resolve<SingleOrganizationWorker>();

            var orgTask = await organizationWorker.StartAsync(stoppingToken);
            processingTasks.Add(orgTask);
        }

        await Task.WhenAll(processingTasks);
    }
}
