﻿namespace AT.Core.Domain.Entities;

using Base;
using Primitives.Enums;

public class Queue
{
    public QueueId Id { get; set; }

    public string? CcId { get; set; }

    public string Name { get; set; } = null!;

    public string? Description { get; set; }

    public bool IsGlobal { get; set; }

    public Language Language { get; set; }

    public CommunicationChannel CommunicationType { get; set; }

    public string? ServiceQualityParameters { get; set; }

    public string? InteractionParameters { get; set; }

    public int MinimalLevel { get; set; }

    public bool Forecast { get; set; }

    public string? ForecastParameters { get; set; }

    public TimeOnly BinSize { get; set; }

    public string? Parameters { get; set; }

    public int Rank { get; set; }

    public string? Abbreviation { get; set; }

    public virtual ICollection<EmployeeQueueProperty> EmployeeQueueProperties { get; set; } =
        new List<EmployeeQueueProperty>();

    public virtual ICollection<PredictionBin> PredictionBins { get; set; } = new List<PredictionBin>();

    public virtual ICollection<QueueProperty> QueueProperties { get; set; } = new List<QueueProperty>();

    public virtual ICollection<RealBin> RealBins { get; set; } = new List<RealBin>();

    public virtual ICollection<StatusPart> StatusParts { get; set; } = new List<StatusPart>();

    public virtual ICollection<WorkMission> WorkMissions { get; set; } = new List<WorkMission>();

    public virtual ICollection<WorkOrderPrediction> WorkOrderPredictions { get; set; } =
        new List<WorkOrderPrediction>();

    public virtual ICollection<WorkOrder> WorkOrders { get; set; } = new List<WorkOrder>();

    public virtual ICollection<Queue> ChildQueues { get; set; } = new List<Queue>();

    public virtual ICollection<Event> Events { get; set; } = new List<Event>();

    public virtual ICollection<Queue> ParentQueues { get; set; } = new List<Queue>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();

    public virtual ICollection<WorkOrderProductivity> WorkOrderProductivities { get; set; } =
        new List<WorkOrderProductivity>();
}
