﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to shifts in the database.
/// </summary>
internal static class ShiftGroupModelBuilder
{
    public static void BuildShiftGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<BreakTemplate>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("BreakTemplates");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasTimeInterval(e => e.TimeInterval, iota);

            entity.HasIndex(e => e.BreakTypeId, "IX_FK_BreakTemplateBreakType");

            entity.HasIndex(e => e.ShiftTemplateId, "IX_FK_ShiftTemplateBreakTemplate");

            entity
                .HasOne(d => d.BreakType)
                .WithMany(p => p.BreakTemplates)
                .HasForeignKey(d => d.BreakTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_BreakTemplateBreakType");

            entity
                .HasOne(d => d.ShiftTemplate)
                .WithMany(p => p.BreakTemplates)
                .HasForeignKey(d => d.ShiftTemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ShiftTemplateBreakTemplate");
        });

        modelBuilder.Entity<ShiftPopularity>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ShiftPopularities");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Type).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasTimeInterval(e => e.TimeInterval, iota);

            entity.Property(e => e.Priority).HasColumnOrder(iota);

            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.SiteId).HasColumnOrder(iota);
            entity.HasIndex(e => e.SiteId, "IX_FK_SiteShiftPopularity");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.ShiftPopularities)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SiteShiftPopularity");
        });

        modelBuilder.Entity<ShiftSystem>(entity =>
        {
            entity.ToTable("ShiftSystems");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity
                .HasMany(d => d.Sites)
                .WithMany(p => p.ShiftSystems)
                .UsingEntity<Dictionary<string, object>>(
                    "ShiftSystemSite",
                    r =>
                        r.HasOne<Site>()
                            .WithMany()
                            .HasForeignKey("SitesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ShiftSystemSite_Site"),
                    l =>
                        l.HasOne<ShiftSystem>()
                            .WithMany()
                            .HasForeignKey("ShiftSystemsId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ShiftSystemSite_ShiftSystem"),
                    j =>
                    {
                        j.HasKey("ShiftSystemsId", "SitesId");
                        j.ToTable("ShiftSystemSite");
                        j.HasIndex(["SitesId"], "IX_FK_ShiftSystemSite_Site");
                        j.IndexerProperty<ShiftSystemId>("ShiftSystemsId").HasColumnName("ShiftSystems_Id");
                        j.IndexerProperty<SiteId>("SitesId").HasColumnName("Sites_Id");
                    }
                );
        });

        modelBuilder.Entity<ShiftSystemDay>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ShiftSystemDays");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Rank).HasColumnOrder(iota);

            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.Parameters).HasMaxLength(4000).HasColumnOrder(iota);

            entity.Property(e => e.ShiftSystemId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ShiftSystemId, "IX_FK_ShiftSystemShiftSystemDays");

            entity.Property(e => e.Text).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.BackgroundColor).HasMaxLength(10).HasColumnOrder(iota);

            entity
                .HasOne(d => d.ShiftSystem)
                .WithMany(p => p.ShiftSystemDays)
                .HasForeignKey(d => d.ShiftSystemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ShiftSystemShiftSystemDays");

            entity
                .HasMany(d => d.ShiftTemplates)
                .WithMany(p => p.ShiftSystemDays)
                .UsingEntity<Dictionary<string, object>>(
                    "ShiftSystemDaysShiftTemplate",
                    r =>
                        r.HasOne<ShiftTemplate>()
                            .WithMany()
                            .HasForeignKey("ShiftTemplatesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ShiftSystemDaysShiftTemplate_ShiftTemplate"),
                    l =>
                        l.HasOne<ShiftSystemDay>()
                            .WithMany()
                            .HasForeignKey("ShiftSystemDaysId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_ShiftSystemDaysShiftTemplate_ShiftSystemDays"),
                    j =>
                    {
                        j.HasKey("ShiftSystemDaysId", "ShiftTemplatesId");
                        j.ToTable("ShiftSystemDaysShiftTemplate");
                        j.HasIndex(["ShiftTemplatesId"], "IX_FK_ShiftSystemDaysShiftTemplate_ShiftTemplate");
                        j.IndexerProperty<ShiftSystemDayId>("ShiftSystemDaysId").HasColumnName("ShiftSystemDays_Id");
                        j.IndexerProperty<ShiftTemplateId>("ShiftTemplatesId").HasColumnName("ShiftTemplates_Id");
                    }
                );
        });

        modelBuilder.Entity<ShiftTemplate>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ShiftTemplates");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.Name).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.Abbreviation).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Description).HasMaxLength(255).HasColumnOrder(iota);

            entity.HasAppearance(e => e.Appearance, iota);
            entity.HasTimeInterval(e => e.TimeInterval, iota);
            entity.HasRecurrence(e => e.Recurrence, iota);

            entity.Property(e => e.ExportCode).HasMaxLength(10).HasColumnOrder(iota);
            entity.Property(e => e.Disabled).HasColumnOrder(iota);
            entity.Property(e => e.IsGlobal).HasColumnOrder(iota);
            entity.Property(e => e.ImplicitAssignment).HasColumnOrder(iota);
            entity.Property(e => e.AutoGenerateBreaks).HasColumnOrder(iota);
            entity.Property(e => e.Parameters).HasMaxLength(2048).HasColumnOrder(iota);
            entity.Property(e => e.ApplyExact).HasColumnOrder(iota);
        });

        modelBuilder.Entity<ShiftTemplateFilter>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("ShiftTemplateFilters");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.FilterId).HasColumnOrder(iota);
            entity.HasIndex(e => e.FilterId, "IX_FK_ShiftTemplateFilterFilter2");

            entity.Property(e => e.ShiftTemplateId).HasColumnOrder(iota);
            entity.HasIndex(e => e.ShiftTemplateId, "IX_FK_ShiftTemplateShiftTemplateFilter");

            entity.HasValidity(e => e.Validity, iota);

            entity.Property(e => e.Parameters).HasMaxLength(256).HasColumnOrder(iota);

            entity
                .HasOne(d => d.Filter)
                .WithMany(p => p.ShiftTemplateFilters)
                .HasForeignKey(d => d.FilterId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ShiftTemplateFilterFilter2");

            entity
                .HasOne(d => d.ShiftTemplate)
                .WithMany(p => p.ShiftTemplateFilters)
                .HasForeignKey(d => d.ShiftTemplateId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ShiftTemplateShiftTemplateFilter");
        });
    }
}
