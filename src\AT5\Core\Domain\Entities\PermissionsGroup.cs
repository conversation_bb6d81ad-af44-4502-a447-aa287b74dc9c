﻿namespace AT.Core.Domain.Entities;

using Base;

public class PermissionsGroup
{
    public PermissionsGroupId Id { get; set; }

    public RoleId RoleId { get; set; }

    public virtual Role Role { get; set; } = null!;

    public virtual ICollection<Role> ExcludedRoles { get; set; } = new List<Role>();

    public virtual ICollection<Permission> PermissionsTypes { get; set; } = new List<Permission>();

    public virtual ICollection<Role> SubordinateRoles { get; set; } = new List<Role>();
}
