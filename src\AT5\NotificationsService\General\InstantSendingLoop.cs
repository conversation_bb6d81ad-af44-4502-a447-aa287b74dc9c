﻿namespace AT.NotificationsService.General;

using System;
using AT.Infrastructure.DependencyInjection;
using AT.NotificationsService.General.Interfaces;
using AT.Utilities.Reflection;
using Microsoft.Extensions.Options;

// Could be general loop runner - each run has a new in-org scope.
public class InstantSendingLoop<TMessageTypeConfig, TMessageProcessor>(
    ILogger<InstantSendingLoop<TMessageTypeConfig, TMessageProcessor>> _logger,
    IOptionsMonitor<TMessageTypeConfig> _configMonitor,
    IOrgServiceProvider _orgServiceProvider
)
    where TMessageTypeConfig : IHasInstantSendingConfig
    where TMessageProcessor : class, IMessageProcessor
{
    public async Task RunInstantSendingAsync(CancellationToken cancellationToken)
    {
        _logger.Info($"Initiating instant message sending loop {typeof(TMessageProcessor).GetShortName()} ...");

        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                var instantSendingConfig = _configMonitor.CurrentValue.InstantSending;
                var pauseBetweenBatchLoads = instantSendingConfig!.PauseBetweenMessageLoads;
                var batchSize = instantSendingConfig!.MaxMessagesToLoadAtOnce;

                using var scopedResolver = _orgServiceProvider.BeginScope();
                var messageProcessor = scopedResolver.Resolve<TMessageProcessor>();

                int messagesProcessed = await messageProcessor.ProcessMessages(batchSize, cancellationToken);

                if (messagesProcessed == 0)
                {
                    await Task.Delay(pauseBetweenBatchLoads, cancellationToken);
                }
            }
            catch (TaskCanceledException)
            {
                _logger.Info("Cancellation requested. Terminating...");
                return;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Received an exception");
                _logger.Info("Entering wait-time after an exception before continuing the loop...");

                try
                {
                    var instantSendingConfig = _configMonitor.CurrentValue.InstantSending;
                    await Task.Delay(instantSendingConfig.PauseAfterException, cancellationToken);
                    _logger.Info("Wait-time after an exception completed.");
                }
                catch (TaskCanceledException)
                {
                    _logger.Info("Cancellation requested while waiting after an exception. Terminating...");
                    return;
                }
            }
        }

        _logger.Info($"Instant message sending finished {nameof(TMessageProcessor)}.");
    }
}
