﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

internal class DummyAdditionalIndexCollection : IAdditionalIndexCollection
{
    public void Clear() { }

    public void CreateFixedIndexBuilder(string indexCreationCommand) { }

    public IEnumerable<FormattableString> GetIndexes()
    {
        return [];
    }

    public ITypedAdditionalIndexBuilder<T> GetOrCreateIndexBuilder<T>(
        EntityTypeBuilder<T> entityTypeBuilder,
        Expression<Func<T, object?>> indexExpression,
        string name
    )
        where T : class
    {
        return new DummyAdditionalIndexBuilder<T>();
    }

    public ITypedAdditionalIndexBuilder<T> GetOrCreateIndexBuilder<T>(
        EntityTypeBuilder<T> entityTypeBuilder,
        string[] propertyNames,
        string name
    )
        where T : class
    {
        return new DummyAdditionalIndexBuilder<T>();
    }
}
