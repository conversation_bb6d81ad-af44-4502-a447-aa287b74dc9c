﻿namespace AT.Core.Domain.Entities;

using Base;

public class RequestTypeNewShift
{
    public RosterItemPartTypeId ActivityId { get; set; }

    public RosterItemPartTypeId? ParallelActivityId { get; set; }

    public RosterItemPartTypeId? LabelActivityId { get; set; }

    public RosterItemPartTypeId? OvertimeActivityId { get; set; }

    public RequestTypeId Id { get; set; }

    public virtual ActivityType Activity { get; set; } = null!;

    public virtual RequestType RequestType { get; set; } = null!;

    public virtual ActivityType? ParallelActivity { get; set; }

    public virtual ActivityType? LabelActivity { get; set; }

    public virtual ActivityType? OvertimeActivity { get; set; }
}
