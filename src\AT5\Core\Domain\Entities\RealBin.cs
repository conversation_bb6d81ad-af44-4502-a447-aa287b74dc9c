﻿namespace AT.Core.Domain.Entities;

using Base;

public class RealBin
{
    public long Ticks { get; set; }

    public double Offered { get; set; }

    public double? Handled { get; set; }

    public double? QueuedOver { get; set; }

    public double? Abandoned { get; set; }

    public double? AbandonedOver { get; set; }

    public double? ServiceTimeMu { get; set; }

    public QueueId QueueId { get; set; }

    public double? AbandonedShort { get; set; }

    public virtual Queue Queue { get; set; } = null!;
}
