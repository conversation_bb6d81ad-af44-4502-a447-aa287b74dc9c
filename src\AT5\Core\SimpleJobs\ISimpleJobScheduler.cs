﻿namespace AT.Core.SimpleJobs;

using AT.Utilities.Cron;

/// <summary>
/// Scheduler for scheduling of simple repeated jobs (not by a User user of AristoTelos).
/// Each job runs in a new own dependency injection subscope of an organization (the same way an HTTP request has its own subscope).
/// Example usages: in NotificationsService we know the types of jobs we want to run and how often we need to run them.
///
/// In future could be used for triggering one-off jobs (triggering one run of a job), when adding such TriggerJob methods see AT4 jobs scheduler.
/// </summary>
public interface ISimpleJobScheduler
{
    Task ScheduleJob<TJob>(CronExpression cron, string jobName, CancellationToken ct)
        where TJob : class, ISimpleJob;
}
