﻿namespace AT.Infrastructure.Database.Infrastructure.AdditionalIndexes;

using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class AdditionalIndexCollection : IAdditionalIndexCollection
{
    private readonly List<IAdditionalIndexBuilder> _unnamedBuilders = new();
    private readonly Dictionary<string, IAdditionalIndexBuilder> _namedBuilders = new();

    public void Clear()
    {
        _unnamedBuilders.Clear();
        _namedBuilders.Clear();
    }

    public void CreateFixedIndexBuilder(string indexCreationCommand)
    {
        var builder = new FixedIndexBuilder(indexCreationCommand);
        _unnamedBuilders.Add(builder);
    }

    public ITypedAdditionalIndexBuilder<T> GetOrCreateIndexBuilder<T>(
        EntityTypeBuilder<T> entityTypeBuilder,
        Expression<Func<T, object?>> indexExpression,
        string name
    )
        where T : class
    {
        if (!_namedBuilders.TryGetValue(name, out var builder))
        {
            builder = new AdditionalIndexBuilder<T>(name, entityTypeBuilder).HasKeyColumns(indexExpression);
            _namedBuilders.Add(name, builder);
        }

        return (ITypedAdditionalIndexBuilder<T>)builder;
    }

    public ITypedAdditionalIndexBuilder<T> GetOrCreateIndexBuilder<T>(
        EntityTypeBuilder<T> entityTypeBuilder,
        string[] propertyNames,
        string name
    )
        where T : class
    {
        if (!_namedBuilders.TryGetValue(name, out var builder))
        {
            builder = new AdditionalIndexBuilder<T>(name, entityTypeBuilder).HasKeyColumns(propertyNames);
            _namedBuilders.Add(name, builder);
        }

        return (ITypedAdditionalIndexBuilder<T>)builder;
    }

    public IEnumerable<FormattableString> GetIndexes()
    {
        foreach (var builder in _unnamedBuilders.Concat(_namedBuilders.Values))
        {
            var index = builder.Build();
            yield return FormattableStringFactory.Create(index);
        }
    }
}
