﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Base;
using AT.Core.Domain.Entities;
using AT.Core.Domain.UserAggregate;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

/// <summary>
/// Provides methods for configuring entity models related to <see cref="Note"/> in the database.
/// </summary>
internal static class NoteGroupModelBuilder
{
    public static void BuildNoteGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Note>(entity =>
        {
            entity.ToTable("Notes");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.AuthorId, "IX_FK_NoteUser");

            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Text).HasMaxLength(1024);

            entity
                .HasOne(d => d.Author)
                .WithMany(p => p.Notes)
                .HasForeignKey(d => d.AuthorId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_NoteUser");
        });

        modelBuilder.Entity<DateNote>(entity =>
        {
            entity.ToTable("Notes_DateNote");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.Date).HasColumnOrder(0).IsStoredAsDateTime();
            entity.Property(e => e.SiteId).HasColumnOrder(1);
            entity.Property(e => e.Id).HasColumnOrder(2);

            entity.HasIndex(e => e.SiteId, "IX_FK_DateNoteSite");

            entity
                .HasOne(d => d.Note)
                .WithOne()
                .HasForeignKey<DateNote>(d => d.Id)
                .HasConstraintName("FK_DateNote_inherits_Note");

            entity
                .HasOne(d => d.Site)
                .WithMany(p => p.DateNotes)
                .HasForeignKey(d => d.SiteId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_DateNoteSite");
        });

        modelBuilder.Entity<EmployeeNote>(entity =>
        {
            entity.ToTable("Notes_EmployeeNote");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.EmployeeId).HasColumnOrder(1);
            entity.Property(e => e.Id).HasColumnOrder(2);

            entity.HasIndex(e => e.EmployeeId, "IX_FK_EmployeeNoteEmployee");

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.EmployeeNotes)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_EmployeeNoteEmployee");

            entity
                .HasOne(d => d.Note)
                .WithOne()
                .HasForeignKey<EmployeeNote>(d => d.Id)
                .HasConstraintName("FK_EmployeeNote_inherits_Note");
        });

        modelBuilder.Entity<RosterItemNote>(entity =>
        {
            entity.ToTable("Notes_RosterItemNote");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.RosterItemId).HasColumnOrder(0);
            entity.Property(e => e.Id).HasColumnOrder(1);

            entity.HasIndex(e => e.RosterItemId, "IX_FK_RosterItemNoteRosterItem");

            entity
                .HasOne(d => d.Note)
                .WithOne()
                .HasForeignKey<RosterItemNote>(d => d.Id)
                .HasConstraintName("FK_RosterItemNote_inherits_Note");

            entity
                .HasOne(d => d.RosterItem)
                .WithMany(p => p.RosterItemNotes)
                .HasForeignKey(d => d.RosterItemId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterItemNoteRosterItem");
        });

        modelBuilder.Entity<RosterNote>(entity =>
        {
            entity.ToTable("Notes_RosterNote");
            entity.HasKey(e => e.Id);

            entity.Property(e => e.EmployeeId).HasColumnOrder(0);
            entity.Property(e => e.Date).HasColumnOrder(1);
            entity.Property(e => e.Id).HasColumnOrder(2);

            entity.HasIndex(e => e.EmployeeId, "IX_FK_RosterNoteEmployee");

            entity.Property(e => e.Date).IsStoredAsDateTime();

            entity
                .HasOne(d => d.Employee)
                .WithMany(p => p.RosterNotes)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RosterNoteEmployee");

            entity
                .HasOne(d => d.Note)
                .WithOne()
                .HasForeignKey<RosterNote>(d => d.Id)
                .HasConstraintName("FK_RosterNote_inherits_Note");
        });

        modelBuilder.Entity<TaskTodo>(entity =>
        {
            entity.ToTable("Tasks");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.ChangedById, "IX_FK_UserTaskChanged");

            entity.HasIndex(e => e.CreatedById, "IX_FK_UserTaskCreated");

            entity.Property(e => e.Changed).IsStoredAsDateTime();
            entity.Property(e => e.Created).IsStoredAsDateTime();
            entity.Property(e => e.Description).HasMaxLength(3000);
            entity.Property(e => e.DueDate).IsStoredAsDateTime();
            entity.Property(e => e.Name).HasMaxLength(255);
            entity.Property(e => e.Parameters).HasMaxLength(4000);

            entity
                .HasOne(d => d.ChangedBy)
                .WithMany(p => p.TaskChangedBies)
                .HasForeignKey(d => d.ChangedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserTaskChanged");

            entity
                .HasOne(d => d.CreatedBy)
                .WithMany(p => p.TaskCreatedBies)
                .HasForeignKey(d => d.CreatedById)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserTaskCreated");

            entity
                .HasMany(d => d.Employees)
                .WithMany(p => p.Tasks)
                .UsingEntity<Dictionary<string, object>>(
                    "TaskEmployee",
                    r =>
                        r.HasOne<Employee>()
                            .WithMany()
                            .HasForeignKey("EmployeesId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_TaskEmployee_Employee"),
                    l =>
                        l.HasOne<TaskTodo>()
                            .WithMany()
                            .HasForeignKey("TasksId")
                            .OnDelete(DeleteBehavior.ClientSetNull)
                            .HasConstraintName("FK_TaskEmployee_Task"),
                    j =>
                    {
                        j.HasKey("TasksId", "EmployeesId");
                        j.ToTable("TaskEmployee");
                        j.HasIndex(["EmployeesId"], "IX_FK_TaskEmployee_Employee");
                        j.IndexerProperty<TaskTodoId>("TasksId").HasColumnName("Tasks_Id");
                        j.IndexerProperty<UserId>("EmployeesId").HasColumnName("Employees_Id");
                    }
                );
        });
    }
}
