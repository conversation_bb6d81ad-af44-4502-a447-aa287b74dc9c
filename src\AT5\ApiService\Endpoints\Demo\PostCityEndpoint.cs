﻿namespace AT.ApiService.Endpoints.Demo;

using System.Threading;
using System.Threading.Tasks;
using AT.Shared.Models.Demo;
using FastEndpoints;

public sealed class PostCityEndpoint(CitiesRepository _citiesRepository) : Ep.Req<PostCityRequest>.Res<PostCityResponse>
{
    public override void Configure()
    {
        Post("/demo/cities");
    }

    public override async Task HandleAsync(PostCityRequest req, CancellationToken ct)
    {
        var city = _citiesRepository.CreateCity(req.Name, req.Population);
        await SendCreatedAtAsync<GetCityEndpoint>(
            routeValues: new { CityId = city.Id },
            new PostCityResponse() { City = city, },
            cancellation: ct
        );
    }
}
