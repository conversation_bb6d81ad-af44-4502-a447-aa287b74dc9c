﻿namespace AT.NotificationsService.PushNotifications.Interfaces;

using System.Collections.Generic;
using AT.Core.Domain.PushNotificationTokenAggregate;

/// <summary>
/// Responsible for deleting invalid push notification tokens from the database.
/// </summary>
public interface IPushNotificationTokensCleanupService
{
    /// <summary>
    /// Runs a loop that loads tokens from the internal queue, tests their validity and deletes them if invalid until cancellation
    /// on the <paramref name="cancellationToken"/> is called.
    /// </summary>
    Task RunAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Inserts <paramref name="tokens"/> to the internal queue that the <see cref="RunAsync"/> method consumes.
    /// </summary>
    void EnqueueTokensToDeleteFromDatabaseIfInvalid(IEnumerable<UserPnToken> tokens);
}
