﻿namespace AT.ApiService.Endpoints;

using System.Threading;
using System.Threading.Tasks;
using AT.Shared.Models;
using FastEndpoints;

public class CitiesEndpoint : EndpointWithoutRequest<CitiesResponse>
{
    private static readonly string[] s_cityNames =
    [
        "Paris",
        "Berlin",
        "Prague",
        "Lisbon",
        "Stockholm",
        "London",
        "Oslo"
    ];

    public override void Configure()
    {
        Get("/api/cities");
    }

    public override async Task HandleAsync(CancellationToken ct)
    {
        var cities = Enumerable
            .Range(1, 5)
            .Select(index => new City(s_cityNames[Random.Shared.Next(s_cityNames.Length)]))
            .ToArray();

        await SendAsync(new CitiesResponse() { Cities = cities, }, cancellation: ct);
    }
}
