﻿namespace AT.Infrastructure.Database.GroupBuilders;

using AT.Core.Domain.Entities;
using AT.Infrastructure.Database.Infrastructure;
using Microsoft.EntityFrameworkCore;

internal static class JobGroupModelBuilder
{
    public static void BuildConfigurationGroup(GroupModelBuilderContext context)
    {
        var modelBuilder = context.ModelBuilder;

        modelBuilder.Entity<Job>(entity =>
        {
            entity.ToTable("Jobs");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.ErrorEmails).HasMaxLength(512);
            entity.Property(e => e.Name).HasMaxLength(128);
            entity.Property(e => e.SuccessEmails).HasMaxLength(512);
        });

        modelBuilder.Entity<JobLogEntry>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("JobLogEntries");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.JobRunId).HasColumnOrder(iota);
            entity.Property(e => e.Timestamp).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Level).HasColumnOrder(iota);
            entity.Property(e => e.MessageId).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.MessageTranslated).HasMaxLength(4000).HasColumnOrder(iota);
            entity.Property(e => e.MessageParameters).HasMaxLength(4000).HasColumnOrder(iota);

            entity.HasIndex(e => e.JobRunId, "IX_FK_JobRunJobLogEntry");

            entity
                .HasOne(d => d.JobRun)
                .WithMany()
                .HasForeignKey(d => d.JobRunId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobRunJobLogEntry");
        });

        modelBuilder.Entity<JobRun>(entity =>
        {
            var iota = new Iota();

            entity.ToTable("JobRuns");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).HasColumnOrder(iota).IsRequired();

            entity.Property(e => e.JobId).HasColumnOrder(iota);
            entity.Property(e => e.Triggered).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Started).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Finished).IsStoredAsDateTime().HasColumnOrder(iota);
            entity.Property(e => e.Result).HasColumnOrder(iota);
            entity.Property(e => e.AuthorId).HasColumnOrder(iota);
            entity.Property(e => e.JobMethod).HasMaxLength(255).HasColumnOrder(iota);
            entity.Property(e => e.MethodParameters).HasMaxLength(4000).HasColumnOrder(iota);

            entity.HasIndex(e => e.JobId, "IX_FK_JobRunJob");

            entity
                .HasOne(d => d.Job)
                .WithMany()
                .HasForeignKey(d => d.JobId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobRunJob");
        });

        modelBuilder.Entity<JobTrigger>(entity =>
        {
            entity.ToTable("JobTriggers");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).UseIdentityColumn(1).IsRequired();

            entity.HasIndex(e => e.JobId, "IX_FK_JobTriggerJob");

            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.JobId).HasColumnName("Job_Id");
            entity.Property(e => e.Name).HasMaxLength(128);
            entity.Property(e => e.RunParameters).HasMaxLength(4000);

            entity
                .HasOne(d => d.Job)
                .WithMany(p => p.JobTriggers)
                .HasForeignKey(d => d.JobId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_JobTriggerJob");
        });
    }
}
