﻿namespace AT.Infrastructure.Jobs.DependencyInjection;

using AT.Core.Jobs.DurableJobs;
using AT.Core.Jobs.JobService;
using AT.Core.Jobs.JobService.EmailsService;
using AT.Infrastructure.Jobs.DurableJobs;
using Autofac;
using Autofac.Multitenant;

public static class JobServiceModule
{
    public static ContainerBuilder AddJobService(this ContainerBuilder builder)
    {
        // Requires AddQuartzCustom to be invoked on ServiceCollection.
        builder.RegisterType<JobEmailsServiceFactory>().As<IJobEmailsServiceFactory>();
        builder.RegisterType<JobServiceBuilder>().As<IJobServiceBuilder>().InstancePerTenant();
        builder.RegisterType<DurableJobSeriesScheduler>().As<IDurableJobSeriesScheduler>().InstancePerTenant();

        return builder;
    }
}
