﻿namespace AT.Core.Domain.Entities;

using Base;

public class WorkMissionType
{
    public WorkMissionTypeId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public string? Color { get; set; }

    public string? Parameters { get; set; }

    public double BonusCoefficient { get; set; }

    public bool IsGlobal { get; set; }

    public virtual ICollection<WorkMission> WorkMissions { get; set; } = new List<WorkMission>();

    public virtual ICollection<Site> Sites { get; set; } = new List<Site>();
}
