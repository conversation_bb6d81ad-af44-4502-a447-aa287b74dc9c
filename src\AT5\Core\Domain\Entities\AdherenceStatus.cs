﻿namespace AT.Core.Domain.Entities;

using AT.Core.Domain.ComplexTypes;
using Base;

public class AdherenceStatus
{
    public AdherenceStatusId Id { get; set; }

    public string Name { get; set; } = null!;

    public string Abbreviation { get; set; } = null!;

    public string? Description { get; set; }

    public Appearance Appearance { get; set; } = null!;

    public string? Icon { get; set; }

    public virtual ICollection<Adherence> Adherences { get; set; } = new List<Adherence>();
}
