namespace AT.ConsoleExample.Commands.AlgorithmExample;

using CliFx;
using CliFx.Attributes;
using CliFx.Infrastructure;
using Core.Repositories.Example;

[Command("algorithm clear", Description = "Clears the algorithm cache")]
public class ClearCommand(IAlgorithmLogRepository _repository) : ICommand
{
    public async ValueTask ExecuteAsync(IConsole console)
    {
        await _repository.ClearAsync();
    }
}
