﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<WarningsAsErrors>Nullable</WarningsAsErrors>
		<Platforms>x64</Platforms>
		<RootNamespace>AT.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\PrimitivesAT5\PrimitivesAT5.csproj" />
		<ProjectReference Include="..\Primitives\Primitives.csproj" />
		<ProjectReference Include="..\DataStructures\DataStructures.csproj" />
		<ProjectReference Include="..\Utilities\Utilities.csproj" />
	</ItemGroup>

	<ItemGroup>
		<!-- Azure monitor -->
		<PackageReference Include="Azure.Monitor.OpenTelemetry.Exporter" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Process" />
		<PackageReference Include="OpenTelemetry.Instrumentation.SqlClient" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Runtime" />
		<PackageReference Include="System.Diagnostics.DiagnosticSource" />
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.Common" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" />

		<!-- Serilog sinks -->
		<PackageReference Include="Serilog.Sinks.Console" />
		<PackageReference Include="Serilog.Sinks.File" />

		<!-- Other -->
		<PackageReference Include="Autofac.Multitenant" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" />
		<PackageReference Include="EntityFrameworkProfiler" />
		<PackageReference Include="CliFx" />
		<PackageReference Include="Spectre.Console" />
		<PackageReference Include="ZCS.DataContractResolver" />
	</ItemGroup>

</Project>
